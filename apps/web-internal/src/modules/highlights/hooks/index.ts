import { useCallback, useContext, useEffect, useMemo, useState } from 'react'

import { useApolloClient, useQuery } from '@apollo/client'
import { DateTime } from 'luxon'

import { OrderBy } from '@apella/component-library'
import { useQueryParamAndLocalStorageState } from '@apella/hooks'
import {
  dateTimeToUrlFriendlyDate,
  orderByToUrlFriendly,
  urlFriendlyDateToJSDate,
  urlFriendlyOrderStrToOrderBy,
} from 'src/components/molecules/Filters/urls'
import { LocaleContext } from 'src/contexts/locale'
import {
  GetHighlightSearchData,
  GetHighlightSearchDataVariables,
  GetHighlightSearchFilters,
} from 'src/modules/highlights/queries/__generated__'
import {
  GET_HIGHLIGHT_SEARCH_DATA,
  GET_HIGHLIGHT_SEARCH_FILTER,
} from 'src/modules/highlights/queries/search'

type GetHighlightSearchData_highlightSearch_edges =
  GetHighlightSearchData['highlightSearch']['edges'][number]
type GetHighlightSearchData_highlightSearch_edges_node =
  GetHighlightSearchData_highlightSearch_edges['node']
type GetHighlightSearchFilters_organizations_edges =
  GetHighlightSearchFilters['organizations']['edges'][number]

interface HighlightCategory {
  name: string
  type:
    | 'first_case_on_time_starts'
    | 'pre_operative_gap'
    | 'post_operative_gap'
    | 'terminal_clean'
    | 'timeout'
    | 'turnover'
    | 'uncategorized'
}

export const useHighlightCategories = () => {
  return [
    { type: 'first_case_on_time_starts', name: 'First case on time starts' },
    { type: 'pre_operative_gap', name: 'Pre-operative gap' },
    { type: 'post_operative_gap', name: 'Post-operative gap' },
    { type: 'snippet', name: 'Snippet' },
    { type: 'terminal_clean', name: 'Terminal clean' },
    { type: 'timeout', name: 'Timeout' },
    { type: 'turnover', name: 'Turnover' },
    { type: 'uncategorized', name: 'Uncategorized' },
  ] as HighlightCategory[]
}

const START_TIME = 'startTime'
const END_TIME = 'endTime'
const ORG_IDS = 'orgIds'
const SITE_IDS = 'siteIds'
const ROOM_IDS = 'roomIds'
const CATEGORIES = 'categories'
const ASSIGNED_USER_IDS = 'assignedUserIds'
const ORDER_BY = 'orderBy'

type Setter<T> = (
  value: T | ((val: T | undefined) => T | undefined) | undefined
) => void

interface StateActionMapper {
  after: undefined
  endTime: (time: string) => void
  first: undefined
  organizationIds: Setter<string[]>
  siteIds: Setter<string[]>
  startTime: (time: string) => void
}

export const useHighlightSearch = ({
  DEFAULT_START_TIME,
  DEFAULT_END_TIME,
  SORTABLE_FIELDS,
  DEFAULT_STATE,
  DEFAULT_ORDER_BY_STATE,
}: {
  DEFAULT_START_TIME: DateTime
  DEFAULT_END_TIME: DateTime
  SORTABLE_FIELDS: string[]
  DEFAULT_STATE: Partial<GetHighlightSearchDataVariables>
  DEFAULT_ORDER_BY_STATE: Pick<GetHighlightSearchDataVariables, typeof ORDER_BY>
}) => {
  const locale = useContext(LocaleContext)

  const [organizationIds, setOrganizationIds] =
    useQueryParamAndLocalStorageState<string[] | undefined>(ORG_IDS, undefined)

  const [roomIds, setRoomIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(ROOM_IDS, undefined)

  const [assignedUserIds, setAssignedUserIds] =
    useQueryParamAndLocalStorageState<string[] | undefined>(
      ASSIGNED_USER_IDS,
      undefined
    )
  const [categories, setCategories] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(CATEGORIES, undefined)

  const [orderBy, setOrderBy] = useQueryParamAndLocalStorageState<
    string | undefined
  >(ORDER_BY, orderByToUrlFriendly(DEFAULT_STATE.orderBy!))

  const [siteIds, setSiteIds] = useQueryParamAndLocalStorageState<
    string[] | undefined
  >(SITE_IDS, undefined)

  const [startTime, setStartTime] =
    useQueryParamAndLocalStorageState<ApellaDateTime>(
      START_TIME,
      dateTimeToUrlFriendlyDate(DEFAULT_START_TIME)
    )
  const [endTime, setEndTime] =
    useQueryParamAndLocalStorageState<ApellaDateTime>(
      END_TIME,
      dateTimeToUrlFriendlyDate(DEFAULT_END_TIME)
    )

  const stateActionMapper: StateActionMapper = useMemo(
    () => ({
      startTime: (time: string) =>
        setStartTime(dateTimeToUrlFriendlyDate(DateTime.fromISO(time))),
      endTime: (time: string) =>
        setEndTime(dateTimeToUrlFriendlyDate(DateTime.fromISO(time))),
      organizationIds: setOrganizationIds,
      siteIds: setSiteIds,
      roomIds: setRoomIds,
      assignedUserIds: setAssignedUserIds,
      categories: setCategories,
      orderBy: (val: OrderBy[]) => setOrderBy(orderByToUrlFriendly(val)),
      first: undefined,
      after: undefined,
    }),
    [
      setStartTime,
      setEndTime,
      setOrganizationIds,
      setSiteIds,
      setRoomIds,
      setAssignedUserIds,
      setCategories,
      setOrderBy,
    ]
  )

  const [filterData, setFilterData] = useState<
    GetHighlightSearchFilters_organizations_edges[] | undefined
  >(undefined)

  const [state, setState] = useState<GetHighlightSearchDataVariables>({
    ...DEFAULT_STATE,
    startTime:
      urlFriendlyDateToJSDate(startTime, locale) || DEFAULT_START_TIME.toISO(),
    endTime:
      urlFriendlyDateToJSDate(endTime, locale) || DEFAULT_END_TIME.toISO(),
    organizationIds: organizationIds,
    assignedUserIds: assignedUserIds,
    roomIds: roomIds,
    siteIds: siteIds,
    categories: categories,
    orderBy:
      urlFriendlyOrderStrToOrderBy(orderBy, SORTABLE_FIELDS) ||
      DEFAULT_ORDER_BY_STATE.orderBy,
  })

  const highlightCategories = useHighlightCategories()

  const client = useApolloClient()

  const { loading: highlightsLoading, data: highlightsData } = useQuery<
    GetHighlightSearchData,
    GetHighlightSearchDataVariables
  >(GET_HIGHLIGHT_SEARCH_DATA, {
    variables: state,
    fetchPolicy: 'no-cache',
  })

  const flattenHighlight = (
    edge: GetHighlightSearchData_highlightSearch_edges
  ) => {
    const ids = Array.from(
      new Set([
        ...edge.node.feedback.edges.map((f) => f.node.user?.id).filter(Boolean),
        ...edge.node.users.edges.map((u) => u.node.id),
      ])
    )

    if (!ids.length || ids.length < 2) {
      return edge.node
    }

    return ids.map<GetHighlightSearchData_highlightSearch_edges_node>((id) => {
      const feedback = edge.node.feedback.edges.find(
        (feedback) => feedback.node.user?.id === id
      )

      const user = feedback?.node.user
        ? feedback.node.user
        : edge.node.users.edges
            .map(({ node }) => node)
            .find((user) => user.id === id)

      return {
        ...edge.node,
        feedback: {
          ...edge.node.feedback,
          edges: feedback ? [feedback] : [],
        },
        users: {
          ...edge.node.users,
          edges: user ? [{ node: user, __typename: 'UserEdge' }] : [],
        },
      }
    })
  }

  const fetchExportRows = useCallback(async () => {
    const { data } = await client.query<GetHighlightSearchData>({
      query: GET_HIGHLIGHT_SEARCH_DATA,
      variables: { ...state, first: undefined, after: undefined },
      fetchPolicy: 'no-cache',
    })

    return data.highlightSearch?.edges.flatMap(flattenHighlight)
  }, [client, state])

  const { loading: filtersLoading, data: filters } =
    useQuery<GetHighlightSearchFilters>(GET_HIGHLIGHT_SEARCH_FILTER)

  useEffect(() => {
    if (!filtersLoading && filters) {
      const filteredData = filters.organizations.edges.map((o) => ({
        ...o,
        node: {
          ...o.node,
          sites: {
            ...o.node.sites,
            edges: o.node.sites.edges
              .filter(
                () => !organizationIds || organizationIds.includes(o.node.id)
              )
              .map((site) => ({
                ...site,
                node: {
                  ...site.node,
                  rooms: {
                    ...site.node.rooms,
                    edges: site.node.rooms.edges.filter(
                      () => !siteIds || siteIds.includes(site.node.id)
                    ),
                  },
                },
              })),
          },
          users: {
            ...o.node.users,
            edges: o.node.users.edges.filter(
              () => !organizationIds || organizationIds.includes(o.node.id)
            ),
          },
        },
      }))

      setFilterData(filteredData)
    }
  }, [filtersLoading, filters, organizationIds, siteIds])

  // This goes through all the state changes:
  // - Update LocalStorage and QueryParams
  // - Update the state
  const updateTableFilterSortState = useCallback(
    (stateDiff: Partial<GetHighlightSearchDataVariables>) => {
      setState((prevState: GetHighlightSearchDataVariables) => {
        const newState: GetHighlightSearchDataVariables = {
          ...prevState,
          ...stateDiff,
        }

        for (const key of Object.keys(stateActionMapper)) {
          const setter = (stateActionMapper as any)[key]
          const newVal = (newState as any)[key]

          setter && setter(newVal)
        }

        return newState
      })
    },
    [stateActionMapper, setState]
  )

  const toIds = (ids?: string | string[]) => {
    if (Array.isArray(ids)) {
      return ids.length ? ids : undefined
    }

    return ids ? [ids] : undefined
  }

  const onOrganizationIdChange = useCallback(
    (organizationIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          organizationIds: toIds(organizationIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onCategoryChange = useCallback(
    (categories?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          categories: toIds(categories),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onSiteIdsChange = useCallback(
    (siteIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          siteIds: toIds(siteIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )
  const onRoomIdsChange = useCallback(
    (roomIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          roomIds: toIds(roomIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onAssignedUsersIdsChange = useCallback(
    (userIds?: string | string[]) => {
      if (!filtersLoading) {
        updateTableFilterSortState({
          assignedUserIds: toIds(userIds),
        })
      }
    },
    [filtersLoading, updateTableFilterSortState]
  )

  const onSortChange = useCallback(
    (orderBy?: OrderBy[]) => {
      updateTableFilterSortState({
        orderBy,
      })
    },
    [updateTableFilterSortState]
  )

  const onDateChange = useCallback(
    (dates: [Date, Date]) => {
      if (!filtersLoading) {
        const newstartTime = DateTime.fromJSDate(dates[0]).setZone(locale)
        const newendTime = DateTime.fromJSDate(dates[1]).setZone(locale)

        updateTableFilterSortState({
          startTime: newstartTime.toISO(),
          endTime: newendTime.toISO(),
        })
      }
    },
    [filtersLoading, locale, updateTableFilterSortState]
  )

  const resetFilters = useCallback(() => {
    updateTableFilterSortState({
      ...DEFAULT_STATE,
      startTime: DEFAULT_START_TIME.toISO(),
      endTime: DEFAULT_END_TIME.toISO(),
    })
  }, [
    DEFAULT_END_TIME,
    DEFAULT_STATE,
    DEFAULT_START_TIME,
    updateTableFilterSortState,
  ])

  const rowKeySelector = useCallback(
    (rowData: GetHighlightSearchData_highlightSearch_edges_node) => rowData.id,
    []
  )

  const tableData = useMemo(
    () =>
      (highlightsData?.highlightSearch?.edges ?? []).map(
        (highlight) => highlight.node
      ),
    [highlightsData?.highlightSearch]
  )

  const selectedDateTime: [Date, Date] = useMemo(
    () => [new Date(state.startTime), new Date(state.endTime)],
    [state.startTime, state.endTime]
  )

  const pageCursors = highlightsData?.highlightSearch?.pageCursors

  return {
    selectedDateTime,
    pageCursors,
    tableData,
    rowKeySelector,
    resetFilters,
    filterData,
    highlightCategories,
    highlightsLoading,
    onOrganizationIdChange,
    onCategoryChange,
    onSiteIdsChange,
    onRoomIdsChange,
    onAssignedUsersIdsChange,
    onSortChange,
    onDateChange,
    state,
    filtersLoading,
    updateTableFilterSortState,
    fetchExportRows,
  }
}
