{"name": "web-internal", "version": "1.0.0", "type": "module", "description": "Apella Internal Web App", "main": "src/index.tsx", "license": "UNLICENSED", "private": true, "browserslist": ["last 2 chrome versions", "last 2 safari versions", "last 2 firefox versions", "last 2 edge versions"], "scripts": {"build": "NODE_ENV=production vite build", "clean": "yarn graphql:clean && rm -rf dist node_modules tsconfig.tsbuildinfo vite.config.ts.timestamp-*.mjs", "circular-dependency:check": "madge --extensions ts,tsx --circular --exclude '^../../../libs/' src/", "orphan:check": "madge --exclude '(.stories.tsx$|.spec.ts$|__generated__|^global.d.ts$|^windowTypes.ts$|.test.ts$)' --orphans src/", "dev": "concurrently --names \"server,graphql\" -c \"bgBlue.bold,bgYellow.bold\" \"vite serve --open\" \"yarn graphql:gen:watch\" --kill-others", "dev:local": "export APELLA_ENV=localhost; concurrently --names \"server,graphql\" -c \"bgBlue.bold,bgYellow.bold\" \"vite serve --open\" \"yarn graphql:gen:local --watch\" --kill-others", "graphql:clean": "find ./src -name '__generated__' -type d -exec rm -rf {} +", "graphql:gen": "graphql-codegen --config codegen.ts", "graphql:gen:watch": "yarn graphql:gen --watch", "graphql:gen:local": "yarn graphql:gen --localSchemaFile=./schema.graphql", "lint": "eslint --max-warnings=0 ./src && prettier --check ./src", "format": "eslint --cache --max-warnings=0 --fix ./src && prettier --cache --write ./src", "test": "vitest run", "test:watch": "vitest watch", "tsc": "tsc --build", "sourcemaps:sentry": "yarn sourcemaps:release && yarn sourcemaps:upload && yarn sourcemaps:commits", "sourcemaps:commits": "sentry-cli releases set-commits \"$SENTRY_RELEASE\" --auto", "sourcemaps:release": "sentry-cli releases new $SENTRY_RELEASE --finalize", "sourcemaps:upload": "sentry-cli releases files $SENTRY_RELEASE upload-sourcemaps ./dist/"}, "nx": {"targets": {"build": {"dependsOn": ["tsc", "graphql:gen"]}, "dev": {"dependsOn": ["graphql:gen"]}, "dev:local": {"dependsOn": ["graphql:gen:local"]}, "graphql:gen": {"cache": true, "inputs": ["default"]}, "test": {"dependsOn": ["graphql:gen"]}, "tsc": {"dependsOn": ["^build", "graphql:gen"], "inputs": ["default", "generated", "^default"]}, "lint": {"dependsOn": ["tsc"]}}}, "devDependencies": {"@apella/eslint-config-custom": "*", "@emotion/babel-plugin": "^11.13.5", "@graphql-codegen/cli": "5.0.6", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@parcel/watcher": "^2.5.1", "@sentry/cli": "^2.45.0", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/d3-scale": "^4.0.9", "@types/lodash.debounce": "^4.0.7", "@types/luxon": "^2.0.4", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-linkify": "^1.0.1", "@vitejs/plugin-react": "^4.4.1", "concurrently": "^9.1.2", "eslint": "^9.27.0", "jsdom": "^26.1.0", "madge": "^8.0.0", "prettier": "^3.5.3", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3", "vite-plugin-html": "^3.2.2", "vitest": "^3.1.4"}, "dependencies": {"@amplitude/analytics-browser": "^2.17.6", "@apella/component-library": "*", "@apella/hooks": "*", "@apella/logger": "*", "@apollo/client": "^3.13.8", "@auth0/auth0-react": "^2.3.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@floating-ui/react": "^0.27.9", "@sentry/browser": "^9.22.0", "@sentry/react": "^9.22.0", "d3-scale": "^4.0.2", "debounce": "^2.2.0", "emotion-reset": "^3.0.1", "formik": "^2.4.6", "graphql": "^16.11.0", "launchdarkly-react-client-sdk": "^3.7.0", "lodash": "^4.17.21", "luxon": "^3.6.1", "polished": "^4.3.1", "qs": "^6.13.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-linkify": "^1.0.0-alpha", "react-router": "^7.6.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "scroll-into-view-if-needed": "^3.0.4", "yup": "^1.6.1"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "overrides": {"react-is": "^19.0.0"}}