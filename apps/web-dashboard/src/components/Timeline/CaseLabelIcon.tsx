import { useTheme } from '@emotion/react'

import { rem } from 'polished'

import { fontWeights, shape } from '@apella/component-library'

import { useTextColor } from '../../utils/useTextColor'

export const CaseLabelIcon = ({
  color,
  abbreviation,
  selected = true,
  size = 'md',
}: {
  color?: string
  abbreviation?: string
  selected?: boolean
  size?: 'sm' | 'md'
}) => {
  const theme = useTheme()
  const getTextColor = useTextColor()

  return (
    <div
      css={{
        backgroundColor: selected ? color : theme.palette.gray[30],
        color: selected ? getTextColor(color) : theme.palette.text.primary,
        padding: `${size === 'md' ? rem('2px') : rem('1px')} ${rem('4px')}`,
        borderRadius: shape.borderRadius.xxsmall,
        fontSize: size === 'md' ? rem('12px') : rem('10px'),
        lineHeight: 'normal',
        opacity: selected ? 1 : 0.7,
        ...fontWeights.semibold,
      }}
    >
      <div css={{ minWidth: '1em', textAlign: 'center' }}>{abbreviation}</div>
    </div>
  )
}
