import { forwardRef, useCallback, useContext, useMemo } from 'react'

import { gql, useQuery } from '@apollo/client'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  OnFragmentLoadedProps,
  OnLevelLoadedProps,
  OnVideoWatchedProps,
  VideoPlayer,
  VideoPlayerProps,
  VideoPlayerRef,
  VideoPlayerVideoProps,
} from '@apella/component-library'
import { useApellaAuth0 } from '@apella/hooks'
import { ShowVideoContext } from 'src/Contexts'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { usePageContext } from 'src/router/components/PageContext'
import { settings } from 'src/settings'
import { EVENTS, useAnalyticsEventLogger } from 'src/utils/analyticsEvents'

import {
  GetCloudMediaPlayerData,
  GetCloudMediaPlayerDataVariables,
} from './__generated__'

interface CloudMediaPlayerParams
  extends Omit<
    VideoPlayerProps,
    | 'apiDomain'
    | 'videos'
    | 'timezone'
    | 'speeds'
    | 'useNewDirectMediaPlaylistEndpoint'
  > {
  addtlLoggingData?: Record<string, any>
  endTime: DateTime
  isPrivacyEnabled?: boolean
  onReady?: () => void
  roomId: string
  startTime: DateTime
}

const GET_CLOUD_MEDIA_PLAYER_DATA = gql`
  query GetCloudMediaPlayerData($roomId: String!, $blurVideoPlayer: Boolean!) {
    room(id: $roomId) {
      id
      name
      defaultCamera {
        id
      }
      cameras {
        edges {
          node {
            id
            roomId
            siteId
            organizationId
            patientBoundingBox @include(if: $blurVideoPlayer) {
              leftPct
              bottomPct
              widthPct
              heightPct
            }
          }
        }
      }
      site {
        timezone
      }
    }
  }
`

const VIDEO_SKIP_OPTIONS = [1, 10]

export const useVideoPlayerTiming = (videoEndTime: DateTime) => {
  return useMemo(() => {
    const isLive = videoEndTime.diffNow('seconds').as('seconds') > 0
    const isHistorical = !isLive
    const daysInThePast = Math.ceil(
      DateTime.now()
        .startOf('day')
        .diff(videoEndTime.startOf('day'), 'days')
        .as('days')
    )

    return {
      daysInThePast,
      isHistorical,
      isLive,
    }
  }, [videoEndTime])
}

export const CloudMediaPlayer = forwardRef<
  VideoPlayerRef,
  CloudMediaPlayerParams
>(
  (
    {
      roomId,
      startTime,
      endTime,
      onReady,
      isPrivacyEnabled,
      addtlLoggingData,
      ...videoPlayerProps
    },
    ref
  ) => {
    const { getAccessTokenSilentlyOrLogout } = useApellaAuth0()
    const showVideoContext = useContext(ShowVideoContext)
    const {
      blurVideoPlayer,
      useNewDirectMediaPlaylistEndpoint,
      videoPlayerSpeeds,
    } = useFlags<WebDashboardFeatureFlagSet>()

    const speeds = [0.5, 1, 2].concat(videoPlayerSpeeds || [])

    const eventsLogger = useAnalyticsEventLogger()

    const { daysInThePast, isHistorical, isLive } =
      useVideoPlayerTiming(endTime)

    const { data, loading } = useQuery<
      GetCloudMediaPlayerData,
      GetCloudMediaPlayerDataVariables
    >(GET_CLOUD_MEDIA_PLAYER_DATA, {
      variables: {
        roomId: roomId,
        blurVideoPlayer,
      },
    })
    const pageSourceName = usePageContext().trackingPathName
    const defaultCamera = data?.room?.defaultCamera?.id
    const timezone = data?.room?.site.timezone ?? ''
    const videos: VideoPlayerVideoProps[] | undefined =
      data?.room?.cameras.edges.map((camera, index) => ({
        organizationId: camera.node.organizationId,
        siteId: camera.node.siteId,
        roomId: camera.node.roomId,
        cameraId: camera.node.id,
        cameraName: `${index + 1}`,
        patientBoundingBox: camera.node.patientBoundingBox ?? undefined,
        startTime: startTime,
        endTime: endTime,
      })) ?? []

    const baseEventInformation = useMemo(
      () => ({
        daysInThePast,
        isHistorical,
        isLive,
        pageSourceName,
        roomId,
        ...(addtlLoggingData ?? {}),
      }),
      [
        daysInThePast,
        isHistorical,
        isLive,
        pageSourceName,
        roomId,
        addtlLoggingData,
      ]
    )

    const videoPrivacyLevel = useMemo(() => {
      if (isPrivacyEnabled && isLive) {
        return 'BLURRED_IMAGE'
      } else if (blurVideoPlayer) {
        return 'BLUR_CENTER'
      }

      return !showVideoContext ? 'IMAGE' : undefined
    }, [blurVideoPlayer, showVideoContext, isPrivacyEnabled, isLive])

    const onVideoWatched = useCallback(
      (videoWatched: OnVideoWatchedProps) => {
        eventsLogger(EVENTS.WATCH_VIDEO, {
          amountOfVideoWatched: videoWatched.amountOfVideoWatched.as('seconds'),
          timeSpentWatchingVideo:
            videoWatched.timeSpentWatchingVideo.as('seconds'),
          ...baseEventInformation,
        })
      },
      [baseEventInformation, eventsLogger]
    )

    const onChangeCamera = useCallback(
      (cameraId: string) => {
        if (videoPlayerProps.onChangeCamera !== undefined) {
          videoPlayerProps.onChangeCamera(cameraId)
        }

        eventsLogger(EVENTS.CHANGE_VIDEO_CAMERA, {
          cameraId,
          ...baseEventInformation,
        })
      },
      [baseEventInformation, eventsLogger, videoPlayerProps]
    )

    const onChangeSpeed = useCallback(
      (speed: number) => {
        if (videoPlayerProps.onChangeSpeed !== undefined) {
          videoPlayerProps.onChangeSpeed(speed)
        }
        eventsLogger(EVENTS.CHANGE_VIDEO_SPEED, {
          speed,
          ...baseEventInformation,
        })
      },
      [baseEventInformation, eventsLogger, videoPlayerProps]
    )

    const onChangeFullscreen = useCallback(
      (isFullscreen: boolean) => {
        if (videoPlayerProps.onChangeFullscreen !== undefined) {
          videoPlayerProps.onChangeFullscreen(isFullscreen)
        }
        if (isFullscreen === true) {
          eventsLogger(EVENTS.ENTER_VIDEO_FULLSCREEN, baseEventInformation)
        } else {
          eventsLogger(EVENTS.EXIT_VIDEO_FULLSCREEN, baseEventInformation)
        }
      },
      [baseEventInformation, eventsLogger, videoPlayerProps]
    )

    const onFragmentLoaded = useCallback(
      (fragmentLoaded: OnFragmentLoadedProps) => {
        eventsLogger(EVENTS.LOADED_FRAGMENT, {
          ...fragmentLoaded,
          ...baseEventInformation,
        })
      },
      [baseEventInformation, eventsLogger]
    )

    const onLevelLoaded = useCallback(
      (levelLoaded: OnLevelLoadedProps) => {
        eventsLogger(EVENTS.LOADED_HLS_LEVEL, {
          ...levelLoaded,
          ...baseEventInformation,
        })
      },
      [baseEventInformation, eventsLogger]
    )

    const onPause = useCallback(() => {
      if (videoPlayerProps.onPause !== undefined) {
        videoPlayerProps.onPause()
      }

      eventsLogger(EVENTS.PAUSE_VIDEO, baseEventInformation)
    }, [baseEventInformation, eventsLogger, videoPlayerProps])

    const onPlay = useCallback(() => {
      if (videoPlayerProps.onPlay !== undefined) {
        videoPlayerProps.onPlay()
      }

      eventsLogger(EVENTS.PLAY_VIDEO, baseEventInformation)
    }, [baseEventInformation, eventsLogger, videoPlayerProps])

    const onSeek = useCallback(
      (seconds: number) => {
        if (videoPlayerProps.onSeek !== undefined) {
          videoPlayerProps.onSeek(seconds)
        }
        eventsLogger(EVENTS.SEEK_VIDEO, baseEventInformation)
      },
      [baseEventInformation, eventsLogger, videoPlayerProps]
    )

    const onVideoReady = () => {
      if (onReady) {
        onReady()
      }
    }

    const getAuthenticationHeader = async () => {
      return 'Bearer ' + (await getAccessTokenSilentlyOrLogout())
    }

    return (
      <VideoPlayer
        apiDomain={settings.api.domain}
        defaultActiveCamera={defaultCamera}
        videos={videos}
        timezone={timezone}
        getHttpAuthHeader={getAuthenticationHeader}
        ref={ref}
        showLoading={loading}
        videoPrivacyLevel={videoPrivacyLevel}
        skipOptions={VIDEO_SKIP_OPTIONS}
        speeds={speeds}
        onChangeCamera={onChangeCamera}
        onChangeSpeed={onChangeSpeed}
        onChangeFullscreen={onChangeFullscreen}
        onFragmentLoaded={onFragmentLoaded}
        onLevelLoaded={onLevelLoaded}
        onPause={onPause}
        onPlay={onPlay}
        onReady={onVideoReady}
        onSeek={onSeek}
        onWatched={onVideoWatched}
        useNewDirectMediaPlaylistEndpoint={useNewDirectMediaPlaylistEndpoint}
        {...videoPlayerProps}
      />
    )
  }
)

CloudMediaPlayer.displayName = 'CloudMediaPlayer'
