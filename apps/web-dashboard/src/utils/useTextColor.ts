import { useTheme } from '@emotion/react'

export const useTextColor = () => {
  const theme = useTheme()
  return (backgroundColor?: string) => {
    if (!backgroundColor) {
      return theme.palette.text.primary
    }

    let r, g, b

    // HEX format
    if (backgroundColor.startsWith('#')) {
      const hex = backgroundColor.replace('#', '')
      const fullHex =
        hex.length === 3
          ? hex
              .split('')
              .map((c) => c + c)
              .join('')
          : hex

      r = parseInt(fullHex.substring(0, 2), 16)
      g = parseInt(fullHex.substring(2, 4), 16)
      b = parseInt(fullHex.substring(4, 6), 16)
    }

    // RGB or RGBA format
    else if (backgroundColor.startsWith('rgb')) {
      const rgbValues = backgroundColor.match(/\d+/g)?.map(Number)
      ;[r, g, b] = rgbValues
    }

    // Fallback
    else {
      throw new Error(`Unsupported color format: ${backgroundColor}`)
    }

    // Calculate brightness (luminance approximation)
    const luminance = 0.299 * r + 0.587 * g + 0.114 * b

    // Decide based on brightness
    return luminance > 186
      ? theme.palette.text.primary
      : theme.palette.text.alternate
  }
}
