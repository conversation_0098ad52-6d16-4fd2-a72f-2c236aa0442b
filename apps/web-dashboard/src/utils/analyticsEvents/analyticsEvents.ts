// Events should be structured as "[active verb] [object]" and should be all lowercase
export enum EVENTS {
  // Highlight
  VIEW_HIGHLIGHT = 'view highlight',
  SUBMIT_HIGHLIGHT_FEEDBACK = 'submit highlight feedback',
  EXPORT_HIGHLIGHT_TABLE = 'export highlights table',

  // Insights
  LOAD_INSIGHTS_PAGE = 'load insights page',
  CHANGE_INSIGHTS_METRIC_TYPE = 'change insights metric type',
  OPEN_INSIGHTS_VIDEO_BLADE = 'open insights video blade',

  // Daily Insights
  LOAD_DAILY_INSIGHTS_PAGE = 'load daily insights page',
  CLICK_CASES_DAILY_INSIGHTS_TAB = 'load cases daily insights tab',
  CLICK_BLOCKS_DAILY_INSIGHTS_TAB = 'load blocks daily insights tab',
  OPEN_CASE_DATA_PANEL = 'open daily insights case data panel',
  CLOSE_CASE_DATA_PANEL = 'close daily insights case data panel',

  // Turnover
  FILTER_TURNOVER_DATE_RANGE = 'filter turnover date range',
  FILTER_TURNOVER_SITES = 'filter turnover sites',
  FILTER_TURNOVER_ROOMS = 'filter turnover rooms',
  FILTER_TURNOVER_STAFF = 'filter turnover staff',
  FILTER_TURNOVER_ANESTHESIA = 'filter turnover anesthesia',
  FILTER_TURNOVER_CIRCULATOR = 'filter turnover circulator',
  FILTER_TURNOVER_SCRUB_TECH = 'filter turnover scrub tech',
  FILTER_TURNOVER_SERVICE_LINE = 'filter turnover service line',
  FILTER_TURNOVER_PROCEDURE = 'filter turnover procedure',
  FILTER_TURNOVER_DAYS_OF_WEEK = 'filter turnover days of week',
  FILTER_TURNOVER_CASE_CLASSIFICATION_TYPES_ID = 'filter turnover case classification type',
  CHANGE_TURNOVER_GRAPH_DIMENSION = 'change turnover graph dimension',
  CHANGE_TURNOVER_GRAPH_TOP_COUNT = 'change turnover graph top count',
  CHANGE_TURNOVER_GRAPH_SORT = 'change turnover graph sort',
  PAGE_TURNOVER_TABLE = 'page turnover table',
  SORT_TURNOVER_TABLE = 'sort turnover table',
  EXPORT_TURNOVER_TABLE = 'export turnover table',
  SCHEDULE_PAGE_OPEN_TURNOVER_DELAY_NOTE = 'open turnover delay note',
  SCHEDULE_PAGE_UPDATE_TURNOVER_DELAY_NOTE = 'update turnover delay note',
  CHANGE_TURNOVER_TABLE_COLUMNS = 'change turnover table columns',

  // First case start
  FILTER_FIRST_CASE_START_DATE_RANGE = 'filter first case start date range',
  FILTER_FIRST_CASE_START_SITES = 'filter first case start sites',
  FILTER_FIRST_CASE_START_ROOMS = 'filter first case start rooms',
  FILTER_FIRST_CASE_START_STAFF = 'filter first case start staff',
  FILTER_FIRST_CASE_START_ANESTHESIA = 'filter first case start anesthesia',
  FILTER_FIRST_CASE_START_CIRCULATOR = 'filter first case start circulator',
  FILTER_FIRST_CASE_START_SCRUB_TECH = 'filter first case start scrub tech',
  FILTER_FIRST_CASE_START_SERVICE_LINE = 'filter first case start service line',
  FILTER_FIRST_CASE_START_PROCEDURE = 'filter first case start procedure',
  FILTER_FIRST_CASE_START_DAYS_OF_WEEK = 'filter first case start days of week',
  FILTER_FIRST_CASE_START_DELAY_TYPE = 'filter first case start delay type',
  CHANGE_FIRST_CASE_START_GRAPH_DIMENSION = 'change first case start graph dimension',
  CHANGE_FIRST_CASE_START_GRAPH_TOP_COUNT = 'change first case start graph top count',
  CHANGE_FIRST_CASE_START_GRAPH_SORT = 'change first case start graph sort',
  PAGE_FIRST_CASE_START_TABLE = 'page first case start table',
  SORT_FIRST_CASE_START_TABLE = 'sort first case start table',
  EXPORT_FIRST_CASE_START_TABLE = 'export first case start table',

  // Case length
  FILTER_CASE_LENGTH_DATE_RANGE = 'filter case length date range',
  FILTER_CASE_LENGTH_SITES = 'filter case length sites',
  FILTER_CASE_LENGTH_ROOMS = 'filter case length rooms',
  FILTER_CASE_LENGTH_STAFF = 'filter case length staff',
  FILTER_CASE_LENGTH_ANESTHESIA = 'filter case length anesthesia',
  FILTER_CASE_LENGTH_CIRCULATOR = 'filter case length circulator',
  FILTER_CASE_LENGTH_SCRUB_TECH = 'filter case length scrub tech',
  FILTER_CASE_LENGTH_SERVICE_LINE = 'filter case length service line',
  FILTER_CASE_LENGTH_PROCEDURE = 'filter case length procedure',
  FILTER_CASE_LENGTH_DAYS_OF_WEEK = 'filter case length days of week',
  CHANGE_CASE_LENGTH_GRAPH_DIMENSION = 'change case length graph dimension',
  CHANGE_CASE_LENGTH_GRAPH_TOP_COUNT = 'change case length graph top count',
  CHANGE_CASE_LENGTH_GRAPH_SORT = 'change case length graph sort',
  CHANGE_CASE_LENGTH_GRAPH_STACK_GROUPING = 'change case length graph stack grouping',
  PAGE_CASE_LENGTH_TABLE = 'page case length table',
  SORT_CASE_LENGTH_TABLE = 'sort case length table',
  EXPORT_CASE_LENGTH_TABLE = 'export case length table',
  CASE_LENGTH_COLUMN_VISIBILITY_CHANGED = 'case length column visibility changed',

  // Case volume
  FILTER_CASE_VOLUME_DATE_RANGE = 'filter case volume date range',
  FILTER_CASE_VOLUME_SITES = 'filter case volume sites',
  FILTER_CASE_VOLUME_ROOMS = 'filter case volume rooms',
  FILTER_CASE_VOLUME_STAFF = 'filter case volume staff',
  FILTER_CASE_VOLUME_ANESTHESIA = 'filter case volume anesthesia',
  FILTER_CASE_VOLUME_CIRCULATOR = 'filter case volume circulator',
  FILTER_CASE_VOLUME_SCRUB_TECH = 'filter case volume scrub tech',
  FILTER_CASE_VOLUME_SERVICE_LINE = 'filter case volume service line',
  FILTER_CASE_VOLUME_PROCEDURE = 'filter case volume procedure',
  FILTER_CASE_VOLUME_DAYS_OF_WEEK = 'filter case volume days of week',
  CHANGE_CASE_VOLUME_GRAPH_DIMENSION = 'change case volume graph dimension',
  CHANGE_CASE_VOLUME_GRAPH_TOP_COUNT = 'change case volume graph top count',
  CHANGE_CASE_VOLUME_GRAPH_SORT = 'change case volume graph sort',
  PAGE_CASE_VOLUME_TABLE = 'page case volume table',
  SORT_CASE_VOLUME_TABLE = 'sort case volume table',
  EXPORT_CASE_VOLUME_TABLE = 'export case volume table',

  // Video
  CHANGE_VIDEO_CAMERA = 'change video camera',
  CHANGE_VIDEO_SPEED = 'change video speed',
  ENTER_VIDEO_FULLSCREEN = 'enter video fullscreen',
  EXIT_VIDEO_FULLSCREEN = 'exit video fullscreen',
  PAUSE_VIDEO = 'pause video',
  PLAY_VIDEO = 'play video',
  SEEK_VIDEO = 'seek video',
  WATCH_VIDEO = 'watch video',
  VIDEO_PRIVACY_TOGGLED = 'video privacy toggled',
  LOADED_FRAGMENT = 'loaded fragment',
  LOADED_HLS_LEVEL = 'loaded hls level',

  // Live
  LIVE_PAGE_LOAD_PAGE = 'load live page',
  CHANGE_LIVE_CAMERA = 'change live camera',
  EXPAND_LIVE_STREAM = 'expand live stream',
  UNEXPAND_LIVE_STREAM = 'unexpand live stream',
  LIVE_PAGE_EXIT = 'exit live page',
  SNOOZE_LIVE_STREAM = 'snooze live stream',
  UNSNOOZE_LIVE_STREAMS = 'unsnooze live streams',
  LIVE_PAGE_FILTER_SITES = 'change live site',
  LIVE_PAGE_FILTER_ROOMS = 'change live rooms',
  LIVE_PAGE_FILTER_DAILY_METRIC = 'live page filter daily metric',
  LIVE_PAGE_FILTER_DATE = 'live page filter date',
  LIVE_PAGE_FILTER_LAYOUT = 'live page filter layout',
  LIVE_PAGE_FILTER_SURGEONS = 'live page filter surgeons',
  LIVE_PAGE_FILTER_TIME_RANGE = 'live page filter time range',
  LIVE_PAGE_TOGGLE_FILTERS = 'live page toggle filters',
  LIVE_PAGE_TOGGLE_SCHEDULED = 'live page toggle scheduled',
  LIVE_PAGE_TOGGLE_SHOW_CLOSED_ROOMS = 'live page toggle show closed rooms',
  LIVE_PAGE_TOGGLE_SHOW_METRICS = 'live page toggle show metrics',
  LIVE_PAGE_TOGGLE_SORT_KEY = 'live page toggle sort key',

  // Schedule
  SCHEDULE_PAGE_LOAD_PAGE = 'load schedule page',
  SCHEDULE_PAGE_EXIT = 'exit schedule page',
  SCHEDULE_PAGE_FILTER_DATE = 'filter schedule date range',
  SCHEDULE_PAGE_FILTER_SITES = 'filter schedule sites',
  SCHEDULE_PAGE_FILTER_ROOMS = 'filter schedule rooms',
  SCHEDULE_PAGE_FILTER_SURGEONS = 'filter schedule surgeons',
  SCHEDULE_PAGE_FILTER_TIME_RANGE = 'filter schedule time range',
  ENTER_TV_MODE = 'enter fullscreen',
  EXIT_TV_MODE = 'exit fullscreen',
  SCHEDULE_PAGE_OPEN_VIDEO_BLADE = 'open schedule video blade',
  SCHEDULE_PAGE_ENABLE_SHOW_SCHEDULED = 'enable show scheduled',
  SCHEDULE_PAGE_DISABLE_SHOW_SCHEDULED = 'disable show scheduled',
  SCHEDULE_PAGE_ENTER_BLOCK_TOOLTIP = 'enter schedule block tooltip',
  SCHEDULE_PAGE_FILTER_DAILY_METRIC = 'schedule page filter daily metric',
  SCHEDULE_PAGE_FILTER_LAYOUT = 'schedule page filter layout',
  SCHEDULE_PAGE_TOGGLE_FILTERS = 'schedule page toggle filters',
  SCHEDULE_PAGE_TOGGLE_SCHEDULED = 'schedule page toggle scheduled',
  SCHEDULE_PAGE_TOGGLE_SHOW_CLOSED_ROOMS = 'schedule page toggle show closed rooms',
  SCHEDULE_PAGE_TOGGLE_SHOW_METRICS = 'schedule page toggle show metrics',
  SCHEDULE_PAGE_TOGGLE_SORT_KEY = 'schedule page toggle sort key',

  // Edit Schedule
  EDIT_SCHEDULE_LOAD_PAGE = 'load edit schedule page',
  EDIT_SCHEDULE_EXIT = 'exit edit schedule page',
  EDIT_SCHEDULE_FILTER_DATE = 'filter edit schedule date range',
  EDIT_SCHEDULE_FILTER_SITES = 'filter edit schedule sites',
  EDIT_SCHEDULE_FILTER_ROOMS = 'filter edit schedule rooms',
  EDIT_SCHEDULE_FILTER_SURGEONS = 'filter edit schedule surgeons',
  EDIT_SCHEDULE_FILTER_DAILY_METRIC = 'edit schedule filter daily metric',
  EDIT_SCHEDULE_FILTER_LAYOUT = 'edit schedule filter layout',
  EDIT_SCHEDULE_FILTER_TIME_RANGE = 'edit schedule filter time range',
  EDIT_SCHEDULE_TOGGLE_FILTERS = 'edit schedule toggle filters',
  EDIT_SCHEDULE_TOGGLE_SCHEDULED = 'edit schedule toggle scheduled',
  EDIT_SCHEDULE_TOGGLE_SHOW_CLOSED_ROOMS = 'edit schedule toggle show closed_rooms',
  EDIT_SCHEDULE_TOGGLE_SHOW_METRICS = 'edit schedule toggle show metrics',
  EDIT_SCHEDULE_TOGGLE_SORT_KEY = 'edit schedule toggle sort key',

  // Staff Management
  STAFF_MANAGEMENT_LOAD_PAGE = 'load staff management page',
  STAFF_MANAGEMENT_PAGE_EXIT = 'exit staff management page',
  STAFF_MANAGEMENT_FILTER_DATE = 'filter staff management date',
  STAFF_MANAGEMENT_FILTER_SITES = 'filter staff management site',
  STAFF_MANAGEMENT_FILTER_ROOMS = 'filter staff management rooms',
  STAFF_MANAGEMENT_FILTER_TIME_RANGE = 'filter staff management time range',
  STAFF_MANAGEMENT_CHANGE_RATIO = 'change staff management ratio',
  STAFF_MANAGEMENT_FILTER_DAILY_METRIC = 'staff management page filter daily metric',
  STAFF_MANAGEMENT_FILTER_LAYOUT = 'staff management page filter layout',
  STAFF_MANAGEMENT_FILTER_SURGEONS = 'staff management page filter surgeons',
  STAFF_MANAGEMENT_TOGGLE_FILTERS = 'staff management page toggle filters',
  STAFF_MANAGEMENT_TOGGLE_SCHEDULED = 'staff management page toggle scheduled',
  STAFF_MANAGEMENT_TOGGLE_SHOW_CLOSED_ROOMS = 'staff management page toggle show closed rooms',
  STAFF_MANAGEMENT_TOGGLE_SHOW_METRICS = 'staff management page toggle show metrics',
  STAFF_MANAGEMENT_TOGGLE_SORT_KEY = 'staff management page toggle sort key',

  // PostOp
  POSTOP_LOAD_PAGE = 'load postop page',
  POSTOP_PAGE_EXIT = 'exit postop page',
  POSTOP_OPEN_NOT_STARTED_PANEL = 'open postop not started panel',
  POSTOP_CLOSE_NOT_STARTED_PANEL = 'close postop not started panel',
  POSTOP_OPEN_IN_PROGRESS_PANEL = 'open postop in progress panel',
  POSTOP_CLOSE_IN_PROGRESS_PANEL = 'close postop in progress panel',
  POSTOP_CLICK_TAB = 'click postop tab',
  POSTOP_FILTER_DAILY_METRIC = 'postop page filter daily metric',
  POSTOP_FILTER_DATE = 'postop page filter date',
  POSTOP_FILTER_LAYOUT = 'postop page filter layout',
  POSTOP_FILTER_ROOMS = 'postop page filter rooms',
  POSTOP_FILTER_SITES = 'postop page filter sites',
  POSTOP_FILTER_SURGEONS = 'postop page filter surgeons',
  POSTOP_FILTER_TIME_RANGE = 'postop page filter time range',
  POSTOP_TOGGLE_FILTERS = 'postop page toggle filters',
  POSTOP_TOGGLE_SCHEDULED = 'postop page toggle scheduled',
  POSTOP_TOGGLE_SHOW_CLOSED_ROOMS = 'postop page toggle show closed rooms',
  POSTOP_TOGGLE_SHOW_METRICS = 'postop page toggle show metrics',
  POSTOP_TOGGLE_SORT_KEY = 'postop page toggle sort key',

  // PreOp
  PREOP_CLICK_TAB = 'click preop tab',
  PREOP_LOAD_PAGE = 'load preop page',
  PREOP_PAGE_EXIT = 'exit preop page',
  PREOP_FILTER_ROOMS = 'filter preop rooms',
  PREOP_FILTER_SITES = 'filter preop sites',
  PREOP_FILTER_DAILY_METRIC = 'preop page filter daily metric',
  PREOP_FILTER_DATE = 'preop page filter date',
  PREOP_FILTER_LAYOUT = 'preop page filter layout',
  PREOP_FILTER_SURGEONS = 'preop page filter surgeons',
  PREOP_FILTER_TIME_RANGE = 'preop page filter time range',
  PREOP_TOGGLE_FILTERS = 'preop page toggle filters',
  PREOP_TOGGLE_SCHEDULED = 'preop page toggle scheduled',
  PREOP_TOGGLE_SHOW_CLOSED_ROOMS = 'preop page toggle show closed rooms',
  PREOP_TOGGLE_SHOW_METRICS = 'preop page toggle show metrics',
  PREOP_TOGGLE_SORT_KEY = 'preop page toggle sort key',

  //Turnovers Dashboard
  TURNOVERS_DASHBOARD_CLICK_TAB = 'click turnovers tab',
  TURNOVERS_DASHBOARD_LOAD_PAGE = 'load turnovers page',
  TURNOVERS_DASHBOARD_PAGE_EXIT = 'exit turnover dashboard page',
  TURNOVERS_DASHBOARD_FILTER_ROOMS = 'filter turnover dashboard page rooms',
  TURNOVERS_DASHBOARD_FILTER_SITES = 'filter turnover dashboard page sites',
  TURNOVERS_DASHBOARD_FILTER_DAILY_METRIC = 'turnover dashboard page filter daily metric',
  TURNOVERS_DASHBOARD_FILTER_DATE = 'turnover dashboard page filter date',
  TURNOVERS_DASHBOARD_FILTER_LAYOUT = 'turnover dashboard page filter layout',
  TURNOVERS_DASHBOARD_FILTER_SURGEONS = 'turnover dashboard page filter surgeons',
  TURNOVERS_DASHBOARD_FILTER_TIME_RANGE = 'turnover dashboard page filter time range',
  TURNOVERS_DASHBOARD_TOGGLE_FILTERS = 'turnover dashboard page toggle filters',
  TURNOVERS_DASHBOARD_TOGGLE_SCHEDULED = 'turnover dashboard page toggle scheduled',
  TURNOVERS_DASHBOARD_TOGGLE_SHOW_CLOSED_ROOMS = 'turnover dashboard page toggle show closed rooms',
  TURNOVERS_DASHBOARD_TOGGLE_SHOW_METRICS = 'turnover dashboard page toggle show metrics',
  TURNOVERS_DASHBOARD_TOGGLE_SORT_KEY = 'turnover dashboard page toggle sort key',

  // Terminal Cleans
  LOAD_TERMINAL_CLEANS_PAGE = 'load terminal cleans page',
  UPSERT_MEASUREMENT_PERIOD = 'upsert measurement period',
  DELETE_MEASUREMENT_PERIOD = 'delete measurement period',
  VIEW_TERMINAL_CLEAN_BLADE = 'view terminal clean blade',

  // Boards
  BOARD_VIEWED = 'board viewed',
  CREATE_BOARD = 'create board',
  UPDATE_BOARD = 'update board',
  DELETE_BOARD = 'delete board',
  UPDATE_CASE_FLAGS = 'update case flags',
  UPDATE_CASE_NOTE = 'update case note',
  UPDATE_CASE_STAFF_ASSIGNMENTS = 'update staff assignments',
  UPDATE_ROOM_NEXT_PREV = 'update room next/prev',

  // Case Duration
  SELECT_CASE_DURATION_COMPLEXITY = 'select case duration complexity',
  SELECT_CASE_DURATION_SURGEON = 'select case duration surgeon',
  SELECT_CASE_DURATION_PROCEDURE = 'select case duration procedure',
  CLEAR_CASE_DURATION_LOOKUP = 'clear case duration lookup',
  RECEIVE_CASE_DURATION_PREDICTIONS = 'receive case duration predictions',
  OPEN_CASE_DURATION_RECENT_CASES_VIDEO_BLADE = 'open case duration recent cases video blade',

  TIME_FINDER_SCROLL_TO_TOP = 'click time finder scroll-to-top',
  TIME_FINDER_PREV_MONTH = 'click time finder prev month',
  TIME_FINDER_NEXT_MONTH = 'click time finder next month',
  TIME_FINDER_OPEN_MONTH_PICKER = 'click time finder open month picker',
  TIME_FINDER_SELECT_MONTH = 'select time finder month',
  TIME_FINDER_SELECT_DATE = 'click time finder scroll-to-date',
  TIME_FINDER_FILTER_SITES = 'filter time finder sites',
  TIME_FINDER_FILTER_ROOMS = 'filter time finder rooms',
  TIME_FINDER_FILTER_DOWS = 'filter time finder days of week',
  TIME_FINDER_FILTER_BLOCKS = 'filter time finder blocks',
  TIME_FINDER_FILTERS_RESET = 'reset time finder filters',
  TIME_FINDER_FILTERS_TOGGLE_CUSTOM_DURATION = 'toggle time finder custom duration',

  TIME_SELECTION_SELECT_TIME = 'select time',
  TIME_SELECTION_UNSELECT_TIME = 'unselect time',
  TIME_SELECTION_SEND_TIME = 'send time slot',
  TIME_SELECTION_EVAL_TIME = 'evaluate time slot',
  TIME_SELECTION_SEND_EMAIL = 'send time email',
  TIME_SELECTION_SAVE = 'save time selection',
  TIME_SELECTION_CANCEL = 'cancel time selection',
  TIME_SELECTION_EDIT = 'edit time selection',
  TIME_SELECTION_ERROR = 'time selection error',
  TIME_SELECTION_UPDATE_TIME = 'update time selection time',
  TIME_SELECTION_CLEAR_SELECTION = 'clear time selection',
  TIME_SELECTION_CLOSE_CONFIRMATION_MODAL = 'close time selection confirmation modal',
  TIME_SELECTION_CONFIRM_SELECTION = 'confirm time selection',

  // Base
  PAGE_VIEWED = 'Page Viewed',
}
