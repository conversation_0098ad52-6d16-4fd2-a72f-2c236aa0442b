import {
  <PERSON><PERSON><PERSON>,
  ReactN<PERSON>,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { toast } from 'react-toastify'

import { useTheme } from '@emotion/react'

import { useMutation, useQuery } from '@apollo/client'
import { Form, Formik, FormikHelpers } from 'formik'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime } from 'luxon'

import {
  ApellaDateTimeFormats,
  Blade,
  Button,
  ButtonLink,
  Caps2,
  FlexContainer,
  fontWeights,
  H5,
  mediaQueries,
  NavigationTab,
  NavigationTabTitle,
  NavigationTabWrapper,
  P3,
  remSpacing,
  shape,
  Span3,
  theme,
  typography,
  VideoPlayerRef,
  VideoProgress,
} from '@apella/component-library'
import {
  CloudMediaPlayer,
  useVideoPlayerTiming,
} from 'src/components/CloudMediaPlayer'
import { RoomPrivacyToggle } from 'src/components/RoomPrivacyToggle'
import { useRoomPrivacy } from 'src/hooks/useRoomPrivacy'
import TimelineView from 'src/pages/Schedule/TimelineView'
import { useRoomName, useRoomSiteId, useRoomSiteName } from 'src/utils/hooks'
import { EM_DASH } from 'src/utils/htmlCodes'
import { getDisplayNameByTurnoverLength } from 'src/utils/turnovers'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'

import { CaseType, TurnoverType } from '../../__generated__/globalTypes'
import LoadingOverlay from '../../components/LoadingOverlay'
import { WebDashboardFeatureFlagSet } from '../../modules/feature/types'
import {
  CaseLabelInput,
  generateCaseLabelsUpsertInput,
  generateCaseStaffPlanUpsertInputData,
} from '../../modules/planning/helpers'
import { useCurrentUser } from '../../modules/user/hooks'
import { LocationPath } from '../../router/types'
import { EVENTS, useAnalyticsEventLogger } from '../../utils/analyticsEvents'
import { CaseStaffPlanRole, getPrimarySurgeonText } from '../../utils/roles'
import { useMostRecentEvent } from '../Live/hooks'
import {
  CaseStatusPill,
  CaseTimingData,
  getPatientClassText,
  ScheduledTimingNode,
} from '../Schedule/CaseDetails'
import { useScheduleFilterProviderUrls } from '../Schedule/ScheduleFilterProvider'
import { TurnoverTimingDetails } from '../Schedule/TurnoverTimingDetails'
import { useTimelineHourBounds } from '../Schedule/useTimelineHourBounds'
import { useTimelineState } from '../Schedule/useTimelineState'
import { ApellaCase, Turnover } from '../types'
import {
  GetCaseLabelsForm,
  GetCaseLabelsFormVariables,
  UpsertCaseLabels,
  UpsertCaseLabelsVariables,
  UpsertCaseNote,
  UpsertCaseNoteVariables,
  UpsertCaseStaff,
  UpsertCaseStaffVariables,
} from './__generated__'
import {
  UPSERT_CASE_LABELS,
  UPSERT_CASE_NOTE,
  UPSERT_CASE_STAFF,
  GET_CASE_LABELS_FORM,
} from './queries'
import { CasePlanningFormValues } from './types'
import { onStaffError, VideoBladeCard, VideoBladeCards } from './VideoBladeCard'

export interface VideoBladeProps {
  apellaCaseId?: string
  /** This should be a datetime w/0s and 0ms */
  endTime?: string
  isBladeOpen: boolean
  onClose: () => void
  roomId?: string
  /** This should be a datetime w/0s and 0ms */
  startTime?: string
  time?: string
  turnoverId?: string
}

export interface VideoBladeContentProps {
  apellaCaseId?: string
  endTime: string
  eventNames?: string[]
  isHighlight?: boolean
  onClose: () => void
  roomId: string
  showHeader?: boolean
  startTime: string
  time?: string
  turnoverId?: string
}

enum BladeType {
  CASE,
  TURNOVER,
  IDLE,
  HIGHLIGHT,
}

enum VideoBladeCaseContext {
  CASE = 'CASE',
  PRECEDING_CASE = 'PRECEDING_CASE',
  FOLLOWING_CASE = 'FOLLOWING_CASE',
}

export const VideoBlade = ({
  isBladeOpen,
  onClose,
  roomId,
  startTime,
  endTime,
  time,
  apellaCaseId,
  turnoverId,
}: VideoBladeProps) => {
  return (
    <Blade
      isOpen={isBladeOpen}
      onClose={onClose}
      size={'lg'}
      side={'right'}
      overlay={true}
    >
      {roomId && startTime && endTime && (
        <VideoBladeContent
          roomId={roomId}
          startTime={startTime}
          endTime={endTime}
          time={time}
          onClose={onClose}
          apellaCaseId={apellaCaseId}
          turnoverId={turnoverId}
        />
      )}
    </Blade>
  )
}

export const VideoBladeContent = ({
  roomId,
  startTime,
  endTime,
  time,
  onClose,
  showHeader = true,
  eventNames,
  apellaCaseId,
  turnoverId,
  isHighlight = false,
}: VideoBladeContentProps): React.JSX.Element => {
  const theme = useTheme()
  const videoStartTime = DateTime.fromISO(startTime)
  const videoEndTime = DateTime.fromISO(endTime)
  const [progress, setProgress] = useState<VideoProgress>()
  const roomName = useRoomName(roomId)
  const siteName = useRoomSiteName(roomId)
  const { siteId, loading } = useRoomSiteId(roomId)
  const [hasLiveCase, setHasLiveCase] = useState<boolean | undefined>(undefined)
  const { ehrEnabled, toggleRoomPrivacy } =
    useFlags<WebDashboardFeatureFlagSet>()
  const { permissions } = useCurrentUser()
  const eventLogger = useAnalyticsEventLogger()

  const { currentOrganization } = useCurrentUser()
  const orgId = currentOrganization?.node.id

  const { isHistorical } = useVideoPlayerTiming(videoEndTime)

  const player = useRef<VideoPlayerRef>(null)
  const onTimelineClick = useCallback((roomId: string, clickTime: DateTime) => {
    if (player) player.current?.seekTo(clickTime)
  }, [])
  const headerTime = useMemo(
    () => videoStartTime.toLocaleString(ApellaDateTimeFormats.DATE),
    [videoStartTime]
  )

  const timelineState = useTimelineState({
    minTime: startTime,
    maxTime: endTime,
    options: {
      roomIds: [roomId],
      siteIds: siteId ? [siteId] : undefined,
      includeTurnovers: !!turnoverId,
    },
    skip: loading,
  })
  const { data: caseLabelFormData } = useQuery<
    GetCaseLabelsForm,
    GetCaseLabelsFormVariables
  >(GET_CASE_LABELS_FORM, { variables: { siteId: siteId! }, skip: !siteId })

  const timelineHourBounds = useTimelineHourBounds({
    minTime: startTime,
    maxTime: endTime,
    cases: timelineState.rooms.flatMap((r) => r.cases),
    calculationMethod: 'fullTime',
  })

  const activeApellaCase = timelineState.rooms
    .flatMap((r) => r.cases)
    .find((c) => c.id === apellaCaseId)
  useEffect(() => {
    if (activeApellaCase !== undefined && hasLiveCase === undefined) {
      setHasLiveCase(activeApellaCase.type === CaseType.LIVE)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeApellaCase?.id, hasLiveCase])

  const activeTurnover = timelineState.rooms
    .flatMap((r) => r.turnovers)
    .find((t) => t.id === turnoverId)
  const activeTurnoverFollowingCase = activeTurnover
    ? timelineState.rooms
        .flatMap((r) => r.cases)
        .find((c) => c.id === activeTurnover.followingCaseId)
    : undefined
  const activeTurnoverPrecedingCase = activeTurnover
    ? timelineState.rooms
        .flatMap((r) => r.cases)
        .find((c) => c.id === activeTurnover.precedingCaseId)
    : undefined

  const bladeType = apellaCaseId
    ? BladeType.CASE
    : turnoverId
      ? BladeType.TURNOVER
      : isHighlight
        ? BladeType.HIGHLIGHT
        : BladeType.IDLE

  const { data: roomPrivacyData } = useRoomPrivacy({
    roomId,
    skip: !toggleRoomPrivacy || isHistorical,
  })

  const hideMediaPlayerBecauseNoAccess =
    bladeType !== BladeType.HIGHLIGHT &&
    isHistorical &&
    !permissions?.readAnyAssetEnabled
  const hideMediaPlayerBecauseInFuture =
    activeApellaCase?.type === CaseType.FORECAST ||
    activeTurnover?.type === TurnoverType.FORECAST
  const hideMediaPlayer =
    hideMediaPlayerBecauseNoAccess || hideMediaPlayerBecauseInFuture
  const showCaseDetails = ehrEnabled && bladeType === BladeType.CASE
  const showFollowingCaseDetails =
    ehrEnabled && bladeType === BladeType.TURNOVER

  const [shownCaseState, setShownCaseState] = useState(
    bladeType === BladeType.CASE
      ? VideoBladeCaseContext.CASE
      : VideoBladeCaseContext.FOLLOWING_CASE
  )

  const shownCaseDetails = useMemo(
    () =>
      shownCaseState === VideoBladeCaseContext.CASE
        ? activeApellaCase
        : shownCaseState === VideoBladeCaseContext.PRECEDING_CASE
          ? activeTurnoverPrecedingCase
          : shownCaseState === VideoBladeCaseContext.FOLLOWING_CASE
            ? activeTurnoverFollowingCase
            : undefined,
    [
      shownCaseState,
      activeTurnoverFollowingCase,
      activeTurnoverPrecedingCase,
      activeApellaCase,
    ]
  )

  const defaultValues = {
    note: shownCaseDetails?.case?.notePlan ?? {
      id: crypto.randomUUID(),
      note: '',
    },
    ...Object.values(CaseStaffPlanRole).reduce(
      (accum, curr) => ({
        ...accum,
        [curr]:
          shownCaseDetails?.case?.staffPlan
            ?.filter((sp) => sp.role === curr.valueOf())
            .map((sp) => sp.id) ?? [],
      }),
      {}
    ),
    labels:
      shownCaseDetails?.case?.caseLabels?.reduce(
        (accum, curr) => ({
          ...accum,
          [curr.fieldId]: curr.optionId,
        }),
        {}
      ) ?? {},
  }

  const [upsertCaseStaff, { loading: caseStaffLoading }] = useMutation<
    UpsertCaseStaff,
    UpsertCaseStaffVariables
  >(UPSERT_CASE_STAFF, {
    onError: onStaffError,
  })

  const [upsertCaseNote, { loading: caseNoteLoading }] = useMutation<
    UpsertCaseNote,
    UpsertCaseNoteVariables
  >(UPSERT_CASE_NOTE)

  const [upsertCaseLabels, { loading: caseLabelsLoading }] = useMutation<
    UpsertCaseLabels,
    UpsertCaseLabelsVariables
  >(UPSERT_CASE_LABELS)

  const onPlanSubmit = useCallback(
    (
      values: CasePlanningFormValues,
      actions: FormikHelpers<CasePlanningFormValues>
    ) => {
      const caseId = shownCaseDetails?.case?.id

      if (!siteId || !orgId || !caseId) {
        return
      }

      const currentCspInput =
        shownCaseDetails
          .case!.staffPlan?.map((csp) => ({
            staff: csp,
            role: csp.role,
            caseId: shownCaseDetails.case!.id,
            archivedTime: undefined,
            id: csp.rowId,
          }))
          .filter(Boolean) ?? []

      const caseStaffPlanUpsertInputData = generateCaseStaffPlanUpsertInputData(
        {
          caseIds: [caseId],
          caseStaffPlans: currentCspInput,
          values,
          siteId,
          orgId,
        }
      )

      let staffPromise = Promise.resolve<null | boolean>(null)
      if (caseStaffPlanUpsertInputData.length) {
        eventLogger(EVENTS.UPDATE_CASE_STAFF_ASSIGNMENTS, {
          caseIds: [caseId],
          siteId,
          orgId,
          values,
          from: 'Schedule',
        })

        staffPromise = upsertCaseStaff({
          variables: { input: caseStaffPlanUpsertInputData },
        }).then((v) => v.data?.caseStaffPlanUpsert?.success ?? null)
      }

      let notePromise = Promise.resolve<null | boolean>(null)
      if (values.note.note !== (shownCaseDetails.case!.notePlan?.note ?? '')) {
        const input = {
          caseId: caseId,
          id: values.note.id,
          orgId: orgId,
          siteId: siteId,
        }

        eventLogger(EVENTS.UPDATE_CASE_NOTE, input)

        notePromise = upsertCaseNote({
          variables: {
            input: { ...input, note: values.note.note },
          },
        }).then((v) => v.data?.caseNotePlanUpsert?.success ?? null)
      }

      const currentLabels = (shownCaseDetails.case!.caseLabels ?? [])
        .map((cl) => ({
          archivedTime: undefined,
          caseId: shownCaseDetails.case!.id,
          id: cl.id,
          optionId: cl.optionId,
          fieldId: cl.fieldId,
        }))
        .filter(Boolean)

      const formLabels = Object.entries(values.labels).map<CaseLabelInput>(
        ([fieldId, optionId]) => ({
          archivedTime: undefined,
          caseId: shownCaseDetails.case!.id,
          optionId,
          fieldId,
          id: crypto.randomUUID(),
        })
      )

      const caseLabelsUpsertInput = generateCaseLabelsUpsertInput(
        currentLabels,
        formLabels
      )
      let labelsPromise = Promise.resolve<null | boolean>(null)
      if (caseLabelsUpsertInput.length !== 0) {
        labelsPromise = upsertCaseLabels({
          variables: { input: caseLabelsUpsertInput },
        }).then((v) => v.data?.caseLabelUpsert?.success ?? null)
      }

      // 3 return states, true (success), false (error), null (not executed)
      Promise.all([staffPromise, notePromise, labelsPromise]).then(
        (successes) => {
          if (successes.some((v) => v === false)) {
            toast.error('Error saving changes, please try again')
          } else if (successes.some((v) => v === true)) {
            timelineState.getCases()
            actions.resetForm({ values })
          }
        }
      )
    },
    [
      timelineState,
      upsertCaseLabels,
      upsertCaseNote,
      shownCaseDetails,
      orgId,
      siteId,
      upsertCaseStaff,
      eventLogger,
    ]
  )

  const addtlLoggingData = useMemo(() => {
    return shownCaseDetails?.id ? { apellaCaseId: shownCaseDetails.id } : {}
  }, [shownCaseDetails?.id])

  return (
    <FlexContainer
      css={{
        height: '100%',
        width: '100%',
        background: theme.palette.background.primary,
      }}
      direction={'column'}
    >
      {showHeader && (
        <Blade.Header css={{ padding: `0 0 0 ${remSpacing.medium}` }}>
          <Blade.Title>
            <div css={{ padding: `${remSpacing.xsmall} 0 ` }}>
              <div
                css={{
                  ...typography.p3,
                  display: 'flex',
                  gap: remSpacing.xsmall,
                }}
              >
                <span css={{ ...fontWeights.semibold }}>{roomName}</span>
                {!!roomPrivacyData?.room && (
                  <RoomPrivacyToggle
                    roomId={roomId}
                    isPrivacyEnabled={roomPrivacyData.room.privacyEnabled}
                    size="sm"
                  />
                )}
              </div>
              <P3 css={{ color: theme.palette.text.secondary }}>
                <Caps2>{siteName}</Caps2>
                <Span3
                  css={{
                    marginLeft: remSpacing.xsmall,
                    marginRight: remSpacing.xsmall,
                  }}
                >
                  &#8226;
                </Span3>
                {headerTime}
              </P3>
            </div>
          </Blade.Title>
          <Blade.CloseButton onClose={onClose} />
        </Blade.Header>
      )}
      {timelineState.isLoading ? (
        <LoadingOverlay />
      ) : (
        <Formik
          initialValues={defaultValues as CasePlanningFormValues}
          onSubmit={onPlanSubmit}
          css={{ height: '100%' }}
        >
          {({ values, setFieldValue, resetForm, dirty }) => {
            const onUpdateTurnoverCaseNav = (
              newShownCaseState: VideoBladeCaseContext
            ) => {
              setShownCaseState(newShownCaseState)
              resetForm()
            }

            return (
              <Form css={{ height: '100%' }}>
                <Blade.Body
                  css={{
                    height: '100%',
                    overflowY: 'auto',
                    paddingBottom: '20rem',
                  }}
                >
                  {(caseStaffLoading ||
                    caseNoteLoading ||
                    caseLabelsLoading) && <LoadingOverlay />}
                  {!hideMediaPlayer && (
                    <CloudMediaPlayer
                      initialStartTime={
                        time ? DateTime.fromISO(time) : DateTime.now()
                      }
                      roomId={roomId}
                      startTime={videoStartTime}
                      endTime={videoEndTime}
                      soundEnabled={false}
                      onProgress={setProgress}
                      ref={player}
                      isPrivacyEnabled={roomPrivacyData?.room?.privacyEnabled}
                      addtlLoggingData={addtlLoggingData}
                    />
                  )}
                  <div
                    css={{
                      display: 'grid',
                      gridTemplateColumns: 'minmax(0, 1fr)',
                      gap: remSpacing.medium,
                      padding: remSpacing.medium,
                      marginTop: remSpacing.medium,
                      [mediaQueries.lg]: {
                        padding: `0 ${remSpacing.gutter} ${remSpacing.gutter}`,
                      },
                    }}
                  >
                    {showCaseDetails && (
                      <VideoBladeCard>
                        <VideoBladeCaseDetails
                          activeApellaCase={activeApellaCase}
                          roomId={roomId}
                          siteId={siteId}
                          hasLiveCase={!!hasLiveCase}
                        />
                      </VideoBladeCard>
                    )}
                    {bladeType === BladeType.TURNOVER && (
                      <VideoBladeCard>
                        <VideoBladeTurnoverDetails turnover={activeTurnover} />
                      </VideoBladeCard>
                    )}
                    <VideoBladeCard style={{ padding: remSpacing.xsmall }}>
                      <TimelineView
                        {...timelineState}
                        {...timelineHourBounds}
                        tooltipPlacement={'top'}
                        progress={progress}
                        onClick={onTimelineClick}
                        showDetails={true}
                        eventNames={eventNames}
                        activeTurnoverId={turnoverId}
                      />
                    </VideoBladeCard>
                    {showFollowingCaseDetails && (
                      <BladeTurnoverCaseDetails
                        shownCaseState={shownCaseState}
                        onUpdateTurnoverCaseNav={onUpdateTurnoverCaseNav}
                        precedingCase={activeTurnoverPrecedingCase}
                        followingCase={activeTurnoverFollowingCase}
                        siteId={siteId}
                        roomId={roomId}
                        hasLiveCase={!!hasLiveCase}
                      />
                    )}
                    <VideoBladeCards
                      apellaCase={shownCaseDetails}
                      values={values}
                      setFieldValue={setFieldValue}
                      caseLabelForm={
                        caseLabelFormData?.site?.caseLabelForm ?? []
                      }
                    />
                  </div>
                </Blade.Body>
                <BladeActionSnackbar
                  animateIn={true}
                  animateIsVisible={dirty}
                  disabled={!dirty}
                  submitText={'Save changes'}
                />
              </Form>
            )
          }}
        </Formik>
      )}
    </FlexContainer>
  )
}

const VideoBladeTurnoverDetails = ({ turnover }: { turnover?: Turnover }) => {
  const theme = useTheme()
  return (
    <div
      css={{
        display: 'grid',
        [mediaQueries.lg]: {
          gridAutoFlow: 'column',
          justifyContent: 'space-between',
        },
      }}
    >
      <H5
        css={{
          display: 'inline-block',
          color: theme.palette.text.primary,
          textTransform: 'uppercase',
        }}
      >
        {getDisplayNameByTurnoverLength(turnover?.overallLengthStatus)}
      </H5>
      <TurnoverTimingDetails turnover={turnover} />
    </div>
  )
}

export const VideoBladeCaseDetails = ({
  activeApellaCase,
  roomId,
  siteId,
  hasLiveCase,
}: {
  activeApellaCase?: ApellaCase
  roomId: string
  siteId?: string
  hasLiveCase?: boolean
}) => {
  const theme = useTheme()
  const activeCase = activeApellaCase?.case
  const mostRecentEvent = useMostRecentEvent(siteId, !hasLiveCase)[roomId]
  const showPatientData = usePatientReadAccess()

  return (
    <div>
      <div
        css={{
          display: 'grid',
          [mediaQueries.md]: {
            gridAutoFlow: 'column',
            justifyContent: 'space-between',
          },
        }}
      >
        <div
          css={{
            width: '100%',
            [mediaQueries.lg]: {
              width: 'auto',
            },
          }}
        >
          <FlexContainer
            css={{
              alignItems: 'center',
              justifyContent: 'space-between',
              gap: remSpacing.xsmall,
              [mediaQueries.md]: {
                justifyContent: 'flex-start',
              },
            }}
          >
            <H5
              css={{
                display: 'inline-block',
                color: theme.palette.text.primary,
              }}
            >
              {activeCase ? getPrimarySurgeonText(activeCase) : EM_DASH}
            </H5>
            {!!activeApellaCase && (
              <CaseStatusPill status={activeApellaCase.status.name} />
            )}
          </FlexContainer>
          <P3 css={{ color: theme.palette.text.secondary }}>
            {activeCase?.procedures.length
              ? activeCase?.procedures.map((p) => p.name).join('; ')
              : EM_DASH}
          </P3>
          <P3 css={{ color: theme.palette.text.secondary }}>
            Case ID: {activeCase ? activeCase?.externalCaseId : EM_DASH}
          </P3>
        </div>
        <FlexContainer alignItems={'center'} gap={remSpacing.small}>
          <CaseTimingData apellaCase={activeApellaCase} />
          <div css={{ whiteSpace: 'nowrap' }}>
            <ScheduledTimingNode caseObj={activeApellaCase?.case} />
          </div>
        </FlexContainer>
      </div>

      <FlexContainer
        gap={remSpacing.medium}
        css={{
          marginTop: remSpacing.small,
          gridTemplateColumns: '1fr max-content 1fr',
          justifyContent: 'space-between',

          [mediaQueries.lg]: {
            gridTemplateColumns: 'auto',
            gridAutoFlow: 'column',
            justifyContent: 'start',
            display: 'grid',
          },
        }}
      >
        {showPatientData && (
          <VideoBladeCaseDetail
            label={'Patient'}
            text={
              activeCase?.patient
                ? `${activeCase?.patient?.abbreviatedName} ${activeCase?.patient?.age}; ${activeCase?.patient?.administrativeSex.toUpperCase()}`
                : ''
            }
            rightAlign
          ></VideoBladeCaseDetail>
        )}

        <VideoBladeCaseDetail
          label={'Add-on'}
          text={
            activeCase?.isAddOn
              ? 'Yes'
              : activeCase?.isAddOn === false
                ? 'No'
                : undefined
          }
        />
        <VideoBladeCaseDetail
          label={'Case'}
          text={activeCase?.caseClassificationType?.name}
        />
        <VideoBladeCaseDetail
          label={'Patient Class'}
          text={getPatientClassText(activeCase?.patientClass)}
          rightAlign
        />
        <VideoBladeCaseDetail
          label={'Service Line'}
          text={activeCase?.serviceLine?.name}
        />

        {hasLiveCase && (
          <VideoBladeCaseDetail
            label={'Last event'}
            text={mostRecentEvent?.attrs?.name}
          />
        )}
      </FlexContainer>
    </div>
  )
}

const VideoBladeCaseDetail = ({
  label,
  text,
  children,
  rightAlign,
}: {
  label: string
  text?: ReactNode
  children?: ReactNode
  rightAlign?: boolean
}) => {
  const theme = useTheme()

  return (
    <FlexContainer
      direction="column"
      css={
        rightAlign && {
          textAlign: 'right',
          justifySelf: 'end',
          [mediaQueries.md]: {
            textAlign: 'left',
            justifySelf: 'auto',
          },
        }
      }
    >
      <Caps2 css={{ color: theme.palette.gray[50] }}>{label}</Caps2>
      <FlexContainer gap={remSpacing.xsmall} css={{ position: 'relative' }}>
        <P3 css={{ color: theme.palette.text.primary }}>{text || EM_DASH}</P3>
        {children}
      </FlexContainer>
    </FlexContainer>
  )
}

export const BladeTurnoverCaseDetails = ({
  shownCaseState,
  onUpdateTurnoverCaseNav,
  followingCase,
  precedingCase,
  roomId,
  siteId,
  hasLiveCase,
}: {
  shownCaseState: VideoBladeCaseContext
  onUpdateTurnoverCaseNav: (newShownCaseState: VideoBladeCaseContext) => void
  followingCase?: ApellaCase
  precedingCase?: ApellaCase
  siteId?: string
  roomId: string
  hasLiveCase: boolean
}) => {
  const onPrecedingNav = useCallback(
    (e: MouseEvent) => {
      e.preventDefault()
      onUpdateTurnoverCaseNav(VideoBladeCaseContext.PRECEDING_CASE)
    },
    [onUpdateTurnoverCaseNav]
  )

  const onFollowingNav = useCallback(
    (e: MouseEvent) => {
      e.preventDefault()
      onUpdateTurnoverCaseNav(VideoBladeCaseContext.FOLLOWING_CASE)
    },
    [onUpdateTurnoverCaseNav]
  )

  return (
    <VideoBladeCard>
      <div>
        <NavigationTabWrapper>
          <NavigationTab
            isSelected={shownCaseState === VideoBladeCaseContext.PRECEDING_CASE}
          >
            <NavigationTabTitle
              isSelected={
                shownCaseState === VideoBladeCaseContext.PRECEDING_CASE
              }
              onClick={onPrecedingNav}
            >
              Previous Case
            </NavigationTabTitle>
          </NavigationTab>
          <NavigationTab
            isSelected={shownCaseState === VideoBladeCaseContext.FOLLOWING_CASE}
          >
            <NavigationTabTitle
              isSelected={
                shownCaseState === VideoBladeCaseContext.FOLLOWING_CASE
              }
              onClick={onFollowingNav}
            >
              Next Case
            </NavigationTabTitle>
          </NavigationTab>
        </NavigationTabWrapper>
      </div>
      <div>
        {shownCaseState === VideoBladeCaseContext.FOLLOWING_CASE ? (
          <VideoBladeCaseDetails
            activeApellaCase={followingCase}
            roomId={roomId}
            siteId={siteId}
            hasLiveCase={hasLiveCase}
          />
        ) : (
          <VideoBladeCaseDetails
            activeApellaCase={precedingCase}
            roomId={roomId}
            siteId={siteId}
            hasLiveCase={hasLiveCase}
          />
        )}
      </div>
    </VideoBladeCard>
  )
}

export const BladeActionSnackbar = ({
  submitText,
  disabled,
  animateIn = false,
  animateIsVisible = false,
}: {
  submitText: string
  disabled: boolean
  animateIn?: boolean
  animateIsVisible?: boolean
}) => {
  const { allSearchParams } = useScheduleFilterProviderUrls()

  return (
    <div
      css={{
        position: 'absolute',
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        transition: animateIn ? 'bottom 0.15s ease-in-out' : undefined,
        bottom: !animateIn || animateIsVisible ? remSpacing.gutter : -60,
      }}
    >
      <div
        css={{
          background: 'white',
          boxShadow: theme.shadows[3],
          borderRadius: shape.borderRadius.small,
          padding: remSpacing.xsmall,
        }}
      >
        <ButtonLink
          appearance="link"
          to={{
            pathname: LocationPath.Schedule,
            search: allSearchParams.toString(),
          }}
        >
          Cancel
        </ButtonLink>
        <Button type="submit" color="primary" disabled={disabled}>
          {submitText}
        </Button>
      </div>
    </div>
  )
}
