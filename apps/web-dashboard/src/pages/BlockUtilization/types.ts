import { DateTime } from 'luxon'

export interface BlockUtilizationTotals {
  totalAvailableSeconds: number
  totalBlockSeconds: number
  totalCancelledCases: number
  totalCappedUtilizedSeconds: number
  totalCases: number
  totalScheduledSeconds: number
  totalUtilizedSeconds: number
  utilizationPercentage: number
}

export interface BlockUtilizationByBlock extends BlockUtilizationTotals {
  blockId?: string
  blockName: string
}

interface AvailableSecondsForBlockTime {
  availableSeconds: number
  blockTimeId: string
  overridden: boolean
}
export interface DailyBlockUtilizationByBlock extends BlockUtilizationByBlock {
  availableSecondsByBlockTimeId: AvailableSecondsForBlockTime[]
  date: DateTime
}

export interface CaseForBlock {
  actualDurationSeconds: number
  caseId: string
  date: DateTime
  externalCaseId: string
  override: boolean
  procedures: string[]
  scheduledDurationSeconds: number
  status: string
  turnoverDurationSeconds: number
  utilizedDurationSeconds: number
}

interface BlockTimeRelease {
  endTime: DateTime
  releasedTime: DateTime
  startTime: DateTime
}

export interface BlockTimeAndReleases {
  blockTimeId: string
  endTime: DateTime
  releases: BlockTimeRelease[]
  roomName: string
  startTime: DateTime
}
