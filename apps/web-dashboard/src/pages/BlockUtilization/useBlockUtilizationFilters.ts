import { DateTime } from 'luxon'
import { ParsedQs } from 'qs'

import { useParsedSearchParams } from '@apella/hooks'

import {
  BY_DAY_OF_WEEK,
  LOW_TO_HIGH,
  VALID_BLOCK_SORT_TYPES,
  VALID_SITE_SORT_TYPES,
} from './constants'

export const DATE_RANGE_FILTER = 'dateRange'
export const SITE_FILTER = 'site'
export const SITE_SORT_TYPE = 'siteSortType'
export const BLOCK_SORT_TYPE = 'blockSortType'

export type BlockUtilizationFiltersType = {
  [DATE_RANGE_FILTER]: string[]
  [SITE_FILTER]: string
  [SITE_SORT_TYPE]: string
  [BLOCK_SORT_TYPE]: string
}
const yesterday = DateTime.now().minus({ days: 1 })
const firstDayOfMonth = yesterday.startOf('month')

export const defaults = {
  [DATE_RANGE_FILTER]: [firstDayOfMonth.toISO(), yesterday.toISO()],
  [SITE_FILTER]: '',
  [SITE_SORT_TYPE]: LOW_TO_HIGH,
  [BLOCK_SORT_TYPE]: BY_DAY_OF_WEEK,
}

export const castParams = (params: ParsedQs) => {
  const {
    [DATE_RANGE_FILTER]: selectedDateRangeParam,
    [SITE_FILTER]: selectedSiteIdParam,
    [SITE_SORT_TYPE]: selectedSiteSortTypeParam,
    [BLOCK_SORT_TYPE]: selectedBlockSortTypeParam,
  } = params

  const selectedDateRange =
    Array.isArray(selectedDateRangeParam) &&
    selectedDateRangeParam.length === 2 &&
    selectedDateRangeParam.every(
      (date) => typeof date === 'string' && DateTime.fromISO(date).isValid
    )
      ? (selectedDateRangeParam as string[])
      : defaults[DATE_RANGE_FILTER]

  const selectedSiteId =
    typeof selectedSiteIdParam === 'string'
      ? selectedSiteIdParam
      : defaults[SITE_FILTER]

  const selectedSiteSortType =
    typeof selectedSiteSortTypeParam === 'string' &&
    VALID_SITE_SORT_TYPES.includes(selectedSiteSortTypeParam)
      ? selectedSiteSortTypeParam
      : defaults[SITE_SORT_TYPE]

  const selectedBlockSortType =
    typeof selectedBlockSortTypeParam === 'string' &&
    VALID_BLOCK_SORT_TYPES.includes(selectedBlockSortTypeParam)
      ? selectedBlockSortTypeParam
      : defaults[BLOCK_SORT_TYPE]

  return {
    selectedDateRange,
    selectedSiteId,
    selectedSiteSortType,
    selectedBlockSortType,
  }
}

export const useBlockUtilizationFilters = () => {
  const { params, onSearch, ...rest } =
    useParsedSearchParams<BlockUtilizationFiltersType>({
      defaults,
    })
  const typedParams = castParams(params)
  return {
    params,
    onSearch,
    ...typedParams,
    ...rest,
  }
}
