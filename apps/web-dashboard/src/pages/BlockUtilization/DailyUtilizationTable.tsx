import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  Badge,
  Button,
  ButtonLink,
  CsvExportButton,
  Edit,
  H4,
  pxSpacing,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
} from '@apella/component-library'
import { LocationPath } from 'src/router/types'

import { UtilizedMinutesToolTip } from './BlockUtilizationToolTips'
import { FILE_NAME_DATE_FORMAT } from './constants'
import { DailyBlockUtilizationByBlock } from './types'
import { useBlockUtilizationFilters } from './useBlockUtilizationFilters'

const getUtilizationPercentageString = ({
  availableSeconds,
  utilizedSeconds,
}: {
  availableSeconds: number
  utilizedSeconds: number
}) => {
  const cappedUtilizedSeconds = Math.min(availableSeconds, utilizedSeconds)
  const utilizationPercentage = availableSeconds
    ? Math.floor((cappedUtilizedSeconds / availableSeconds) * 100)
    : 0

  return `${utilizationPercentage}%`
}

export const DailyUtilizationTable = ({
  blockName,
  blockUtilizations,
  editView = false,
  editOnClick = () => {},
}: {
  blockName: string
  blockUtilizations: DailyBlockUtilizationByBlock[]
  editView?: boolean
  editOnClick?: (selectedBlockUtilization: DailyBlockUtilizationByBlock) => void
}) => {
  const { selectedDateRange, selectedSiteId } = useBlockUtilizationFilters()

  const filename = `DailyUtilization-${blockName.replace(/[ ,\/\-\._()]/g, '')}-${DateTime.now().toFormat(FILE_NAME_DATE_FORMAT)}`

  const columns = [
    {
      name: 'Day',
      exportFormatter: (rowData: DailyBlockUtilizationByBlock) =>
        rowData.blockName,
    },
    {
      name: 'Cases',
      exportFormatter: (rowData: DailyBlockUtilizationByBlock) =>
        `${rowData.totalCases}`,
    },
    {
      name: 'Utilization (%)',
      exportFormatter: (rowData: DailyBlockUtilizationByBlock) => {
        const availableSeconds = rowData.availableSecondsByBlockTimeId.reduce(
          (sum, obj) => sum + obj.availableSeconds,
          0
        )
        return getUtilizationPercentageString({
          availableSeconds,
          utilizedSeconds: rowData.totalUtilizedSeconds,
        })
      },
    },
    {
      name: 'Available minutes',
      exportFormatter: (rowData: DailyBlockUtilizationByBlock) =>
        Math.floor(rowData.totalAvailableSeconds / 60).toLocaleString(),
    },
    {
      name: 'Utilized minutes',
      exportFormatter: (rowData: DailyBlockUtilizationByBlock) =>
        Math.floor(rowData.totalUtilizedSeconds / 60).toLocaleString(),
    },
  ]

  return (
    <div
      css={{
        border: `1px solid ${theme.palette.gray[30]}`,
        borderRadius: pxSpacing.xsmall,
        padding: remSpacing.gutter,
        gap: remSpacing.medium,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <H4>Daily Utilization</H4>
        {!editView && (
          <div
            css={{
              display: 'flex',
              gap: remSpacing.small,
            }}
          >
            <ButtonLink
              color="alternate"
              size="sm"
              to={{
                pathname: generatePath(
                  LocationPath.BlockUtilizationVerifyReport
                ),
                search: new URLSearchParams({
                  'dateRange[0]': selectedDateRange[0],
                  'dateRange[1]': selectedDateRange[1],
                  site: selectedSiteId,
                }).toString(),
              }}
            >
              <Edit size={'sm'} />
            </ButtonLink>
            <CsvExportButton
              rows={blockUtilizations}
              columns={columns}
              filename={filename}
            />
          </div>
        )}
      </div>
      <Table>
        <THead>
          <TR>
            {columns.map(({ name }) =>
              name == 'Utilized minutes' ? (
                <TH key={name}>
                  <span
                    css={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: remSpacing.xsmall,
                    }}
                  >
                    {name}
                    <UtilizedMinutesToolTip />
                  </span>
                </TH>
              ) : (
                <TH key={name}>{name}</TH>
              )
            )}
            {editView && <TH />}
          </TR>
        </THead>
        <TBody>
          {blockUtilizations.map((blockUtilization) => {
            const availableSeconds =
              blockUtilization.availableSecondsByBlockTimeId.reduce(
                (sum, obj) => sum + obj.availableSeconds,
                0
              )
            const overrideExists =
              blockUtilization.availableSecondsByBlockTimeId.some(
                (availableSecondsForBlockTime) =>
                  availableSecondsForBlockTime.overridden === true
              )

            const utilizationPercentage = getUtilizationPercentageString({
              availableSeconds,
              utilizedSeconds: blockUtilization.totalUtilizedSeconds,
            })
            return (
              <TR key={blockUtilization.date.toISO()}>
                <TD>{blockUtilization.blockName}</TD>
                <TD>{blockUtilization.totalCases}</TD>
                <TD>{utilizationPercentage}</TD>
                <TD>
                  <span
                    css={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: remSpacing.xsmall,
                    }}
                  >
                    {Math.floor(availableSeconds / 60).toLocaleString()}
                    {overrideExists && (
                      <Badge color="orange" variant="secondary">
                        Adjusted
                      </Badge>
                    )}
                  </span>
                </TD>
                <TD>
                  {Math.floor(
                    blockUtilization.totalUtilizedSeconds / 60
                  ).toLocaleString()}
                </TD>
                {editView && (
                  <TD>
                    <Button
                      appearance="link"
                      size="sm"
                      onClick={() => {
                        editOnClick(blockUtilization)
                      }}
                    >
                      <Edit size={'sm'} />
                    </Button>
                  </TD>
                )}
              </TR>
            )
          })}
        </TBody>
      </Table>
    </div>
  )
}
