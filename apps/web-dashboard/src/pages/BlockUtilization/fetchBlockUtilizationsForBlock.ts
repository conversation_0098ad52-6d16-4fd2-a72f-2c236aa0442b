import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { groupBy } from 'lodash'
import { DateTime } from 'luxon'

import { isoWeekDateToDayOfWeek } from 'src/components/DayOfWeekFilter'
import { logger } from 'src/utils/exceptionLogging'

import {
  CaseStatus,
  DEFAULT_TURNOVER_DURATION_IN_MINUTES,
} from '../BlockUtilizationManagement/constants'
import { ISOWeekDate } from '../Insights/types'
import {
  GetBlockUtilizationForBlockView,
  GetBlockUtilizationForBlockViewVariables,
} from './__generated__'
import { BY_WEEK, BY_DAY_OF_WEEK } from './constants'
import { GET_BLOCK_UTILIZATIONS_FOR_BLOCK_VIEW } from './queries'
import {
  BlockUtilizationByBlock,
  CaseForBlock,
  DailyBlockUtilizationByBlock,
} from './types'
import { getBlockUtilizationTotals, getByDayFormattedDateTime } from './utils'

export const fetchBlockUtilizationsForBlock = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    selectedDateRange,
    selectedSiteId,
    selectedBlockId,
    selectedBlockSortType,
    timezone,
  }: {
    selectedDateRange: string[]
    selectedSiteId: string
    selectedBlockId: string
    selectedBlockSortType: string
    timezone: string
  }
) => {
  const { data: blockUtilizationsDataForBlock, error } = await client.query<
    GetBlockUtilizationForBlockView,
    GetBlockUtilizationForBlockViewVariables
  >({
    query: GET_BLOCK_UTILIZATIONS_FOR_BLOCK_VIEW,
    variables: {
      query: {
        minDate: selectedDateRange[0],
        maxDate: selectedDateRange[1],
        siteId: selectedSiteId,
        blockId: selectedBlockId,
      },
    },
  })
  if (error) {
    logger.log(`Error getting Block Utilization data: ${error.message}`)
    throw error
  }

  const overallBlockUtilizationForBlock =
    blockUtilizationsDataForBlock.blockUtilizations &&
    blockUtilizationsDataForBlock.blockUtilizations.length > 0
      ? getBlockUtilizationTotals(
          blockUtilizationsDataForBlock.blockUtilizations
        )
      : undefined

  const dailyBlockUtilizations: DailyBlockUtilizationByBlock[] = (
    blockUtilizationsDataForBlock.blockUtilizations ?? []
  ).map((blockUtilization) => {
    const totals = getBlockUtilizationTotals([blockUtilization])
    return {
      ...totals,
      date: DateTime.fromISO(blockUtilization.date),
      blockName: getByDayFormattedDateTime(
        DateTime.fromISO(blockUtilization.date)
      ),
      availableSecondsByBlockTimeId:
        blockUtilization.availableSecondsByBlockTimeId,
    }
  })

  let blockUtilizations: BlockUtilizationByBlock[] = dailyBlockUtilizations

  if (
    selectedBlockSortType === BY_DAY_OF_WEEK ||
    selectedBlockSortType === BY_WEEK
  ) {
    const groupedBlockUtilization = groupBy(
      blockUtilizationsDataForBlock.blockUtilizations,
      (blockUtilization) => {
        if (selectedBlockSortType === BY_WEEK) {
          const date = DateTime.fromISO(blockUtilization.date)
          const weekStart = date.startOf('week')
          return weekStart.toFormat('MM/dd/yyyy')
        }
        return DateTime.fromISO(blockUtilization.date).weekday
      }
    )
    const sortedKeys = Object.keys(groupedBlockUtilization).sort((a, b) =>
      String(a).localeCompare(String(b))
    )
    blockUtilizations = sortedKeys.map((key) => {
      const utilizations = groupedBlockUtilization[key]
      const totals = getBlockUtilizationTotals(utilizations)
      return {
        ...totals,
        blockName:
          selectedBlockSortType === BY_DAY_OF_WEEK
            ? `${isoWeekDateToDayOfWeek[Number(key) as ISOWeekDate].toUpperCase()}S`
            : key,
      }
    })
  }

  const casesForBlock: CaseForBlock[] =
    blockUtilizationsDataForBlock.blockUtilizations
      ?.flatMap((blockUtilization) => blockUtilization.casesForBlockDay)
      .map((caseToBlock) => {
        const scheduledEndTime = DateTime.fromISO(
          caseToBlock?.scheduledCase?.scheduledEndTime ?? '',
          {
            zone: timezone,
          }
        )
        const scheduledDurationSeconds = scheduledEndTime.diff(
          DateTime.fromISO(
            caseToBlock?.scheduledCase?.scheduledStartTime ?? '',
            {
              zone: timezone,
            }
          ),
          'seconds'
        ).seconds

        return {
          caseId: caseToBlock?.caseId ?? '',
          externalCaseId: caseToBlock?.scheduledCase?.externalCaseId ?? '',
          status: caseToBlock?.scheduledCase?.status ?? CaseStatus.SCHEDULED,
          procedures:
            caseToBlock?.scheduledCase?.primaryCaseProcedures?.map(
              (primaryCaseProcedure) => primaryCaseProcedure.procedure.name
            ) ?? [],
          scheduledDurationSeconds,
          actualDurationSeconds: caseToBlock?.actualCaseSeconds ?? 0,
          utilizedDurationSeconds: caseToBlock?.overriddenUtilizedCaseSeconds
            ? caseToBlock?.overriddenUtilizedCaseSeconds
            : (caseToBlock?.utilizedCaseSeconds ?? 0),
          turnoverDurationSeconds: caseToBlock?.overriddenTurnoverSeconds
            ? caseToBlock?.overriddenTurnoverSeconds
            : DEFAULT_TURNOVER_DURATION_IN_MINUTES * 60,
          date: scheduledEndTime,
          override: caseToBlock?.overridden === true,
        }
      }) ?? []

  return {
    overallBlockUtilizationForBlock,
    blockUtilizations,
    dailyBlockUtilizations,
    casesForBlock,
  }
}
