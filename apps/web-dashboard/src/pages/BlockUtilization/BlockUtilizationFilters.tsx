import { useMemo, useState } from 'react'
import { generatePath } from 'react-router'

import { DateTime } from 'luxon'

import {
  Button,
  ButtonLink,
  CalendarIcon,
  Close,
  Edit,
  H3,
  H6,
  MonthRangePickerWithQuarters,
  P3,
  remSpacing,
  theme,
} from '@apella/component-library'
import { FilterLabel } from 'src/components/FilterLabel'
import { SitesFilter, SiteShape } from 'src/components/SitesFilter'
import { WarningMessage } from 'src/components/WarningMessage'
import { LocationPath } from 'src/router/types'

import {
  DATE_RANGE_FILTER,
  SITE_FILTER,
  useBlockUtilizationFilters,
} from './useBlockUtilizationFilters'

export const BlockUtilizationFilters = ({
  sites,
  title,
  hideSites = false,
  verifyReport = false,
}: {
  title: string
  sites?: SiteShape[]
  hideSites?: boolean
  verifyReport?: boolean
}) => {
  const { onSearch, selectedDateRange, selectedSiteId } =
    useBlockUtilizationFilters()

  const [monthQuarterPickerValues, setMonthPickerValues] = useState<
    [DateTime, DateTime]
  >([
    DateTime.fromISO(selectedDateRange[0]),
    DateTime.fromISO(selectedDateRange[1]),
  ])

  const warningMessage = useMemo(() => {
    if (selectedDateRange) {
      const startDate = DateTime.fromISO(selectedDateRange[0])
      const endDate = DateTime.fromISO(selectedDateRange[1])
      const lastDayOfMonth = endDate.endOf('month')

      if (!endDate.hasSame(lastDayOfMonth, 'day')) {
        const monthName = endDate.toFormat('LLLL')
        const startStr = startDate.toFormat('LLLL d')
        const endStr = endDate.toFormat('d')
        const releaseDate = endDate
          .plus({ months: 1 })
          .startOf('month')
          .toFormat('LLLL d')

        return (
          <>
            <H6
              css={{
                color: theme.palette.text.secondary,
              }}
            >
              {monthName} in progress.
            </H6>
            <P3
              css={{
                color: theme.palette.text.secondary,
              }}
            >
              Viewing data from {startStr}–{endStr}. Final month data will be
              available on {releaseDate}
            </P3>
          </>
        )
      }
    }
    return undefined
  }, [selectedDateRange])

  const [showWarning, setShowWarning] = useState<boolean>(true)
  return (
    <div
      css={{
        display: 'flex',
        gap: remSpacing.medium,
        flexDirection: 'column',
        width: '100%',
      }}
    >
      <div
        css={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <H3>{title}</H3>
        {verifyReport && (
          <ButtonLink
            size="sm"
            to={{
              pathname: generatePath(LocationPath.BlockUtilizationVerifyReport),
              search: new URLSearchParams({
                'dateRange[0]': selectedDateRange[0],
                'dateRange[1]': selectedDateRange[1],
                site: selectedSiteId,
              }).toString(),
            }}
          >
            <Edit size="sm" />
            <H6>Verify report</H6>
          </ButtonLink>
        )}
      </div>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.medium,
        }}
      >
        <FilterLabel label="Date range">
          <MonthRangePickerWithQuarters
            name="monthPicker"
            value={[monthQuarterPickerValues[0], monthQuarterPickerValues[1]]}
            onChange={(dates) => {
              if (Array.isArray(dates) && dates[0] && dates[1]) {
                setMonthPickerValues([dates[0], dates[1]])
              }
            }}
            onClose={() => {
              setMonthPickerValues([
                DateTime.fromISO(selectedDateRange[0]),
                DateTime.fromISO(selectedDateRange[1]),
              ])
            }}
            onDone={(dates) => {
              if (Array.isArray(dates) && dates[0] && dates[1]) {
                onSearch({
                  [DATE_RANGE_FILTER]: [dates[0].toISO(), dates[1].toISO()],
                })
              }
            }}
            maxDate={DateTime.now().minus({ day: 1 })}
            maxMonthRange={3}
          />
        </FilterLabel>
        {!hideSites && (
          <FilterLabel label="Site">
            <SitesFilter
              label="Select a site"
              sites={sites ?? []}
              selectedSiteIds={selectedSiteId}
              onChangeSites={(siteId) => {
                onSearch({ [SITE_FILTER]: siteId ?? '' })
              }}
              disableSelectAllOption
              disableSearch
            />
          </FilterLabel>
        )}
      </div>
      {showWarning && warningMessage && (
        <WarningMessage
          css={{
            width: '100%',
            paddingRight: remSpacing.small,
          }}
        >
          <div
            css={{
              display: 'flex',
              justifyContent: 'space-between',
              alignSelf: 'stretch',
            }}
          >
            <div
              css={{
                display: 'flex',
                gap: remSpacing.xsmall,
                alignItems: 'center',
              }}
            >
              <CalendarIcon color={theme.palette.gray[50]} size="xs" />
              {warningMessage}
            </div>
            <Button
              color="black"
              appearance="link"
              onClick={() => setShowWarning(false)}
              buttonType={'icon'}
              css={{ padding: 0 }}
            >
              <Close css={{ color: theme.palette.gray[60] }} size={'sm'} />
            </Button>
          </div>
        </WarningMessage>
      )}
    </div>
  )
}
