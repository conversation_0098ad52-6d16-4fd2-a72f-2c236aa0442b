import {
  ActionFunctionArgs,
  generatePath,
  redirect,
  replace,
} from 'react-router'

import { parseWithYup } from '@conform-to/yup'
import { DateTime } from 'luxon'
import * as yup from 'yup'

import {
  BlockTimeOverrideUpsertInput,
  CaseToBlockOverrideUpsertInput,
} from 'src/__generated__/globalTypes'
import {
  UpsertBlockTimeOverrides,
  UpsertBlockTimeOverridesVariables,
  UpsertCaseToBlockOverrides,
  UpsertCaseToBlockOverridesVariables,
} from 'src/pages/BlockUtilizationManagement/__generated__'
import {
  UPSERT_BLOCK_TIME_OVERRIDES,
  UPSERT_CASES_TO_BLOCKS_OVERRIDES,
} from 'src/pages/BlockUtilizationManagement/queries'
import { getRouteContext, LocationPath } from 'src/router/types'

const schema = yup.object({
  blockTimeUpdates: yup.array().of(
    yup.object({
      blockTimeId: yup.string().required(),
      availableMinutes: yup.number().required(),
      note: yup.string().required(),
    })
  ),
  casesToAdd: yup.array().of(
    yup.object({
      caseId: yup.string().required(),
      date: yup.string().required(),
    })
  ),
})

export const verifyReportAction = async (
  { request, params }: ActionFunctionArgs,
  context: unknown
) => {
  const formData = await request.formData()
  const submission = parseWithYup(formData, { schema })
  if (submission.status !== 'success') {
    return submission.reply()
  }

  const blockId = params.blockId
  if (!blockId) {
    throw replace(LocationPath.BlockUtilization)
  }
  const { apolloClient, user } = getRouteContext(context)

  const caseToBlockOverrides: CaseToBlockOverrideUpsertInput[] = (
    submission.value.casesToAdd || []
  ).map((caseToAdd) => {
    return {
      caseId: caseToAdd.caseId,
      blockId,
      blockDate: caseToAdd.date,
      note: `case was added to block ${blockId} on ${DateTime.now()}`,
      userId: user.userId,
    }
  })
  const { data } = await apolloClient.mutate<
    UpsertCaseToBlockOverrides,
    UpsertCaseToBlockOverridesVariables
  >({
    mutation: UPSERT_CASES_TO_BLOCKS_OVERRIDES,
    variables: {
      input: caseToBlockOverrides,
    },
    update: (cache) => {
      // the blockUtilizations have been updated with this override update so in order to reload the blockUtilizations with the correct data we need to evict the cache
      cache.evict({ fieldName: 'blockUtilizations' })
    },
  })

  const blockTimeOverrides: BlockTimeOverrideUpsertInput[] = (
    submission.value.blockTimeUpdates || []
  ).map((blockTimeUpdate) => {
    return {
      blockTimeId: blockTimeUpdate.blockTimeId,
      blockTimeMinutes: blockTimeUpdate.availableMinutes,
      note: blockTimeUpdate.note,
      userId: user.userId,
    }
  })

  const { data: blockTimeData } = await apolloClient.mutate<
    UpsertBlockTimeOverrides,
    UpsertBlockTimeOverridesVariables
  >({
    mutation: UPSERT_BLOCK_TIME_OVERRIDES,
    variables: {
      input: blockTimeOverrides,
    },
    update: (cache) => {
      // the blockUtilizations have been updated with this override update so in order to reload the blockUtilizations with the correct data we need to evict the cache
      cache.evict({ fieldName: 'blockUtilizations' })
    },
  })

  if (
    data?.caseToBlockOverridesUpsert?.success &&
    blockTimeData?.blockTimeOverridesUpsert?.success
  ) {
    const requestURL = new URL(request.url)
    return redirect(
      generatePath(
        `${LocationPath.BlockUtilization}/${LocationPath.BlockUtilizationForBlock}`,
        { blockId }
      ) +
        '?' +
        requestURL.searchParams.toString()
    )
  }
  return submission.reply({
    formErrors: [`Failed to add cases and update available block times.`],
  })
}
