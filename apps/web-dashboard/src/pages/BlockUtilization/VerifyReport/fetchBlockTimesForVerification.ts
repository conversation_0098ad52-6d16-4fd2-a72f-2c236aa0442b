import { ApolloClient, NormalizedCacheObject } from '@apollo/client'
import { DateTime } from 'luxon'

import { logger } from 'src/utils/exceptionLogging'

import { GetBlockTimesInfo, GetBlockTimesInfoVariables } from '../__generated__'
import { GET_BLOCK_TIMES_INFO } from '../queries'
import { BlockTimeAndReleases } from '../types'

export const fetchBlockTimesForVerification = async (
  client: ApolloClient<NormalizedCacheObject>,
  {
    selectedDateRange,
    selectedBlockId,
    timezone,
  }: {
    selectedDateRange: string[]
    selectedBlockId: string
    timezone: string
  }
) => {
  const { data, error } = await client.query<
    GetBlockTimesInfo,
    GetBlockTimesInfoVariables
  >({
    query: GET_BLOCK_TIMES_INFO,
    variables: {
      query: {
        minEndTime: selectedDateRange[0],
        maxStartTime: selectedDateRange[1],
        blockId: selectedBlockId,
      },
    },
  })
  if (error) {
    logger.log(
      `Error getting block time data for block ${selectedBlockId}: ${error.message}`
    )
    throw error
  }

  const blockTimes: { [blockTimeId: string]: BlockTimeAndReleases } = {}

  data.blockTimes.edges.forEach(({ node }) => {
    const blockTimeEntry: BlockTimeAndReleases = {
      blockTimeId: node.id,
      roomName: node.room.name,
      startTime: DateTime.fromISO(node.startTime, {
        zone: timezone,
      }),
      endTime: DateTime.fromISO(node.endTime, {
        zone: timezone,
      }),
      releases: node.releases.map((release) => ({
        releasedTime: DateTime.fromISO(release.releasedTime, {
          zone: timezone,
        }),
        startTime: DateTime.fromISO(release.startTime, {
          zone: timezone,
        }),
        endTime: DateTime.fromISO(release.endTime, {
          zone: timezone,
        }),
      })),
    }
    blockTimes[node.id] = blockTimeEntry
  })

  return blockTimes
}
