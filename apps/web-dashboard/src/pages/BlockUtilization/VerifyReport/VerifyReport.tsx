import { FormEvent, Suspense, useMemo, useRef, useState } from 'react'
import {
  Await,
  Form,
  generatePath,
  LoaderFunctionArgs,
  replace,
  useActionData,
  useAsyncValue,
  useLoaderData,
  useNavigate,
  useRouteLoaderData,
  useSubmit,
} from 'react-router'

import { useForm } from '@conform-to/react'

import {
  Button,
  ButtonLink,
  Caps2,
  Caps2Bold,
  Dialog,
  InputNumber,
  InputText,
  P2,
  P3,
  pxSpacing,
  remSpacing,
  SelectToggleIcon,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
  Union,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import LoadingOverlay from 'src/components/LoadingOverlay'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { defaultTimezone } from 'src/Contexts'
import { fetchCasesToBlocks } from 'src/pages/BlockUtilizationManagement/fetchCasesToBlocks'
import { getRoute<PERSON>ontext, LocationPath } from 'src/router/types'

import { DEFAULT_TURNOVER_DURATION_IN_MINUTES } from '../../BlockUtilizationManagement/constants'
import { CaseToBlockType } from '../../BlockUtilizationManagement/types'
import { BlockUtilizationErrorBoundary } from '../BlockUtilizationErrorBoundary'
import { loader as blockUtilizationForBlockLoader } from '../BlockUtilizationForBlock'
import { CasesTable } from '../CasesTable'
import { CASE_DATE_FORMAT } from '../constants'
import { DailyUtilizationTable } from '../DailyUtilizationTable'
import { CaseForBlock, DailyBlockUtilizationByBlock } from '../types'
import {
  castParams,
  useBlockUtilizationFilters,
} from '../useBlockUtilizationFilters'
import { fetchBlockTimesForVerification } from './fetchBlockTimesForVerification'

export const loader = async (
  { request, params }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationDashboard) {
    throw replace(LocationPath.Home)
  }
  const selectedBlockId = params.blockId
  const requestURL = new URL(request.url)
  const searchParams = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange } = castParams(searchParams)
  if (!selectedSiteId || !selectedDateRange || !selectedBlockId) {
    throw replace(LocationPath.BlockUtilization)
  }

  const allCasesForBlockPromise = fetchCasesToBlocks(apolloClient, {
    selectedDateRange,
    selectedSiteId,
    timezone:
      user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
      defaultTimezone,
    selectedBlockId,
  })

  const allBlockTimesPromise = fetchBlockTimesForVerification(apolloClient, {
    selectedDateRange,
    selectedBlockId,
    timezone:
      user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
      defaultTimezone,
  })

  return {
    allBlockTimesPromise,
    allCasesForBlockPromise,
    selectedBlockId,
  }
}

export const VerifyReportLayout = () => {
  const { blockUtilizationPromise, blockName } = useRouteLoaderData(
    'blockUtilizationForBlock'
  ) as Awaited<ReturnType<typeof blockUtilizationForBlockLoader>>

  const { allCasesForBlockPromise, allBlockTimesPromise, selectedBlockId } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  const dataRef = useRef(
    Promise.all([
      blockUtilizationPromise,
      allCasesForBlockPromise,
      allBlockTimesPromise,
    ])
  )

  return (
    <PageContentTemplate title="Verify Utilization">
      <Suspense fallback={<LoadingOverlay />}>
        <Await
          errorElement={<BlockUtilizationErrorBoundary />}
          resolve={dataRef.current}
        >
          <VerifyReport blockName={blockName} blockId={selectedBlockId} />
        </Await>
      </Suspense>
    </PageContentTemplate>
  )
}

const VERIFY_REPORT_MODAL = {
  AddCasesToBlock: 'AddCasesToBlock',
  AdjustBlockDay: 'AdjustBlockDay',
  Confirmation: 'Confirmation',
} as const
type VerifyReportModal =
  (typeof VERIFY_REPORT_MODAL)[keyof typeof VERIFY_REPORT_MODAL]

const VerifyReport = ({
  blockName,
  blockId,
}: {
  blockName: string
  blockId: string
}) => {
  const [
    { dailyBlockUtilizations, casesForBlock },
    { casesForBlocks },
    blockTimes,
  ] = useAsyncValue() as [
    Awaited<
      Awaited<
        ReturnType<typeof blockUtilizationForBlockLoader>
      >['blockUtilizationPromise']
    >,
    Awaited<Awaited<ReturnType<typeof loader>>['allCasesForBlockPromise']>,
    Awaited<Awaited<ReturnType<typeof loader>>['allBlockTimesPromise']>,
  ]

  const [openModal, setOpenModal] = useState<VerifyReportModal | undefined>(
    undefined
  )
  const [selectedCaseIds, setSelectedCaseIds] = useState<Set<string>>(new Set())
  const [blockTimeUpdates, setBlockTimeUpdates] = useState<{
    [blockTimeId: string]: {
      availableMinutes: number
      note?: string
    }
  }>({})
  const [currentBlockTimeUpdate, setCurrentBlockTimesUpdate] = useState<
    | {
        [blockTimeId: string]: {
          availableMinutes: number
          note?: string
        }
      }
    | undefined
  >(undefined)
  const [currentBlockTimeUpdateError, setCurrentBlockTimesUpdateError] =
    useState<boolean>(false)
  const [selectedBlockUtilization, setSelectedBlockUtilization] = useState<
    DailyBlockUtilizationByBlock | undefined
  >(undefined)

  const editOnAddCasesClick = () => {
    setOpenModal(VERIFY_REPORT_MODAL.AddCasesToBlock)
  }
  const editOnBlockUtilizationClick = (
    blockUtilization: DailyBlockUtilizationByBlock
  ) => {
    setOpenModal(VERIFY_REPORT_MODAL.AdjustBlockDay)
    setSelectedBlockUtilization(blockUtilization)
  }

  const caseIdsForBlock = casesForBlock.map(
    (caseForBlock) => caseForBlock.caseId
  )
  const allCasesToAdd = useMemo(() => {
    return casesForBlocks.filter(
      (caseToBlock: CaseToBlockType) =>
        !caseIdsForBlock.includes(caseToBlock.caseId)
    )
  }, [casesForBlocks, caseIdsForBlock])
  const { selectedDateRange, selectedSiteId } = useBlockUtilizationFilters()

  const navigate = useNavigate()
  const submit = useSubmit()
  const lastResult = useActionData()
  const [form] = useForm({ lastResult })
  const handleManualSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const formData = new FormData()
    Object.entries(blockTimeUpdates).forEach(
      ([blockTimeId, blockTimeUpdate], index) => {
        formData.append(
          `blockTimeUpdates[${index}].availableMinutes`,
          blockTimeUpdate.availableMinutes.toString()
        )
        formData.append(
          `blockTimeUpdates[${index}].note`,
          blockTimeUpdate?.note ?? ''
        )
        formData.append(`blockTimeUpdates[${index}].blockTimeId`, blockTimeId)
      }
    )

    addedCases.forEach(({ date, caseId }, index) => {
      formData.append(`casesToAdd[${index}].caseId`, caseId)
      formData.append(`casesToAdd[${index}].date`, date.toISODate())
    })

    const path = generatePath(
      `${LocationPath.BlockUtilization}/${LocationPath.BlockUtilizationForBlock}/${LocationPath.BlockUtilizationVerifyReport}`,
      {
        blockId,
      }
    )

    const searchParams = new URLSearchParams({
      'dateRange[0]': selectedDateRange[0],
      'dateRange[1]': selectedDateRange[1],
      site: selectedSiteId,
    }).toString()

    submit(formData, {
      method: 'post',
      action: `${path}?${searchParams}`,
    })
  }

  const toggleSelection = (caseId: string) => {
    setSelectedCaseIds((prev) => {
      const next = new Set(prev)
      next.has(caseId) ? next.delete(caseId) : next.add(caseId)
      return next
    })
  }

  const addedCases: CaseForBlock[] = useMemo(() => {
    if (selectedCaseIds.size > 0) {
      return casesForBlocks
        .filter(({ caseId }) => selectedCaseIds.has(caseId))
        .map((caseToBlock) => {
          const scheduledDurationSeconds = caseToBlock.scheduledEndTime
            .diff(caseToBlock.scheduledStartTime)
            .as('seconds')
          return {
            actualDurationSeconds: (caseToBlock?.actualMinutes ?? 0) * 60,
            caseId: caseToBlock.caseId,
            date: caseToBlock.scheduledEndTime,
            externalCaseId: caseToBlock.externalId,
            procedures: caseToBlock.procedures,
            scheduledDurationSeconds,
            status: caseToBlock.status,
            utilizedDurationSeconds: (caseToBlock.caseMinutes ?? 0) * 60,
            turnoverDurationSeconds:
              (caseToBlock.utilizedTurnoverMinutes ??
                DEFAULT_TURNOVER_DURATION_IN_MINUTES) * 60,
            override: true,
          }
        })
    }
    return []
  }, [selectedCaseIds, casesForBlocks])

  const blockUtilizationForBlockPath = {
    pathname: generatePath(
      `${LocationPath.BlockUtilization}/${LocationPath.BlockUtilizationForBlock}`,
      {
        blockId,
      }
    ),
    search: new URLSearchParams({
      'dateRange[0]': selectedDateRange[0],
      'dateRange[1]': selectedDateRange[1],
      site: selectedSiteId,
    }).toString(),
  }
  const dailyBlockUtilizationsPlusOverrides = useMemo(() => {
    return dailyBlockUtilizations.map((dailyBlockUtilization) => {
      return {
        ...dailyBlockUtilization,
        availableSecondsByBlockTimeId:
          dailyBlockUtilization.availableSecondsByBlockTimeId.map(
            (availableSecondsForBlockTime) => {
              if (
                availableSecondsForBlockTime.blockTimeId in blockTimeUpdates
              ) {
                const override =
                  blockTimeUpdates[availableSecondsForBlockTime.blockTimeId]
                return {
                  ...availableSecondsForBlockTime,
                  overridden: true,
                  availableSeconds: override.availableMinutes * 60,
                }
              }
              return availableSecondsForBlockTime
            }
          ),
      }
    })
  }, [dailyBlockUtilizations, blockTimeUpdates])

  return (
    <>
      <div
        css={{
          display: 'flex',
          gap: remSpacing.gutter,
          flexDirection: 'column',
        }}
      >
        <CasesTable
          blockName={blockName}
          casesForBlock={casesForBlock.concat(addedCases)}
          editView
          editOnClick={editOnAddCasesClick}
        />
        <DailyUtilizationTable
          blockName={blockName}
          blockUtilizations={dailyBlockUtilizationsPlusOverrides}
          editView
          editOnClick={editOnBlockUtilizationClick}
        />
        <Dialog
          isOpen={openModal == VERIFY_REPORT_MODAL.AddCasesToBlock}
          onClose={() => {
            setSelectedCaseIds(new Set())
            setOpenModal(undefined)
          }}
          title="Add cases to block report"
          overflow="scroll"
          isFullScreen
        >
          <div
            css={{
              border: `1px solid ${theme.palette.gray[30]}`,
              borderRadius: pxSpacing.xsmall,
              padding: `${remSpacing.small} ${remSpacing.gutter}`,
              display: 'flex',
              alignSelf: 'stretch',
              background: theme.palette.gray.background,
            }}
          >
            <P3 color={theme.palette.gray[60]}>
              Cases added will be counted towards block utilization
            </P3>
          </div>
          <Table>
            <THead>
              <TR>
                <TH />
                <TH>Case Id</TH>
                <TH>Date</TH>
                <TH>Primary Surgeon</TH>
                <TH>Procedure</TH>
                <TH>Actual duration (min)</TH>
              </TR>
            </THead>
            <TBody>
              {allCasesToAdd.map((caseForBlock) => {
                return (
                  <TR key={caseForBlock.caseId}>
                    <TD>
                      <Button
                        appearance="link"
                        onClick={() => toggleSelection(caseForBlock.caseId)}
                      >
                        <SelectToggleIcon
                          type={
                            selectedCaseIds.has(caseForBlock.caseId)
                              ? 'selected'
                              : undefined
                          }
                          size={'sm'}
                        />
                      </Button>
                    </TD>
                    <TD>{caseForBlock.externalId}</TD>
                    <TD>
                      {caseForBlock.scheduledEndTime.toFormat(CASE_DATE_FORMAT)}
                    </TD>
                    <TD>{caseForBlock.primaryStaffNames}</TD>
                    <TD>{caseForBlock.procedures.join(', ')}</TD>
                    <TD>{caseForBlock.caseMinutes}</TD>
                  </TR>
                )
              })}
            </TBody>
          </Table>
          <div
            css={{
              paddingTop: remSpacing.gutter,
              display: 'flex',
              justifyContent: 'flex-end',
              gap: remSpacing.xsmall,
            }}
          >
            <Button
              color="alternate"
              onClick={() => {
                setSelectedCaseIds(new Set())
                setOpenModal(undefined)
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setOpenModal(undefined)
              }}
            >
              Add case(s)
            </Button>
          </div>
        </Dialog>
        <Dialog
          isOpen={openModal == VERIFY_REPORT_MODAL.AdjustBlockDay}
          onClose={() => setOpenModal(undefined)}
          title="Adjust block day"
        >
          <div
            css={{
              display: 'flex',
              flexDirection: 'column',
              gap: remSpacing.gutter,
            }}
          >
            <div
              css={{
                border: `1px solid ${theme.palette.gray[30]}`,
                borderRadius: pxSpacing.xsmall,
                padding: `${remSpacing.small} ${remSpacing.gutter}`,
                display: 'flex',
                alignSelf: 'stretch',
                background: theme.palette.gray.background,
              }}
            >
              <P3 color={theme.palette.gray[60]}>
                The denominator for block utilization calculations will be
                impacted by adjusting the day&apos;s block time
              </P3>
            </div>
            <div
              css={{
                display: 'flex',
                gap: remSpacing.xsmall,
                alignItems: 'center',
              }}
            >
              <Union size="xs" color={theme.palette.gray[50]} />
              <Caps2Bold>block</Caps2Bold>
              <Caps2>{selectedBlockUtilization?.blockName}</Caps2>
            </div>
            <div
              css={{
                display: 'flex',
                flexDirection: 'column',
                gap: remSpacing.xsmall,
              }}
            >
              {selectedBlockUtilization &&
                selectedBlockUtilization?.availableSecondsByBlockTimeId.map(
                  (availableSecondsForBlockTime) => {
                    const blockTimeId = availableSecondsForBlockTime.blockTimeId
                    return (
                      <>
                        {blockTimeId in blockTimes && (
                          <div
                            css={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: remSpacing.xxsmall,
                            }}
                          >
                            <P3>
                              Start time:{' '}
                              {blockTimes[blockTimeId].startTime.toFormat(
                                'hh:mm a'
                              )}{' '}
                              End time:{' '}
                              {blockTimes[blockTimeId].endTime.toFormat(
                                'hh:mm a'
                              )}{' '}
                              Room name: {blockTimes[blockTimeId].roomName}
                            </P3>
                            {blockTimes[blockTimeId].releases.map((release) => {
                              return (
                                <P3 key={release.startTime.toISO()}>
                                  {release.startTime.toFormat('hh:mm a')} -{' '}
                                  {release.endTime.toFormat('hh:mm a')} released
                                  at{' '}
                                  {release.releasedTime.toFormat(
                                    'MMMM dd, yyyy hh:mm a'
                                  )}
                                </P3>
                              )
                            })}
                          </div>
                        )}
                        <InputNumber
                          name={`blockTimeUpdates."${availableSecondsForBlockTime.blockTimeId}".availableMinutes`}
                          label="Available Minutes"
                          defaultValue={Math.floor(
                            availableSecondsForBlockTime.availableSeconds / 60
                          )}
                          onChange={(value) => {
                            if (value !== undefined) {
                              const blockTimeUpdate = currentBlockTimeUpdate
                                ? currentBlockTimeUpdate[
                                    availableSecondsForBlockTime.blockTimeId
                                  ]
                                : {}
                              setCurrentBlockTimesUpdate({
                                ...currentBlockTimeUpdate,
                                [availableSecondsForBlockTime.blockTimeId]: {
                                  ...blockTimeUpdate,
                                  availableMinutes: value,
                                },
                              })
                            }
                          }}
                        />
                        <InputText
                          name={`blockTimeUpdates."${availableSecondsForBlockTime.blockTimeId}".note`}
                          label="Reason for adjustment"
                          placeholder="Add reason ..."
                          onChange={(e) => {
                            const blockTimeUpdate = currentBlockTimeUpdate
                              ? currentBlockTimeUpdate[
                                  availableSecondsForBlockTime.blockTimeId
                                ]
                              : undefined
                            if (
                              e.target.value &&
                              blockTimeUpdate?.availableMinutes
                            ) {
                              setCurrentBlockTimesUpdateError(false)
                              setCurrentBlockTimesUpdate({
                                ...currentBlockTimeUpdate,
                                [availableSecondsForBlockTime.blockTimeId]: {
                                  ...blockTimeUpdate,
                                  note: e.target.value,
                                },
                              })
                            }
                          }}
                          multiline
                          rows={2}
                          required
                        />
                      </>
                    )
                  }
                )}
              {currentBlockTimeUpdateError && (
                <P3 css={{ color: theme.palette.red[50] }}>
                  Please enter a reason
                </P3>
              )}
            </div>
            <div
              css={{
                paddingTop: remSpacing.gutter,
                display: 'flex',
                justifyContent: 'flex-end',
                gap: remSpacing.xsmall,
              }}
            >
              <Button
                color="alternate"
                onClick={() => {
                  setCurrentBlockTimesUpdate(undefined)
                  setOpenModal(undefined)
                  setCurrentBlockTimesUpdateError(false)
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  if (currentBlockTimeUpdate) {
                    const updatesToMake = { ...blockTimeUpdates }
                    for (const [blockTimeId, update] of Object.entries(
                      currentBlockTimeUpdate
                    )) {
                      if (update.availableMinutes && !update?.note) {
                        setCurrentBlockTimesUpdateError(true)
                        return
                      }
                      updatesToMake[blockTimeId] = update
                    }
                    setBlockTimeUpdates({
                      ...updatesToMake,
                    })
                  }
                  setCurrentBlockTimesUpdate(undefined)
                  setOpenModal(undefined)
                }}
              >
                Done
              </Button>
            </div>
          </div>
        </Dialog>
        <Form onSubmit={handleManualSubmit} id="verify-report">
          {form.errors && (
            <div css={{ color: theme.palette.red[50] }}>
              {Array.isArray(form.errors)
                ? form.errors.map((error, index) => (
                    <div key={index}>{error}</div>
                  ))
                : form.errors}
            </div>
          )}
          <div
            css={{
              display: 'flex',
              justifyContent: 'flex-end',
              gap: remSpacing.small,
            }}
          >
            <Button
              color="alternate"
              onClick={() => {
                if (
                  selectedCaseIds.size > 0 ||
                  Object.entries(blockTimeUpdates).length > 0
                ) {
                  setOpenModal(VERIFY_REPORT_MODAL.Confirmation)
                } else {
                  navigate(blockUtilizationForBlockPath)
                }
              }}
              type="button"
            >
              Discard changes
            </Button>
            <Button type="submit" form="verify-report">
              Save
            </Button>
          </div>
          <Dialog
            title="Save your changes?"
            isOpen={
              openModal === VERIFY_REPORT_MODAL.Confirmation &&
              !(form.errors && form.errors.length > 0)
            }
            onClose={() => setOpenModal(undefined)}
          >
            <div
              css={{
                display: 'flex',
                gap: remSpacing.small,
                flexDirection: 'column',
              }}
            >
              <P2 color={theme.palette.gray[70]}>
                You have unsaved changes. Do you want to save?
              </P2>
              <div
                css={{
                  display: 'flex',
                  gap: remSpacing.xsmall,
                  justifyContent: 'flex-end',
                }}
              >
                <ButtonLink
                  color="alternate"
                  type="button"
                  to={blockUtilizationForBlockPath}
                  onClick={() => {
                    setOpenModal(undefined)
                    setSelectedCaseIds(new Set())
                  }}
                >
                  Discard changes
                </ButtonLink>
                <Button type="submit" form="verify-report">
                  Save
                </Button>
              </div>
            </div>
          </Dialog>
        </Form>
      </div>
    </>
  )
}
