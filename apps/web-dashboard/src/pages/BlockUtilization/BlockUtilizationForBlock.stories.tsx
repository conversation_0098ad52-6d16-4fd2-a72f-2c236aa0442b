import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { ApellaRouteContext } from 'src/router/types'
import { DEFAULT_STORYBOOK_USER } from 'src/test/constants'
import MockLDProvider from 'src/test/MockLDProvider'

import {
  GetBlockInfoDocument,
  GetBlockUtilizationForBlockViewDocument,
} from './__generated__'
import {
  BlockUtilizationForBlockLayout,
  loader as blockUtilizationForBlockLoader,
} from './BlockUtilizationForBlock'

const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  cube: cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  }),
  user: DEFAULT_STORYBOOK_USER,
})

const meta: Meta<typeof BlockUtilizationForBlockLayout> = {
  title: 'Pages/BlockUtilizationDashboard/DashboardForBlock',
  component: BlockUtilizationForBlockLayout,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <CubeProvider cubeApi={parameters.reactRouter.context.cube}>
            <ApolloProvider
              client={parameters.reactRouter.context.apolloClient}
            >
              <Story />
            </ApolloProvider>
          </CubeProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof BlockUtilizationForBlockLayout>

const routing = {
  id: 'blockUtilizationForBlock',
  path: '/block-utilization/:blockId',
  handle: 'BlockUtilizationForBlockLayout',
  Component: BlockUtilizationForBlockLayout,
  loader: blockUtilizationForBlockLoader,
}

const BlockUtilizationForBlockViewData = {
  data: {
    blockUtilizations: [
      {
        availableSeconds: 30 * 60,
        totalScheduledCaseSeconds: 25 * 60,
        utilizedSeconds: 50 * 60,
        totalBlockSeconds: 40 * 60,
        availableSecondsByBlockTimeId: [
          {
            availableSeconds: 30 * 60,
            blockTimeId: 'block-time-1',
            overridden: false,
            __typename: 'AvailableSecondsForBlockTime' as const,
          },
        ],
        casesForBlockDay: [
          {
            caseId: '1',
            actualCaseSeconds: 20 * 60,
            utilizedCaseSeconds: 15 * 60,
            overridden: false,
            overriddenTurnoverSeconds: null,
            overriddenUtilizedCaseSeconds: null,
            scheduledCase: {
              externalCaseId: '1',
              status: 'scheduled',
              scheduledEndTime: '2025-02-05T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-05T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #1 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
          {
            caseId: '2',
            actualCaseSeconds: null,
            utilizedCaseSeconds: 15 * 60,
            overridden: true,
            overriddenUtilizedCaseSeconds: 99 * 60,
            overriddenTurnoverSeconds: null,
            scheduledCase: {
              externalCaseId: '2',
              status: 'cancelled',
              scheduledEndTime: '2025-02-06T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-06T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #1 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-1',
        date: '2025-02-06',
        block: {
          name: 'block 1',
          surgeonIds: ['surgeon-1'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
      {
        availableSeconds: 40 * 60,
        totalScheduledCaseSeconds: 25 * 60,
        utilizedSeconds: 20 * 60,
        totalBlockSeconds: 40 * 60,
        availableSecondsByBlockTimeId: [
          {
            availableSeconds: 40 * 60,
            blockTimeId: 'block-time-2',
            overridden: false,
            __typename: 'AvailableSecondsForBlockTime' as const,
          },
        ],
        casesForBlockDay: [
          {
            caseId: '2',
            utilizedCaseSeconds: 15 * 60,
            actualCaseSeconds: 20 * 60,
            overridden: false,
            overriddenTurnoverSeconds: null,
            overriddenUtilizedCaseSeconds: null,
            scheduledCase: {
              externalCaseId: '2',
              status: 'scheduled',
              scheduledEndTime: '2025-02-10T17:36:49.729884+00:00',
              scheduledStartTime: '2025-02-10T16:13:49.729884+00:00',
              primaryCaseProcedures: [
                {
                  procedure: {
                    name: 'procedure #2 name',
                    __typename: 'Procedure' as const,
                  },
                  __typename: 'CaseProcedure' as const,
                },
              ],
              __typename: 'ScheduledCase' as const,
            },
            __typename: 'CaseToBlock' as const,
          },
        ],
        blockId: 'block-id-1',
        date: '2025-02-10',
        block: {
          name: 'block 1',
          surgeonIds: ['surgeon-1'],
          __typename: 'Block' as const,
        },
        __typename: 'BlockUtilization' as const,
      },
    ],
  },
}
const msw = (
  wait: number,
  {
    getBlockUtilizationForBlockViewData = BlockUtilizationForBlockViewData,
  } = {}
) => ({
  handlers: [
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetBlockUtilizationForBlockViewDocument, async () => {
      await delay(wait)
      return HttpResponse.json(getBlockUtilizationForBlockViewData)
    }),
    graphql.query(GetBlockInfoDocument, async () => {
      await delay(wait)
      return HttpResponse.json({
        data: {
          block: {
            name: 'block 1',
            __typename: 'Block',
          },
        },
      })
    }),
  ],
})

const mswError = () => ({
  handlers: [
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetBlockUtilizationForBlockViewDocument, async () => {
      throw new Response('error getting data')
    }),
    graphql.query(GetBlockInfoDocument, async () => {
      return HttpResponse.json({
        data: {
          block: {
            name: 'block 1',
            __typename: 'Block',
          },
        },
      })
    }),
  ],
})

export const DashboardEnabled: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardByWeek: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
          blockSortType: 'byWeek',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardByDay: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
          blockSortType: 'byDay',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardNoBlocks: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      getBlockUtilizationForBlockViewData: { data: { blockUtilizations: [] } },
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardError: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: mswError(),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const DashboardNoCasesForBlock: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationDashboard: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      getBlockUtilizationForBlockViewData: {
        data: {
          blockUtilizations: [
            {
              availableSeconds: 40 * 60,
              totalScheduledCaseSeconds: 0,
              utilizedSeconds: 0,
              totalBlockSeconds: 50 * 60,
              casesForBlockDay: [],
              blockId: 'block-id-1',
              date: '2025-02-10',
              block: {
                name: 'block 1',
                surgeonIds: ['surgeon-1'],
                __typename: 'Block' as const,
              },
              availableSecondsByBlockTimeId: [
                {
                  availableSeconds: 40 * 60,
                  blockTimeId: 'block-time-1',
                  overridden: false,
                  __typename: 'AvailableSecondsForBlockTime' as const,
                },
              ],
              __typename: 'BlockUtilization' as const,
            },
          ],
        },
      },
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/block-id-1',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
