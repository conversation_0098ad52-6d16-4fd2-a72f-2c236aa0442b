import { gql } from '@apollo/client'

export const GET_BLOCK_UTILIZATIONS = gql`
  query GetBlockUtilization($query: BlockUtilizationInput!) {
    blockUtilizations(query: $query) {
      date
      blockId
      utilizedSeconds
      availableSeconds
      totalBlockSeconds
      totalScheduledCaseSeconds
      casesForBlockDay {
        overridden
        scheduledCase {
          status
        }
      }
      block {
        name
        surgeonIds
      }
    }
  }
`

export const GET_BLOCK_UTILIZATIONS_FOR_BLOCK_VIEW = gql`
  query GetBlockUtilizationForBlockView($query: BlockUtilizationInput!) {
    blockUtilizations(query: $query) {
      date
      blockId
      utilizedSeconds
      availableSeconds
      totalBlockSeconds
      totalScheduledCaseSeconds
      block {
        name
      }
      availableSecondsByBlockTimeId {
        blockTimeId
        availableSeconds
        overridden
      }
      casesForBlockDay {
        caseId
        actualCaseSeconds
        utilizedCaseSeconds
        overridden
        overriddenUtilizedCaseSeconds
        overriddenTurnoverSeconds
        scheduledCase {
          externalCaseId
          primaryCaseProcedures {
            procedure {
              name
            }
          }
          scheduledStartTime
          scheduledEndTime
          status
        }
      }
    }
  }
`

export const GET_BLOCK_INFO = gql`
  query GetBlockInfo($id: ID!) {
    block(id: $id) {
      name
    }
  }
`

export const GET_BLOCK_TIMES_INFO = gql`
  query GetBlockTimesInfo($query: BlockTimeQueryInput!) {
    blockTimes(query: $query) {
      edges {
        node {
          id
          blockId
          startTime
          endTime
          room {
            name
          }
          releases {
            releasedTime
            startTime
            endTime
          }
        }
      }
    }
  }
`
