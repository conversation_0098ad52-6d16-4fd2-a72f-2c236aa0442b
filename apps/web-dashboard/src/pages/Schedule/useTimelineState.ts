import { useMemo } from 'react'

import { useQuery } from '@apollo/client'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { chain } from 'lodash'
import { DateTime } from 'luxon'

import { useCurrentUser } from 'src/modules/user/hooks'
import { LiveStatus } from 'src/utils/status'
import { usePatientReadAccess } from 'src/utils/usePatientReadAccess'

import {
  CaseMatchingStatus,
  CaseType,
  RoomStatusName,
  TurnoverType,
} from '../../__generated__/globalTypes'
import { useTimezone } from '../../Contexts'
import { WebDashboardFeatureFlagSet } from '../../modules/feature/types'
import { usePolling } from '../../utils/usePolling'
import { SLOW_POLL_INTERVAL_MS } from '../Live/consts'
import {
  GetTimelineCaseData,
  GetTimelineCaseDataVariables,
} from './__generated__'
import { GET_TIMELINE_CASE_DATA } from './queries'
import { TimelineRoom } from './types'
import {
  computeConflictingLastNames,
  parseCases,
  parsePrimeTimeConfig,
  parseRoomStatus,
  parseTurnovers,
} from './useTimelineStateHelpers'

export interface TimelineState {
  getCases: () => void
  isLoading: boolean
  maxTime: string
  minTime: string
  rooms: TimelineRoom[]
  totalRoomsBeforeFiltering: number
  turnoverGoals?: {
    goalMinutes: number | null
    maxMinutes: number
  }[]
}
export interface UseTimelineStateOptions {
  caseMatchingStatuses?: Set<CaseMatchingStatus>
  includeTurnovers?: boolean
  roomIds?: string[]
  showClosedRooms?: boolean
  showDetails?: boolean
  siteIds?: string[]
  sortKeys?: LiveStatus[]
}

export const useTimelineState = ({
  minTime,
  maxTime,
  options,
  skip,
}: {
  minTime: string
  maxTime: string
  options?: UseTimelineStateOptions
  skip?: boolean
}): TimelineState => {
  const { timezone } = useTimezone()
  const { permissions } = useCurrentUser()
  const showPatientData = usePatientReadAccess()
  const { ehrEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const {
    siteIds,
    roomIds,
    includeTurnovers,
    caseMatchingStatuses,
    showClosedRooms = true,
    sortKeys,
  } = options ?? { showClosedRooms: true }

  const roomStatusFilter = showClosedRooms
    ? undefined
    : Object.values(RoomStatusName).filter(
        (status) => status !== RoomStatusName.CLOSED
      )

  const {
    loading: casesLoading,
    data: casesData,
    startPolling,
    stopPolling,
    refetch,
  } = useQuery<GetTimelineCaseData, GetTimelineCaseDataVariables>(
    GET_TIMELINE_CASE_DATA,
    {
      variables: {
        maxStartTime: maxTime,
        minEndTime: minTime,
        siteIds: siteIds ?? [],
        roomIds,
        caseMatchingStatuses: caseMatchingStatuses
          ? Array.from(caseMatchingStatuses)
          : undefined,
        includeStaffPlan: !!permissions?.caseStaffPlanReadEnabled,
        includeNotePlan: !!permissions?.caseNotePlanReadEnabled,
        includeTurnovers: includeTurnovers ?? false,
        includePatientData: showPatientData,
        includeEventNotifications:
          !!permissions?.notificationReadEnabled &&
          !!permissions?.contactInformationReadEnabled,
        includeEHRCases: ehrEnabled,
        statusFilter: roomStatusFilter,
      },
      skip: skip || !siteIds,
      // Allow partial data when loading the Schedule page loads, since it loads data from many
      // different sources and they're not all required to render the page.
      errorPolicy: 'all',
    }
  )
  usePolling({
    interval: SLOW_POLL_INTERVAL_MS,
    refetch,
    startPolling,
    stopPolling,
    skip,
  })

  const { rooms, totalRoomsBeforeFiltering } = useMemo(() => {
    if (!casesData?.sites.edges) {
      return { rooms: [], totalRoomsBeforeFiltering: 0 }
    }

    const conflictingLastNames = computeConflictingLastNames(casesData)

    const timelineRooms = casesData.sites.edges.flatMap(({ node: site }) =>
      site.rooms.edges.map<TimelineRoom>(({ node: room }) => {
        const siteObj = {
          id: site.id,
          name: site.name,
        }
        const turnoverGoals = site.turnoverGoals
        const primeTimeConfig = room.primeTimeConfig
        return {
          id: room.id,
          name: room.name,
          site: siteObj,
          since: DateTime.fromISO(room.status.since),
          status: parseRoomStatus(room, turnoverGoals),
          cases: parseCases(
            room,
            siteObj,
            timezone,
            conflictingLastNames,
            caseMatchingStatuses
          ).filter((c) => ehrEnabled || c.type !== CaseType.FORECAST),
          turnovers: parseTurnovers(room, timezone, turnoverGoals).filter(
            (t) => ehrEnabled || t.type === TurnoverType.COMPLETE
          ),
          primeTime: parsePrimeTimeConfig(minTime, timezone, primeTimeConfig),
          sortKey: room.sortKey,
        }
      })
    )

    const totalBeforeFiltering = timelineRooms.length

    const filteredRooms = chain(timelineRooms)
      .filter((r) => {
        const validRoom = !roomIds?.length || roomIds?.includes(r.id)
        const validSite = !!siteIds?.includes(r.site.id)
        return validRoom && validSite
      })
      .sortBy([
        (room) => {
          const priority = (sortKeys ?? []).indexOf(
            room.status.inProgressApellaCase?.status.name ?? room.status.name
          )
          return priority === -1 ? Number.MAX_SAFE_INTEGER : priority
        },
        (r) => r.sortKey ?? r.id,
      ])
      .value()

    return {
      rooms: filteredRooms,
      totalRoomsBeforeFiltering: totalBeforeFiltering,
    }
  }, [
    casesData,
    timezone,
    caseMatchingStatuses,
    minTime,
    roomIds,
    siteIds,
    sortKeys,
    ehrEnabled,
  ])

  const turnoverGoals = casesData?.sites.edges.flatMap(({ node: site }) => {
    return site.turnoverGoals
  })

  const isLoading = useMemo(() => casesLoading, [casesLoading])

  return {
    rooms,
    totalRoomsBeforeFiltering,
    isLoading,
    minTime,
    maxTime,
    getCases: refetch,
    turnoverGoals,
  }
}
