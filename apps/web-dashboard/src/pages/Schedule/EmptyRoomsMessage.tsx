import { useMemo } from 'react'

import { DateTime } from 'luxon'

import { H4, P2, remSpacing, Tile } from '@apella/component-library'
import { useTimezone } from 'src/Contexts'

import { useScheduleFilterContext } from './ScheduleFilterContext'

interface EmptyRoomsMessageProps {
  isLoading: boolean
}

export const EmptyRoomsMessage = ({ isLoading }: EmptyRoomsMessageProps) => {
  const { timezone } = useTimezone()
  const { minTime } = useScheduleFilterContext()

  const messageInfo = useMemo(() => {
    if (isLoading) {
      return null
    }

    const selectedDate = DateTime.fromISO(minTime).setZone(timezone)
    const isToday = selectedDate.hasSame(
      DateTime.now().setZone(timezone),
      'day'
    )
    const dateFormatted = selectedDate.toFormat('MMMM d, yyyy')

    return {
      type: 'no-rooms' as const,
      title: 'No rooms found',
      message: `No rooms are available for ${isToday ? 'today' : dateFormatted}.`,
      showToggle: false,
    }
  }, [isLoading, minTime, timezone])

  if (!messageInfo) {
    return null
  }

  return (
    <div
      css={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '300px',
        padding: remSpacing.large,
      }}
    >
      <Tile
        css={{
          maxWidth: '600px',
          textAlign: 'center',
        }}
        gutter={remSpacing.large}
      >
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            gap: remSpacing.medium,
            alignItems: 'center',
          }}
        >
          <H4>{messageInfo.title}</H4>
          <P2>{messageInfo.message}</P2>
        </div>
      </Tile>
    </div>
  )
}
