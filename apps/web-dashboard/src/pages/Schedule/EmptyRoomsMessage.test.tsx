import { ThemeProvider } from '@emotion/react'

import { render, screen } from '@testing-library/react'
import { DateTime } from 'luxon'
import { vi } from 'vitest'

import { theme } from '@apella/component-library'
import { TimezoneProvider } from 'src/Contexts'

import { EmptyRoomsMessage } from './EmptyRoomsMessage'
import { ScheduleFilterContext } from './ScheduleFilterContext'
import { TimeRange } from './types'

const createMockContext = (overrides = {}) => ({
  sites: [],
  rooms: [],
  onChangeSurgeons: vi.fn(),
  maxTime: '',
  minTime: DateTime.now().toISO(),
  onChangeDate: vi.fn(),
  onChangeSites: vi.fn(),
  onChangeRooms: vi.fn(),
  onToggleScheduled: vi.fn(),
  showResetFiltersButton: false,
  showScheduled: true,
  onChangeDailyMetric: vi.fn(),
  resetFilters: vi.fn(),
  onToggleShowClosedRooms: vi.fn(),
  onToggleShowMetrics: vi.fn(),
  showClosedRooms: true,
  showMetrics: true,
  sortKeys: [],
  onToggleSortKey: vi.fn(),
  onToggleFilters: vi.fn(),
  timeRange: TimeRange.DayBound,
  ...overrides,
})

const renderWithProviders = (
  component: React.ReactElement,
  contextOverrides = {},
  timezone = 'America/New_York'
) => {
  const mockContext = createMockContext(contextOverrides)

  return render(
    <ThemeProvider theme={theme}>
      <TimezoneProvider orgDefaultTimezone={timezone}>
        <ScheduleFilterContext.Provider value={mockContext}>
          {component}
        </ScheduleFilterContext.Provider>
      </TimezoneProvider>
    </ThemeProvider>
  )
}

describe('EmptyRoomsMessage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not render when loading', () => {
    const { container } = renderWithProviders(
      <EmptyRoomsMessage isLoading={true} />
    )

    expect(container.firstChild).toBeNull()
  })

  it('should show "No rooms found" when no rooms exist', () => {
    const today = DateTime.now().toISO()

    renderWithProviders(<EmptyRoomsMessage isLoading={false} />, {
      minTime: today,
    })

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
    expect(screen.queryByText('Show closed rooms')).not.toBeInTheDocument()
  })

  it('should show "No rooms found" for today', () => {
    const today = DateTime.now().toISO()

    renderWithProviders(<EmptyRoomsMessage isLoading={false} />, {
      minTime: today,
    })

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
    expect(
      screen.getByText(/No rooms are available for today/)
    ).toBeInTheDocument()
  })

  it('should show formatted date for non-today dates', () => {
    const futureDate = DateTime.now().plus({ days: 7 }).toISO()

    renderWithProviders(<EmptyRoomsMessage isLoading={false} />, {
      minTime: futureDate,
    })

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
    expect(screen.getByText(/\w+ \d+, \d{4}/)).toBeInTheDocument()
  })

  it('should handle timezone correctly', () => {
    const date = DateTime.fromISO('2025-04-22T10:00:00.000Z').toISO()

    renderWithProviders(
      <EmptyRoomsMessage isLoading={false} />,
      {
        minTime: date,
      },
      'America/Los_Angeles'
    )

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
  })

  it('should show "No rooms found" for past dates', () => {
    const pastDate = DateTime.now().minus({ days: 3 }).toISO()

    renderWithProviders(<EmptyRoomsMessage isLoading={false} />, {
      minTime: pastDate,
    })

    expect(screen.getByText('No rooms found')).toBeInTheDocument()
    expect(screen.queryByText('Show closed rooms')).not.toBeInTheDocument()
  })
})
