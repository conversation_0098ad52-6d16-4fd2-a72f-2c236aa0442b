import { useCallback, useMemo } from 'react'

import { Button, ClosedRoom, Tooltip } from '@apella/component-library'

import { useScheduleFilterContext } from './ScheduleFilterContext'

export const ShowClosedRoomsToggle = () => {
  const {
    showClosedRoomsUserPreference,
    isViewingPastDate,
    onToggleShowClosedRooms,
  } = useScheduleFilterContext()

  const onClick = useCallback(
    () => onToggleShowClosedRooms(!showClosedRoomsUserPreference),
    [onToggleShowClosedRooms, showClosedRoomsUserPreference]
  )

  const tooltipBody = useMemo(() => {
    if (isViewingPastDate) {
      return 'Inactive on historical data'
    }
    return showClosedRoomsUserPreference
      ? 'Hide closed rooms'
      : 'Show closed rooms'
  }, [isViewingPastDate, showClosedRoomsUserPreference])

  return (
    <Tooltip body={tooltipBody} placement="bottom">
      <Button
        color={showClosedRoomsUserPreference ? 'active-gray' : 'alternate'}
        buttonType="icon"
        onClick={onClick}
        disabled={isViewingPastDate}
      >
        <ClosedRoom size="sm" />
      </Button>
    </Tooltip>
  )
}
