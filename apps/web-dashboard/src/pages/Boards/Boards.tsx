import { useCallback, useEffect, useState } from 'react'
import { generatePath, Link, useLocation, useNavigate } from 'react-router'
import { toast } from 'react-toastify'

import {
  ApellaDateTimeFormats,
  ButtonLink,
  Edit,
  FlexContainer,
  fontWeights,
  P3,
  Plus,
  remSpacing,
  Table,
  TBody,
  TD,
  TH,
  THead,
  theme,
  TR,
} from '@apella/component-library'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { useSitesAndBoardConfigs } from 'src/modules/board/hooks/useSitesAndBoardConfigs'
import { BoardConfig } from 'src/modules/board/types'
import { useCurrentUser } from 'src/modules/user/hooks'
import { LocationPath } from 'src/router/types'

import { BoardEditResponse } from './types'

const BOARD_VIEW = {
  to: LocationPath.Boards,
  id: 'boards',
  display: 'Boards',
}

export const Boards = () => {
  const navigate = useNavigate()

  const location = useLocation()
  const locationSiteId = location.state?.exitSiteId

  const searchParams = new URLSearchParams(location.search)
  const siteId = searchParams.get('siteId') || undefined

  const [hasCheckedRedirect, setHasCheckedRedirect] = useState(false)

  const { permissions: uiPermissions } = useCurrentUser()
  const userHasEditPermission = uiPermissions?.bigBoardWriteEnabled ?? false

  const { sites, boardConfigs, loading } = useSitesAndBoardConfigs({ siteId })

  const maybeDoRedirect = useCallback(() => {
    const searchParams = new URLSearchParams(location.search)
    const boardEditResponse = searchParams.get('s') || undefined

    if (boardEditResponse !== undefined) {
      const toastId = 'boardEditResponseToast'

      const successToast = (action: string) =>
        toast.success(`Board ${action} successfully`, { toastId })
      const failureToast = (action: string) =>
        toast.error(`Error ${action} board configuration. Please Try again.`, {
          toastId,
        })

      if (boardEditResponse === `${BoardEditResponse.CreateSuccess}`) {
        successToast('created')
      } else if (boardEditResponse === `${BoardEditResponse.CreateFailure}`) {
        failureToast('creating')
      } else if (boardEditResponse === `${BoardEditResponse.UpdateSuccess}`) {
        successToast('updated')
      } else if (boardEditResponse === `${BoardEditResponse.UpdateFailure}`) {
        failureToast('updating')
      } else if (boardEditResponse === `${BoardEditResponse.DeleteSuccess}`) {
        successToast('deleted')
      } else if (boardEditResponse === `${BoardEditResponse.DeleteFailure}`) {
        failureToast('deleting')
      }

      searchParams.delete('s')
      navigate(
        {
          pathname: LocationPath.Boards,
          search: searchParams.toString(),
        },
        { replace: true }
      )
      return
    }

    if (
      !userHasEditPermission &&
      boardConfigs?.length === 1 &&
      !location.state?.exitSiteId
    ) {
      navigate(
        generatePath(LocationPath.BoardView, {
          boardId: boardConfigs[0].id,
        }),
        { replace: true }
      )
      return
    }

    if (!siteId && (sites?.length === 1 || locationSiteId)) {
      const searchParams = new URLSearchParams({
        siteId: locationSiteId || sites?.[0].id || '',
      })
      navigate({
        pathname: LocationPath.Boards,
        search: searchParams.toString(),
      })
      return
    }

    setHasCheckedRedirect(true)
  }, [
    boardConfigs,
    location.search,
    location.state?.exitSiteId,
    locationSiteId,
    navigate,
    siteId,
    sites,
    userHasEditPermission,
  ])

  useEffect(() => {
    maybeDoRedirect()
  }, [maybeDoRedirect])

  const siteName = hasCheckedRedirect
    ? sites?.find((site) => site.id === siteId)?.name
    : undefined

  const showSites = hasCheckedRedirect && sites?.length && !siteId

  return (
    <PageContentTemplate
      views={[BOARD_VIEW]}
      title={
        <>
          {`${
            (sites?.length && sites?.length > 1 && siteName) ||
            `${siteName || ''} `
          } Boards`}
        </>
      }
      selectedViewId="boards"
      actions={
        !showSites && !!siteId && userHasEditPermission ? (
          <ButtonLink
            css={{ marginLeft: remSpacing.xsmall }}
            size={'sm'}
            to={generatePath(LocationPath.BoardEdit, {
              siteId: siteId,
              boardId: crypto.randomUUID(),
            })}
          >
            <Plus size="xs" />
            Create board
          </ButtonLink>
        ) : undefined
      }
    >
      <div css={{ position: 'relative' }}>
        {!loading &&
          hasCheckedRedirect &&
          (showSites ? (
            <SitesTable sites={sites ?? []} />
          ) : (
            <BoardsTable
              boardsForSite={boardConfigs ?? []}
              showEditButton={userHasEditPermission}
            />
          ))}
      </div>
    </PageContentTemplate>
  )
}

const SitesTable = ({
  sites,
}: {
  sites: {
    id: string
    name: string
  }[]
}) => {
  return (
    <Table>
      <THead>
        <TR>
          <TH>Sites</TH>
        </TR>
      </THead>
      <TBody>
        {sites.map((site) => {
          return (
            <TR key={site.id}>
              <TD>
                <Link
                  css={{
                    textDecoration: 'none',
                    color: theme.palette.text.secondary,
                  }}
                  to={{
                    pathname: LocationPath.Boards,
                    search: new URLSearchParams({
                      siteId: site.id,
                    }).toString(),
                  }}
                >
                  {site.name}
                </Link>
              </TD>
              <TD>
                <FlexContainer
                  alignItems={'center'}
                  gap={remSpacing.gutter}
                  justifyContent={'flex-end'}
                >
                  <ButtonLink
                    size={'sm'}
                    appearance={'link'}
                    to={{
                      pathname: LocationPath.Boards,
                      search: new URLSearchParams({
                        siteId: site.id,
                      }).toString(),
                    }}
                  >
                    View Boards
                  </ButtonLink>
                </FlexContainer>
              </TD>
            </TR>
          )
        })}
      </TBody>
    </Table>
  )
}

const BoardsTable = ({
  boardsForSite,
  showEditButton,
}: {
  boardsForSite: BoardConfig[]
  showEditButton?: boolean
}) => {
  const isAnyBoardConfigured = boardsForSite.length !== 0

  return isAnyBoardConfigured ? (
    <Table>
      <THead>
        <TR>
          <TH>Boards</TH>
          <TH />
        </TR>
      </THead>
      <TBody>
        {boardsForSite.map((config) => {
          return (
            <TR key={config.id}>
              <TD>
                <Link
                  css={{
                    textDecoration: 'none',
                    color: theme.palette.text.secondary,
                  }}
                  to={generatePath(LocationPath.BoardView, {
                    boardId: config.id,
                  })}
                >
                  {config.name}
                </Link>
              </TD>
              <TD>
                <FlexContainer
                  alignItems={'center'}
                  gap={remSpacing.gutter}
                  justifyContent={'flex-end'}
                >
                  <>
                    <P3 css={{ color: theme.palette.gray[60] }}>
                      <span css={{ ...fontWeights.semibold }}>
                        Last edited:{' '}
                      </span>
                      {config.lastUpdatedBy}{' '}
                      {config.lastUpdated?.toLocaleString(
                        ApellaDateTimeFormats.DATE
                      )}
                    </P3>
                    {showEditButton && (
                      <ButtonLink
                        size={'sm'}
                        appearance={'link'}
                        to={generatePath(LocationPath.BoardEdit, {
                          siteId: config.siteId,
                          boardId: config.id ?? crypto.randomUUID(),
                        })}
                      >
                        <Edit size={'xs'} />
                        Edit
                      </ButtonLink>
                    )}
                  </>
                  <ButtonLink
                    size={'sm'}
                    appearance={'link'}
                    to={generatePath(LocationPath.BoardView, {
                      boardId: config.id,
                    })}
                  >
                    View Board
                  </ButtonLink>
                </FlexContainer>
              </TD>
            </TR>
          )
        })}
      </TBody>
    </Table>
  ) : (
    <div>There are no Boards configured for this site.</div>
  )
}
