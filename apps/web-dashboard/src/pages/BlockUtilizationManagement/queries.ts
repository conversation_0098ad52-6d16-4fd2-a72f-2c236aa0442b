import { gql } from '@apollo/client'

export const LIST_BLOCK_NAMES_FOR_DATE_RANGE = gql`
  query ListBlockNamesForDateRange(
    $siteIds: [ID!]
    $minDate: DateTime!
    $maxDate: DateTime!
  ) {
    blocks(
      query: { siteIds: $siteIds, minEndTime: $minDate, maxStartTime: $maxDate }
    ) {
      edges {
        node {
          id
          name
          surgeons {
            firstName
            lastName
          }
        }
      }
    }
  }
`

export const GET_CASES_TO_BLOCKS = gql`
  query CasesToBlocks(
    $minDate: DateTime!
    $maxDate: DateTime!
    $siteId: String!
    $blockId: String!
  ) {
    casesToBlocks(
      query: {
        minDate: $minDate
        maxDate: $maxDate
        siteId: $siteId
        blockId: $blockId
      }
    ) {
      edges {
        node {
          caseId
          overridden
          overriddenUtilizedCaseSeconds
          overriddenTurnoverSeconds
          blockId
          score
          utilizedCaseSeconds
          actualCaseSeconds
          scheduledCase {
            externalCaseId
            status
            scheduledStartTime
            scheduledEndTime
            caseStaff {
              role
              staff {
                id
                lastName
                firstName
              }
            }
            primaryCaseProcedures {
              procedure {
                name
              }
            }
          }
        }
      }
    }
  }
`

export const UPSERT_CASES_TO_BLOCKS_OVERRIDES = gql`
  mutation UpsertCaseToBlockOverrides(
    $input: [CaseToBlockOverrideUpsertInput!]!
  ) {
    caseToBlockOverridesUpsert(input: $input) {
      success
    }
  }
`

export const UPSERT_BLOCK_TIME_OVERRIDES = gql`
  mutation UpsertBlockTimeOverrides($input: [BlockTimeOverrideUpsertInput!]!) {
    blockTimeOverridesUpsert(input: $input) {
      success
    }
  }
`
