import { Suspense, useMemo } from 'react'
import {
  ActionFunctionArgs,
  Await,
  generatePath,
  LoaderFunctionArgs,
  replace,
  useAsyncValue,
  useFetcher,
  useLoaderData,
  useNavigate,
} from 'react-router'
import { toast } from 'react-toastify'

import { parseWithYup } from '@conform-to/yup'
import { groupBy } from 'lodash'
import { DateTime } from 'luxon'
import * as yup from 'yup'

import {
  ApellaDateTimeFormats,
  Button,
  Close,
  Edit,
  H4,
  H5,
  Plus,
  pxSpacing,
  remSpacing,
  theme,
  Union,
} from '@apella/component-library'
import { parseParams } from '@apella/hooks'
import { Block, Staff } from 'src/__generated__/globalTypes'
import { EmptyState } from 'src/components/EmptyState'
import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { defaultTimezone } from 'src/Contexts'
import { fetchSites } from 'src/modules/site/fetchSites'
import { getRouteContext, LocationPath } from 'src/router/types'

import { THE_UNBLOCKED_BLOCK_ID } from '../ScheduleAssistant/constants'
import {
  ListBlockNamesForDateRange,
  ListBlockNamesForDateRangeVariables,
  UpsertCaseToBlockOverrides,
  UpsertCaseToBlockOverridesVariables,
} from './__generated__'
import { BlockUtilizationManagementFilters } from './BlockUtilizationManagementFilters'
import { CaseStatus } from './constants'
import { fetchCasesToBlocks } from './fetchCasesToBlocks'
import {
  LIST_BLOCK_NAMES_FOR_DATE_RANGE,
  UPSERT_CASES_TO_BLOCKS_OVERRIDES,
} from './queries'
import { CaseToBlockType } from './types'
import {
  castParams,
  useBlockUtilizationManagementFilters,
} from './useBlockUtilizationManagementFilters'

export const loader = async (
  { request }: LoaderFunctionArgs,
  context: unknown
) => {
  const { flags, apolloClient, user } = getRouteContext(context)
  if (!flags.enableBlockUtilizationManagementPage) {
    throw replace(LocationPath.Home)
  }

  const requestURL = new URL(request.url)
  const params = parseParams(requestURL.search)
  const { selectedSiteId, selectedDateRange, selectedBlockId } =
    castParams(params)
  const blocksPromise =
    selectedSiteId && selectedDateRange
      ? apolloClient.query<
          ListBlockNamesForDateRange,
          ListBlockNamesForDateRangeVariables
        >({
          query: LIST_BLOCK_NAMES_FOR_DATE_RANGE,
          variables: {
            minDate: DateTime.fromISO(selectedDateRange[0])
              .startOf('day')
              .toISO(),
            maxDate: DateTime.fromISO(selectedDateRange[1])
              .endOf('day')
              .toISO(),
            siteIds: [selectedSiteId],
          },
        })
      : Promise.resolve({ data: null })

  const [{ sites }, { data: blocksData }] = await Promise.all([
    fetchSites(apolloClient),
    blocksPromise,
  ])
  const blocks: Block[] =
    blocksData?.blocks?.edges.map((blockEdge) => blockEdge.node as Block) ?? []

  const timezone =
    user.currentOrganization?.node.sites.edges[0]?.node.timezone ||
    defaultTimezone
  return {
    sites,
    blocks,
    casesToBlocksPromise: fetchCasesToBlocks(apolloClient, {
      selectedSiteId,
      selectedDateRange,
      timezone,
      selectedBlockId,
    }),
  }
}

const caseToBlockOverrideSchema = yup.object({
  caseId: yup.string().required(),
  blockId: yup.string().required(),
  blockDate: yup.string().required(),
  utilizedProcedureMinutes: yup.number(),
  utilizedTurnoverMinutes: yup.number(),
})

type CaseToBlockOverrideData = yup.InferType<typeof caseToBlockOverrideSchema>

export const action = async (
  { request }: ActionFunctionArgs,
  context: unknown
) => {
  const { apolloClient, user } = getRouteContext(context)
  const formData = await request.formData()
  const submission = parseWithYup(formData, {
    schema: caseToBlockOverrideSchema,
  })
  if (submission.status !== 'success') {
    throw new Error('Failed to remove case missing date and caseId')
  }

  const { data } = await apolloClient.mutate<
    UpsertCaseToBlockOverrides,
    UpsertCaseToBlockOverridesVariables
  >({
    mutation: UPSERT_CASES_TO_BLOCKS_OVERRIDES,
    variables: {
      input: [
        {
          blockDate: submission.value.blockDate,
          blockId: submission.value.blockId,
          caseId: submission.value.caseId,
          utilizedProcedureMinutes: submission.value.utilizedProcedureMinutes,
          utilizedTurnoverMinutes: submission.value.utilizedTurnoverMinutes,
          userId: user.userId,
        },
      ],
    },
  })
  if (data?.caseToBlockOverridesUpsert?.success) {
    toast.success('successfully removed/added case from block')
  } else {
    toast.error('failed to remove/add case from block')
  }
  return null
}
export const BlockUtilizationManagementPage = () => {
  const { sites, casesToBlocksPromise, blocks } =
    useLoaderData<Awaited<ReturnType<typeof loader>>>()

  const { selectedBlockId, selectedSiteId } =
    useBlockUtilizationManagementFilters()

  const selectedBlocksSurgeons = useMemo(() => {
    const selectedBlock: Block[] = selectedBlockId
      ? blocks.filter((block) => block.id === selectedBlockId)
      : []
    return selectedBlock.length === 1 ? selectedBlock[0].surgeons : []
  }, [selectedBlockId, blocks])

  return (
    <PageContentTemplate
      title="Block Utilization Management"
      filters={
        <BlockUtilizationManagementFilters sites={sites} blocks={blocks} />
      }
    >
      {selectedBlockId && (
        <div
          css={{
            display: 'flex',
            gap: remSpacing.small,
            flexDirection: 'column',
            borderBottom: `1px solid ${theme.palette.gray[40]}`,
            paddingBottom: remSpacing.small,
          }}
        >
          <H4>Block surgeon(s): </H4>
          <p>
            {selectedBlocksSurgeons
              .map((staff: Staff) => `${staff.lastName}, ${staff.firstName}`)
              .join('; ')}
          </p>
        </div>
      )}
      {!selectedSiteId ? (
        <EmptyState
          message="Select a site"
          subtext="to view blocks"
          Icon={Union}
        />
      ) : !selectedBlockId ? (
        <EmptyState
          message="Select a block"
          subtext="to view cases"
          Icon={Union}
        />
      ) : (
        <Suspense fallback={<div>Loading...</div>}>
          <Await resolve={casesToBlocksPromise}>
            <CasesByDates />
          </Await>
        </Suspense>
      )}
    </PageContentTemplate>
  )
}

const CasesByDates = (): React.JSX.Element => {
  const navigate = useNavigate()
  const fetcher = useFetcher<CaseToBlockOverrideData>()

  const { casesForBlocks } = useAsyncValue() as Awaited<
    Awaited<ReturnType<typeof loader>>['casesToBlocksPromise']
  >

  const { selectedBlockId, selectedDateRange, selectedSiteId } =
    useBlockUtilizationManagementFilters()

  const casesForBlock = useMemo(
    () =>
      casesForBlocks
        .filter((caseForBlock: CaseToBlockType) => caseForBlock.score >= 4)
        .filter(
          (caseForBlock: CaseToBlockType) =>
            caseForBlock.blockId === selectedBlockId
        )
        .filter(
          (caseForBlock: CaseToBlockType) =>
            // Optimistically remove deleted case during action submission
            !(
              fetcher.formData &&
              caseForBlock.caseId === fetcher.formData.get('caseId')
            )
        ),
    [casesForBlocks, selectedBlockId, fetcher.formData]
  )
  const casesByDate = useMemo(() => {
    const casesByDate = groupBy(
      casesForBlock,
      (c) =>
        `${c.scheduledEndTime.toLocaleString(
          ApellaDateTimeFormats.DATETIME_WITH_MONTH_LONG
        )}`
    )

    return casesByDate
  }, [casesForBlock])

  if (casesForBlock.length === 0) {
    return (
      <EmptyState
        message="No cases found for selected block"
        subtext="There are no cases assigned to this block for the selected date range"
        Icon={Union}
      />
    )
  }

  return (
    <div style={{ height: '100vh', overflow: 'auto' }}>
      {Object.keys(casesByDate).map((date: string) => (
        <div key={date}>
          <H4>{date}</H4>
          {casesByDate[date].map((caseToBlock: CaseToBlockType) => {
            const cancelledCase =
              caseToBlock.status === CaseStatus.CANCELLED &&
              !caseToBlock.overridden
            const border = `1px ${cancelledCase ? 'dashed' : 'solid'} ${theme.palette.gray[40]}`
            return (
              <div
                key={caseToBlock.caseId}
                css={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: remSpacing.small,
                  padding: remSpacing.medium,
                  border,
                  borderRadius: pxSpacing.xsmall,
                  marginBottom: remSpacing.small,
                }}
              >
                <div
                  css={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    flexDirection: 'column',
                    gap: remSpacing.small,
                  }}
                >
                  {cancelledCase && (
                    <div>
                      This case was cancelled. Click add to count it towards the
                      block utilization score.
                    </div>
                  )}
                  <H5>
                    scheduled time:&nbsp;
                    {caseToBlock?.scheduledStartTime.toLocaleString(
                      ApellaDateTimeFormats.TIME
                    )}
                    -
                    {caseToBlock?.scheduledEndTime.toLocaleString(
                      ApellaDateTimeFormats.TIME
                    )}
                  </H5>
                  <p>
                    surgeon(s): {caseToBlock?.primaryStaffNames?.join(', ')}
                  </p>
                </div>
                {!cancelledCase && (
                  <>
                    <Button
                      appearance="link"
                      onClick={() => {
                        const removeData: CaseToBlockOverrideData = {
                          caseId: caseToBlock.caseId,
                          blockDate: caseToBlock.scheduledEndTime.toISODate(),
                          blockId: THE_UNBLOCKED_BLOCK_ID,
                        }
                        if (
                          confirm('Are you sure you want to remove this case?')
                        ) {
                          fetcher.submit(removeData, {
                            method: 'POST',
                          })
                        }
                      }}
                      size="sm"
                    >
                      <Close size="xs" />
                      Remove
                    </Button>
                    <Button
                      appearance="link"
                      onClick={() => {
                        const path = generatePath(
                          LocationPath.BlockUtilizationCaseEditView,
                          {
                            caseId: caseToBlock.caseId,
                          }
                        )
                        navigate({
                          pathname: path,
                          search: new URLSearchParams({
                            'dateRange[0]': selectedDateRange[0],
                            'dateRange[1]': selectedDateRange[1],
                            site: selectedSiteId,
                            block: selectedBlockId,
                          }).toString(),
                        })
                      }}
                      size="sm"
                    >
                      <Edit size="xs" />
                      Edit
                    </Button>
                  </>
                )}
                {cancelledCase && (
                  <>
                    <Button
                      appearance="link"
                      onClick={() => {
                        const addData: CaseToBlockOverrideData = {
                          caseId: caseToBlock.caseId,
                          blockDate: caseToBlock.scheduledEndTime.toISODate(),
                          blockId: selectedBlockId,
                          utilizedProcedureMinutes: Math.floor(
                            caseToBlock.scheduledEndTime
                              .diff(caseToBlock.scheduledStartTime)
                              .as('minutes')
                          ),
                          utilizedTurnoverMinutes: 0,
                        }
                        if (
                          confirm('Are you sure you want to add this case?')
                        ) {
                          fetcher.submit(addData, {
                            method: 'POST',
                          })
                        }
                      }}
                      size="sm"
                    >
                      <Plus size="xs" />
                      Add
                    </Button>
                  </>
                )}
              </div>
            )
          })}
        </div>
      ))}
    </div>
  )
}
