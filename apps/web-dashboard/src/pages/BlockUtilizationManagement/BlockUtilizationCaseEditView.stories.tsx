import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'
import { Meta, StoryObj } from '@storybook/react'
import { delay, graphql, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { TimezoneProvider } from 'src/Contexts'
import { ApellaRouteContext } from 'src/router/types'
import { DEFAULT_STORYBOOK_USER } from 'src/test/constants'
import MockLDProvider from 'src/test/MockLDProvider'

import { CasesToBlocksDocument } from './__generated__'
import {
  BlockUtilizationCaseEditView,
  loader as blockUtilizationCaseEditViewLoader,
} from './BlockUtilizationCaseEditView'
import { CaseStatus } from './constants'

const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  cube: cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  }),
  user: DEFAULT_STORYBOOK_USER,
})

const meta: Meta<typeof BlockUtilizationCaseEditView> = {
  title: 'Pages/BlockUtilizationManagement',
  component: BlockUtilizationCaseEditView,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <CubeProvider cubeApi={parameters.reactRouter.context.cube}>
            <ApolloProvider
              client={parameters.reactRouter.context.apolloClient}
            >
              <Story />
            </ApolloProvider>
          </CubeProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
  ],
}

export default meta
type Story = StoryObj<typeof BlockUtilizationCaseEditView>

const routing = {
  path: '/block-utilization/:caseId/edit',
  handle: 'BlockUtilizationCaseEditView',
  loader: blockUtilizationCaseEditViewLoader,
}

const msw = (wait: number) => ({
  handlers: [
    graphql.query(CasesToBlocksDocument, async () => {
      await delay(wait)
      return HttpResponse.json({
        data: {
          casesToBlocks: {
            __typename: 'CaseToBlockConnection',
            edges: [
              {
                node: {
                  __typename: 'CaseToBlock',
                  actualCaseSeconds: 99 * 60,
                  utilizedCaseSeconds: 99 * 60,
                  scheduledCase: {
                    externalCaseId: 'external-1',
                    primaryCaseProcedures: [
                      {
                        procedure: {
                          name: 'procedure #1 name',
                          __typename: 'Procedure' as const,
                        },
                        __typename: 'CaseProcedure' as const,
                      },
                    ],
                    status: CaseStatus.SCHEDULED,
                    scheduledEndTime: '2025-02-06T17:36:49.729884+00:00',
                    scheduledStartTime: '2025-02-06T16:13:49.729884+00:00',
                    caseStaff: [
                      {
                        role: 'Primary',
                        staff: {
                          id: 'staff-1',
                          firstName: 'Thanos',
                          lastName: 'Last',
                          __typename: 'Staff',
                        },
                        __typename: 'CaseStaff',
                      },
                    ],
                    __typename: 'ScheduledCase',
                  },
                  blockId: 'block1-id',
                  score: 4,
                  caseId: '123',
                  overridden: true,
                  overriddenTurnoverSeconds: null,
                  overriddenUtilizedCaseSeconds: 99 * 60,
                },
                __typename: 'CaseToBlockEdge',
              },
              {
                node: {
                  __typename: 'CaseToBlock',
                  actualCaseSeconds: 4320,
                  utilizedCaseSeconds: 4320,
                  scheduledCase: {
                    externalCaseId: 'external-2',
                    primaryCaseProcedures: [],
                    status: CaseStatus.SCHEDULED,
                    scheduledEndTime: '2025-02-07T17:36:49.729884+00:00',
                    scheduledStartTime: '2025-02-07T16:13:49.729884+00:00',
                    caseStaff: [],
                    __typename: 'ScheduledCase',
                  },
                  blockId: 'block1-id',
                  score: 2,
                  caseId: 'ignore-this-case',
                  overridden: false,
                  overriddenTurnoverSeconds: null,
                  overriddenUtilizedCaseSeconds: null,
                },
                __typename: 'CaseToBlockEdge',
              },
              {
                node: {
                  __typename: 'CaseToBlock',
                  actualCaseSeconds: 4320,
                  utilizedCaseSeconds: 4320,
                  scheduledCase: {
                    externalCaseId: 'external-3',
                    primaryCaseProcedures: [],
                    status: CaseStatus.SCHEDULED,
                    scheduledEndTime: '2025-02-08T17:36:49.729884+00:00',
                    scheduledStartTime: '2025-02-08T16:13:49.729884+00:00',
                    caseStaff: [],
                    __typename: 'ScheduledCase',
                  },
                  blockId: 'block1-id',
                  score: 3,
                  caseId: '456',
                  overridden: false,
                  overriddenTurnoverSeconds: null,
                  overriddenUtilizedCaseSeconds: null,
                },
                __typename: 'CaseToBlockEdge',
              },
            ],
          },
        },
      })
    }),
  ],
})

export const BlockUtilizationCaseViewEnabled: Story = {
  parameters: {
    ldFlags: {
      enableBlockUtilizationManagementPage: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/block-utilization/123/edit',
        searchParams: {
          'dateRange[0]': '2025-02-01',
          'dateRange[1]': '2025-02-12',
          site: 'site-01',
          block: 'block1-id',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
