import { DateTime } from 'luxon'

export interface CaseToBlockType {
  actualMinutes?: number
  blockId: string
  caseId: string
  caseMinutes?: number
  externalId: string
  overridden: boolean
  primaryStaffNames: string[]
  procedures: string[]
  scheduledEndTime: DateTime
  scheduledStartTime: DateTime
  score: number
  status: string
  utilizedProcedureMinutes?: number
  utilizedTurnoverMinutes?: number
}
