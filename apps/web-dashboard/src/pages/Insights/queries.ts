import { gql } from '@apollo/client'
import { Filter, Query } from '@cubejs-client/core'
import { capitalize } from 'lodash'
import { DateTime } from 'luxon'

import { NONE_CASE_CLASSIFICATION_ID } from 'src/components/CaseClassificationTypesFilter'
import { FlipRoomValues } from 'src/components/FlipRoomFilter'
import { TimePeriod } from 'src/components/TimePeriodFilter'
import {
  CASE_DERIVED_PROPERTIES,
  CASE_PROCEDURES,
  CASE_STAFF,
  CASE_STAFF_ANESTHESIA,
  CASE_STAFF_CIRCULATOR,
  CASE_STAFF_SCRUB_TECH,
  CASE_STAFF_SURGEONS,
  CASES,
  REPORT_CASE,
  SERVICE_LINES,
  TURNOVER_LABELS,
  TURNOVERS,
} from 'src/modules/cube/types/dataCubes'
import { DayOfWeek } from 'src/pages/Insights/types'
import { Dimension, NonTimeDimensions } from 'src/utils/bucketHelpers'

import {
  ANESTHESIA_ROLES,
  C<PERSON><PERSON><PERSON>TOR_ROLES,
  SCRUB_TECH_ROLES,
  PRIMARY_SURGEON_ROLES,
} from '../../utils/roles'
import { DelayType } from './DelayTypeFilter'
import { TurnoverExclusionOptions } from './Turnover/turnoverExclusionOptions'

export const GET_PHASE_TYPES = gql`
  query GetPhaseTypesData {
    phaseTypes {
      edges {
        node {
          type
          title
          slug
          description
        }
      }
    }
  }
`
export const GET_OBSERVATION_TYPE_NAMES_FOR_CUSTOM_PHASES = gql`
  query GetObservationTypeNamesForCustomPhases($orgId: ID!) {
    observationTypeNamesForCustomPhases(query: { orgId: $orgId }) {
      typeId
      name
    }
  }
`

/**
 * According to the documentation for CubeJS, it expects the time dimension date ranges to not include
 * a timezone offset and be in the timezone `timezone`.
 * For example, an input of `2022-11-30T00:00:00.000-08:00` and `America/New_York`,
 * should output `2022-11-30T03:00:00.000`
 *
 * https://cube.dev/docs/query-format#time-dimensions-format
 * @param datetimes array of the string datetime, with potential offset defined
 * @param timezone desired timezone
 */
export const formatTimeDimensionDateRange = (
  datetimes: [string, string],
  timezone: string
): [string, string] => {
  // CubeJS requires the timeDimension type to be [string, string] rather than string[].
  // Doing datetimes.map() fails the typechecking since map() returns string[]. Therefore, we
  // explicitly create a helper function and call it twice. 🤷‍
  const convertDateTime = (datetime: string) =>
    DateTime.fromISO(datetime).setZone(timezone).toISO({ includeOffset: false })

  return [convertDateTime(datetimes[0]), convertDateTime(datetimes[1])]
}

export const getTurnoverQueryParams = ({
  siteIds,
  roomIds,
  staffIds,
  anesthesiaIds,
  serviceLineIds,
  procedureIds,
  caseClassificationTypesIds,
  minDateTime,
  maxDateTime,
  daysOfWeek,
  timezone,
  dimension,
  timePeriod,
  durationRangeMinutes,
  showFlipRooms,
  turnoverExclusionOption,
}: {
  siteIds?: string[]
  roomIds?: string[]
  staffIds?: string[]
  anesthesiaIds?: string[]
  serviceLineIds?: string[]
  procedureIds?: string[]
  caseClassificationTypesIds?: string[]
  minDateTime: string
  maxDateTime: string
  daysOfWeek?: DayOfWeek[]
  timezone: string
  dimension?: Dimension
  timePeriod?: TimePeriod<string>
  durationRangeMinutes?: TimePeriod<number>
  showFlipRooms?: FlipRoomValues
  turnoverExclusionOption?: TurnoverExclusionOptions
}): Query => {
  const filters: Filter[] = []

  if (turnoverExclusionOption === TurnoverExclusionOptions.NOT_EXCLUDED) {
    filters.push({
      member: TURNOVER_LABELS.TYPE,
      operator: 'notContains',
      values: ['exclusion'],
    })
  } else if (turnoverExclusionOption === TurnoverExclusionOptions.EXCLUDED) {
    filters.push({
      member: TURNOVER_LABELS.TYPE,
      operator: 'contains',
      values: ['exclusion'],
    })
  }

  if (showFlipRooms === FlipRoomValues.SameSurgeonSameRoom) {
    filters.push({
      member: TURNOVERS.SAME_SURGEON_SAME_ROOM,
      operator: 'equals',
      values: ['true'],
    })
  } else if (showFlipRooms === FlipRoomValues.HideFlipRooms) {
    filters.push({
      member: CASE_DERIVED_PROPERTIES.IS_IN_FLIP_ROOM,
      operator: 'notEquals',
      values: ['true'],
    })
  } else if (showFlipRooms === FlipRoomValues.ShowFlipRooms) {
    filters.push({
      member: CASE_DERIVED_PROPERTIES.IS_IN_FLIP_ROOM,
      operator: 'equals',
      values: ['true'],
    })
  }

  if (timePeriod) {
    filters.push(
      {
        member: TURNOVERS.START_TIME_LOCAL,
        operator: 'gte',
        values: [timePeriod.min],
      },
      {
        member: TURNOVERS.END_TIME_LOCAL,
        operator: 'lte',
        values: [timePeriod.max],
      }
    )
  }

  if (durationRangeMinutes) {
    filters.push(
      {
        member: TURNOVERS.TOTAL_DURATION_MINUTES,
        operator: 'gte',
        values: [`${durationRangeMinutes.min}`],
      },
      {
        member: TURNOVERS.TOTAL_DURATION_MINUTES,
        operator: 'lte',
        values: [`${durationRangeMinutes.max}`],
      }
    )
  }

  if (siteIds) {
    filters.push({
      member: TURNOVERS.SITE_ID,
      operator: 'equals',
      values: siteIds ?? [],
    })
  }
  if (roomIds) {
    filters.push({
      member: TURNOVERS.ROOM_ID,
      operator: 'equals',
      values: roomIds ?? [],
    })
  }

  // When filtering or aggregating by any clinical dimensions, the following phase case needs to be
  // filtered by typeId.

  if (dimension == NonTimeDimensions.SURGEON) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: PRIMARY_SURGEON_ROLES,
    })
  } else if (dimension == NonTimeDimensions.ANESTHESIA) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: ANESTHESIA_ROLES,
    })
  }

  if (staffIds?.length) {
    filters.push({
      member: CASE_STAFF_SURGEONS.STAFF_ID,
      operator: 'equals',
      values: staffIds,
    })
  }

  if (anesthesiaIds?.length) {
    filters.push({
      member: CASE_STAFF_ANESTHESIA.STAFF_ID,
      operator: 'equals',
      values: anesthesiaIds,
    })
  }

  if (serviceLineIds?.length) {
    filters.push({
      member: SERVICE_LINES.ID,
      operator: 'equals',
      values: serviceLineIds,
    })
  }

  if (procedureIds?.length) {
    filters.push({
      member: CASE_PROCEDURES.PROCEDURE_ID,
      operator: 'equals',
      values: procedureIds,
    })
  }

  if (
    caseClassificationTypesIds &&
    caseClassificationTypesIds.length &&
    caseClassificationTypesIds.some((id) => id === NONE_CASE_CLASSIFICATION_ID)
  ) {
    filters.push({
      or: [
        {
          member: CASES.CASE_CLASSIFICATION_ID,
          operator: 'equals',
          values: caseClassificationTypesIds,
        },
        {
          member: CASES.CASE_CLASSIFICATION_ID,
          operator: 'notSet',
        },
      ],
    })
  } else if (caseClassificationTypesIds?.length) {
    filters.push({
      member: CASES.CASE_CLASSIFICATION_ID,
      operator: 'equals',
      values: caseClassificationTypesIds,
    })
  }
  if (daysOfWeek) {
    filters.push({
      member: TURNOVERS.DAY_OF_WEEK,
      operator: 'equals',
      values: daysOfWeek.map(capitalize),
    })
  }

  return {
    filters: filters,
    timeDimensions: [
      {
        dimension: TURNOVERS.START_TIMESTAMP,
        dateRange: formatTimeDimensionDateRange(
          [minDateTime, maxDateTime],
          timezone
        ),
      },
    ],
    timezone: timezone,
  }
}

export const getNewFirstCaseStartQueryParams = ({
  minDateTime,
  maxDateTime,
  daysOfWeek,
  siteIds,
  roomIds,
  staffIds,
  anesthesiaIds,
  serviceLineIds,
  procedureIds,
  timezone,
  dimension,
  delayType,
}: {
  minDateTime: string
  maxDateTime: string
  daysOfWeek?: string[]
  siteIds?: string[]
  roomIds?: string[]
  staffIds?: string[]
  anesthesiaIds?: string[]
  serviceLineIds?: string[]
  procedureIds?: string[]
  timezone: string
  dimension?: Dimension
  delayType?: string
}): Query => {
  const filters: Filter[] = [
    {
      member: CASES.IS_FIRST_CASE,
      operator: 'equals',
      values: ['true'],
    },
    {
      member: CASES.ACTUAL_START_TIMESTAMP,
      operator: 'set',
    },
  ]

  if (siteIds) {
    filters.push({
      member: CASES.SITE_ID,
      operator: 'equals',
      values: siteIds ?? [],
    })
  }
  if (roomIds) {
    filters.push({
      member: CASES.ROOM_ID,
      operator: 'equals',
      values: roomIds ?? [],
    })
  }

  if (dimension == NonTimeDimensions.SURGEON) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: PRIMARY_SURGEON_ROLES,
    })
  } else if (dimension == NonTimeDimensions.ANESTHESIA) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: ANESTHESIA_ROLES,
    })
  }

  if (staffIds?.length) {
    filters.push({
      member: CASE_STAFF_SURGEONS.STAFF_ID,
      operator: 'equals',
      values: staffIds,
    })
  }

  if (anesthesiaIds?.length) {
    filters.push({
      member: CASE_STAFF_ANESTHESIA.STAFF_ID,
      operator: 'equals',
      values: anesthesiaIds,
    })
  }

  if (serviceLineIds?.length) {
    filters.push({
      member: SERVICE_LINES.ID,
      operator: 'equals',
      values: serviceLineIds,
    })
  }

  if (procedureIds?.length) {
    filters.push({
      member: CASE_PROCEDURES.PROCEDURE_ID,
      operator: 'equals',
      values: procedureIds,
    })
  }
  if (daysOfWeek) {
    filters.push({
      member: CASES.ACTUAL_DAY_OF_WEEK,
      operator: 'equals',
      values: daysOfWeek.map(capitalize),
    })
  }
  if (delayType === DelayType.PRESENT) {
    filters.push({
      member: REPORT_CASE.DELAY_TYPE,
      operator: 'set',
    })
    filters.push({
      member: REPORT_CASE.DELAY_TYPE,
      operator: 'notEquals',
      values: [''],
    })
  }
  if (delayType === DelayType.NONE) {
    filters.push({
      member: REPORT_CASE.DELAY_TYPE,
      operator: 'equals',
      values: [''],
    })
  }

  return {
    filters: filters,
    timeDimensions: [
      {
        dimension: CASES.SCHEDULED_START_TIMESTAMP,
        dateRange: formatTimeDimensionDateRange(
          [minDateTime, maxDateTime],
          timezone
        ),
      },
    ],
    timezone: timezone,
  }
}

export const getCasesQueryParams = ({
  siteIds,
  roomIds,
  staffIds,
  anesthesiaIds,
  circulatorStaffIds,
  scrubTechStaffIds,
  serviceLineIds,
  procedureIds,
  minDateTime,
  maxDateTime,
  daysOfWeek,
  timezone,
  timePeriod,
  isTimePeriodInverted,
  dimension,
}: {
  siteIds?: string[]
  roomIds?: string[]
  staffIds?: string[]
  anesthesiaIds?: string[]
  circulatorStaffIds?: string[]
  scrubTechStaffIds?: string[]
  serviceLineIds?: string[]
  procedureIds?: string[]
  minDateTime: string
  maxDateTime: string
  daysOfWeek?: DayOfWeek[]
  timezone: string
  timePeriod?: TimePeriod<string>
  isTimePeriodInverted?: boolean
  dimension?: Dimension
}): Query => {
  const filters: Filter[] = []

  if (siteIds) {
    filters.push({
      member: CASES.SITE_ID,
      operator: 'equals',
      values: siteIds ?? [],
    })
  }
  if (roomIds) {
    filters.push({
      member: CASES.ROOM_ID,
      operator: 'equals',
      values: roomIds ?? [],
    })
  }

  if (dimension == NonTimeDimensions.SURGEON) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: PRIMARY_SURGEON_ROLES,
    })
  } else if (dimension == NonTimeDimensions.ANESTHESIA) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: ANESTHESIA_ROLES,
    })
  } else if (dimension == NonTimeDimensions.CIRCULATOR) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: CIRCULATOR_ROLES,
    })
  } else if (dimension == NonTimeDimensions.SCRUB_TECH) {
    filters.push({
      member: CASE_STAFF.ROLE,
      operator: 'equals',
      values: SCRUB_TECH_ROLES,
    })
  }

  if (staffIds?.length) {
    filters.push({
      member: CASE_STAFF_SURGEONS.STAFF_ID,
      operator: 'equals',
      values: staffIds,
    })
  }

  if (anesthesiaIds?.length) {
    filters.push({
      member: CASE_STAFF_ANESTHESIA.STAFF_ID,
      operator: 'equals',
      values: anesthesiaIds,
    })
  }

  if (circulatorStaffIds?.length) {
    filters.push({
      member: CASE_STAFF_CIRCULATOR.STAFF_ID,
      operator: 'equals',
      values: circulatorStaffIds,
    })
  }
  if (scrubTechStaffIds?.length) {
    filters.push({
      member: CASE_STAFF_SCRUB_TECH.STAFF_ID,
      operator: 'equals',
      values: scrubTechStaffIds,
    })
  }

  if (serviceLineIds?.length) {
    filters.push({
      member: SERVICE_LINES.ID,
      operator: 'equals',
      values: serviceLineIds,
    })
  }

  if (procedureIds?.length) {
    filters.push({
      member: CASE_PROCEDURES.PROCEDURE_ID,
      operator: 'equals',
      values: procedureIds,
    })
  }

  if (daysOfWeek) {
    filters.push({
      member: CASES.ACTUAL_DAY_OF_WEEK,
      operator: 'equals',
      values: daysOfWeek.map(capitalize),
    })

    if (timePeriod) {
      if (isTimePeriodInverted) {
        /*
         * between 17:00 and 7:30
         * (case_start > 17:00 OR case_start < 7:30)
         * OR (case_end > 17:00 OR case_end < 7:30)
         */
        filters.push({
          or: [
            {
              or: [
                {
                  member: CASES.ACTUAL_START_TIME_LOCAL,
                  operator: 'gte',
                  values: [timePeriod.max],
                },
                {
                  member: CASES.ACTUAL_START_TIME_LOCAL,
                  operator: 'lte',
                  values: [timePeriod.min],
                },
              ],
            },
            {
              or: [
                {
                  member: CASES.ACTUAL_END_TIME_LOCAL,
                  operator: 'gte',
                  values: [timePeriod.max],
                },
                {
                  member: CASES.ACTUAL_END_TIME_LOCAL,
                  operator: 'lte',
                  values: [timePeriod.min],
                },
              ],
            },
          ],
        })
      } else {
        /*
         * between 7:30 and 17:00
         * (case_start > 7:30 AND case_start < 17:00)
         * OR (case_end > 7:30 AND case_end < 17:00)
         */
        filters.push({
          or: [
            {
              and: [
                {
                  member: CASES.ACTUAL_START_TIME_LOCAL,
                  operator: 'gte',
                  values: [timePeriod.min],
                },
                {
                  member: CASES.ACTUAL_START_TIME_LOCAL,
                  operator: 'lte',
                  values: [timePeriod.max],
                },
              ],
            },
            {
              and: [
                {
                  member: CASES.ACTUAL_END_TIME_LOCAL,
                  operator: 'gte',
                  values: [timePeriod.min],
                },
                {
                  member: CASES.ACTUAL_END_TIME_LOCAL,
                  operator: 'lte',
                  values: [timePeriod.max],
                },
              ],
            },
          ],
        })
      }
    }
  }

  return {
    filters: filters,
    timeDimensions: [
      {
        dimension: CASES.ACTUAL_START_TIMESTAMP,
        dateRange: formatTimeDimensionDateRange(
          [minDateTime, maxDateTime],
          timezone
        ),
      },
    ],
    timezone: timezone,
  }
}
