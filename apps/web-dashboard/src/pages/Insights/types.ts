import { Query } from '@cubejs-client/core'
import { DateTime, Duration } from 'luxon'

import {
  CASE_DERIVED_PROPERTIES,
  CASE_STAFF,
  PROCEDURES,
  SERVICE_LINES,
  STAFF,
  TURNOVERS,
  CASES,
} from 'src/modules/cube/types/dataCubes'

export type RawClinicalData = Partial<{
  [CASES.CASE_ID]: string
  [CASE_STAFF.ROLE]: string | null
  [STAFF.FIRST_NAME]: string | null
  [STAFF.LAST_NAME]: string | null
  [PROCEDURES.NAME]: string | null
  [CASES.CUSTOMER_CASE_ID]: string | null
  [SERVICE_LINES.NAME]: string | null
  [CASES.IS_ADD_ON]: boolean | null
  [CASES.CASE_CLASSIFICATION_ID]: string | null
  [CASES.PATIENT_CLASSIFICATION_ID]: string | null
  [CASE_DERIVED_PROPERTIES.IS_IN_FLIP_ROOM]: boolean | null
  [CASE_DERIVED_PROPERTIES.PRECEDING_CASE_ID]: string | null
  [CASES.ACTUAL_START_TIMESTAMP]: string | null
  [CASES.SCHEDULED_START_TIMESTAMP]: string | null
}>

export type CountField = CASES.COUNT | TURNOVERS.COUNT

export type CaseLengthFilterProps = {
  minTime: string
  maxTime: string
  graphStackGrouping?: string
  cubeParams: Query
}

export type ISOWeekDate = 1 | 2 | 3 | 4 | 5 | 6 | 7

export type DayOfWeek =
  | 'sunday'
  | 'monday'
  | 'tuesday'
  | 'wednesday'
  | 'thursday'
  | 'friday'
  | 'saturday'

export const daysOfWeekInOrder: DayOfWeek[] = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
]

export enum METRICS {
  TURNOVER = 'Turnovers',
  FIRST_CASE_START = 'First case starts',
  CASE = 'Cases',
  CASE_START = 'On time start',
}

export interface VideoBladeTableData {
  actualDuration: Duration | undefined
  actualPhaseId?: string
  actualStartTime: DateTime
  caseId?: string
  followingCaseId?: string
  goal?: number | null
  maxMinutes?: number | null
  precedingCaseId?: string
  roomId: string
  roomName: string
}

export interface Paging {
  limit: number
  offset: number
}

export interface StaffCount {
  count: string
  firstName: string
  lastName: string
}

export interface ProcedureCount {
  count: string
  procedureName: string
}

export enum OnTimeStartType {
  ACOTS = 'ACOTS',
  FCOTS = 'FCOTS',
  NFCOTS = 'NFCOTS',
}

export enum DisplayName {
  ACTUAL_START_TIME = 'Actual Start Time',
  ANESTHESIA = 'Anesthesia',
  CASE_CLASSIFICATION_TYPE = 'Case Classification Type',
  DATE = 'Date',
  DAY = 'Day of Week',
  ACTUAL_DURATION = 'Actual Duration',
  SCHEDULED_DURATION = 'Scheduled Duration',
  CUSTOMER_CASE_ID = 'Case ID',
  MINUTES_LATE = 'Minutes Late',
  PRIMARY_SURGEONS = 'Primary Surgeons',
  PROCEDURES = 'Procedures',
  ROOMS = 'Rooms',
  SCHEDULED_START = 'Scheduled Start Time',
  SITE = 'Site',
  START = 'Start Date Time',
  SERVICE_LINE = 'Service Line',
  ADD_ON = 'Add On',
  PATIENT_CLASS = 'Patient Class',
  CASE_CLASSIFICATION = 'Case Classification',
  FOLLOWING_CASE_ID = 'Following Case ID',
  FOLLOWING_CASE_SURGEONS = 'Following Case Surgeons',
  FOLLOWING_CASE_ANESTHESIA = 'Following Case Anesthesia',
  FOLLOWING_CASE_SERVICE_LINE = 'Following Case Service Line',
  FOLLOWING_CASE_PROCEDURES = 'Following Case Procedures',
  FOLLOWING_CASE_IS_IN_FLIP_ROOM = 'Following Case Flip Room',
  FOLLOWING_CASE_IS_FOLLOWING = 'Following Case Is Following',
  FOLLOWING_CASE_ADD_ON = 'Following Case Add On',
  FOLLOWING_CASE_PATIENT_CLASS = 'Following Case Patient Class',
  FOLLOWING_CASE_SCHEDULED_START = 'Following Case Scheduled Start',
  FOLLOWING_CASE_ACTUAL_START = 'Following Case Actual Start',
  FOLLOWING_CASE_CLASSIFICATION = 'Following Case Classification',
  START_TIME = 'Start Time',
  ROOM = 'Room',
  CLEANING = 'Cleaning',
  OPENING = 'Opening',
  PREP_SURGERY = 'Prep (Surgery)',
  PREP_ANESTHESIA = 'Prep (Anesthesia)',
  NOTE = 'Note',
  GOAL = 'Goal',
  DELAY_LABELS = 'Exclusion',
  SCRUB_TECHS = 'Scrub Techs',
  CIRCULATORS = 'Circulators',
  DELAY_COMMENTS = 'Delay Comments',
  DELAY_DURATION = 'Delay Duration (minutes)',
  DELAY_TYPE = 'Delay Type',
  DELAY_REASON = 'Delay Reason',
}

export enum Description {
  ACTUAL_DURATION = 'Actual duration of procedure, wheels-in to wheels-out (Apella)',
  SCHEDULED_DURATION = 'Scheduled duration of procedure, wheels-in to wheels-out (EHR)',
}

export interface CustomPhaseConfig {
  description: string
  endEventTypeId: string
  id: string
  name: string
  startEventTypeId: string
}
