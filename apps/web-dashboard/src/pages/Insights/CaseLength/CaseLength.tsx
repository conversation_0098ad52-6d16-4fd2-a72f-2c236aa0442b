import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { InsightsDatePicker } from 'src/components/InsightsDatePicker'
import { FilterBarContainer } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { ServiceLinesFilterWithCount } from 'src/components/ServiceLinesFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { TimePeriodFilter } from 'src/components/TimePeriodFilter'
import { CASES } from 'src/modules/cube/types/dataCubes'
import AnesthesiaFilterDropdown from 'src/pages/Insights/common/AnesthesiaFilterDropdown'
import CirculatorFilterDropdown from 'src/pages/Insights/common/CirculatorFilterDropdown'
import PrimarySurgeonsFilterDropdown from 'src/pages/Insights/common/PrimarySurgeonsFilterDropdown'
import ProcedureFilterDropdown from 'src/pages/Insights/common/ProceduresFilterDropdown'
import ScrubTechFilterDropdown from 'src/pages/Insights/common/ScrubTechFilterDropdown'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { MetricsContainer } from '../MetricsContainer'
import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import CaseLengthGraph from './CaseLengthGraph'
import CaseLengthSummaryTile from './CaseLengthSummaryTile'
import CaseLengthTable from './CaseLengthTable'

export const CaseLength = () => {
  const {
    minTime,
    maxTime,
    siteIds,
    roomIds,
    serviceLineIds,
    daysOfWeek,
    timePeriod,
    isTimePeriodInverted,

    showResetFiltersButton,

    localOnChangeDateRanges,
    onChangeServiceLines,
    onChangeSites,
    onChangeRooms,
    onChangeDaysOfWeek,
    onChangeTimePeriod,
    onChangeTimePeriodInverted,
    resetActions,
    cubeParams,
  } = useInsightsSearchParams(METRICS.CASE)

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  return (
    <>
      <FilterBarContainer>
        <InsightsDatePicker
          minTime={minTime}
          maxTime={maxTime}
          onChangeDateRanges={localOnChangeDateRanges}
        />
        <DayOfWeekFilter selected={daysOfWeek} onChange={onChangeDaysOfWeek} />
        <TimePeriodFilter
          timePeriod={timePeriod}
          onChangeTimePeriod={onChangeTimePeriod}
          showInvertButton
          isInverted={isTimePeriodInverted}
          onChangeInverted={onChangeTimePeriodInverted}
        />
        <SitesRoomsFilter
          sites={sites}
          selectedSiteIds={siteIds}
          onChangeSites={onChangeSites}
          rooms={rooms}
          selectedRoomIds={roomIds}
          onChangeRooms={onChangeRooms}
          multipleSites
          bulkSelectSites
          bulkSelectRooms
        />

        <PrimarySurgeonsFilterDropdown metric={METRICS.CASE} />
        <AnesthesiaFilterDropdown metric={METRICS.CASE} />
        <CirculatorFilterDropdown metric={METRICS.CASE} />
        <ScrubTechFilterDropdown metric={METRICS.CASE} />
        <ServiceLinesFilterWithCount
          measure={CASES.COUNT}
          cubeParams={cubeParams}
          value={serviceLineIds}
          onChange={onChangeServiceLines}
        />
        <ProcedureFilterDropdown metric={METRICS.CASE} />

        {showResetFiltersButton && <ResetFilters resetActions={resetActions} />}
      </FilterBarContainer>
      <MetricsContainer
        graph={<CaseLengthGraph />}
        summaryTile={<CaseLengthSummaryTile />}
        table={<CaseLengthTable />}
      />
    </>
  )
}
