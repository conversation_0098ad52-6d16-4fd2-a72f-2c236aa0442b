import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

import {
  ArrowBack,
  Button,
  Dialog,
  H6,
  InputText,
  Option,
  P3,
  remSpacing,
  SingleSelect,
  theme,
  ToastMessage,
} from '@apella/component-library'
import { logger } from 'src/utils/exceptionLogging'

import { CustomPhaseConfig, DisplayName } from '../types'
import useCustomPhaseConfigUpsert from './hooks/useCustomPhaseConfigUpsert'

interface EventData {
  id?: string
  name: string
}

interface CustomPhaseDialogProps {
  allCustomPhaseConfigs: CustomPhaseConfig[]
  customPhaseConfig?: CustomPhaseConfig
  customPhaseEventData: EventData[]
  handleCustomConfigMutationComplete: (customPhaseId: string) => void
  isModalOpen: boolean
  setEditCustomConfig: (id: string) => void
  setIsOpenModal: (isOpen: boolean) => void
}

interface ApellaPhaseConfig {
  endEventTypeId: string
  name: string
  startEventTypeId: string
}

export const CustomPhaseDialog = ({
  customPhaseEventData,
  isModalOpen,
  setIsOpenModal,
  customPhaseConfig,
  handleCustomConfigMutationComplete,
  setEditCustomConfig,
  allCustomPhaseConfigs,
}: CustomPhaseDialogProps) => {
  const [columnTitle, setColumnTitle] = useState('')
  const [selectedEvent1, setSelectedEvent1] = useState('')
  const [selectedEvent2, setSelectedEvent2] = useState('')
  const [isValidEventTypeEntry, setIsValidEventTypeEntry] = useState(true)

  const missingRequiredField =
    selectedEvent1 === '' || selectedEvent2 === '' || columnTitle === ''

  const apellaConfigs: ApellaPhaseConfig[] = [
    {
      name: 'Prep',
      startEventTypeId: 'patient_wheels_in',
      endEventTypeId: 'patient_draped',
    },
    {
      name: 'Surgery',
      startEventTypeId: 'patient_draped',
      endEventTypeId: 'patient_undraped',
    },
    {
      name: 'Wrap-up',
      startEventTypeId: 'patient_undraped',
      endEventTypeId: 'patient_wheels_out',
    },
    {
      name: DisplayName.PREP_ANESTHESIA,
      startEventTypeId: 'patient_wheels_in',
      endEventTypeId: 'intubation',
    },
    {
      name: DisplayName.PREP_ANESTHESIA,
      startEventTypeId: 'patient_wheels_in',
      endEventTypeId: 'anesthesia_undraping',
    },
    {
      name: DisplayName.PREP_SURGERY,
      startEventTypeId: 'anesthesia_undraping',
      endEventTypeId: 'patient_draped',
    },
    {
      name: DisplayName.PREP_SURGERY,
      startEventTypeId: 'intubation',
      endEventTypeId: 'patient_draped',
    },
  ]

  let isDuplicateColumnConfig = false
  let isDuplicateName = false
  let duplicateConfigName = ''

  allCustomPhaseConfigs.forEach((config) => {
    if (
      config.id !== customPhaseConfig?.id &&
      config.startEventTypeId === selectedEvent1 &&
      config.endEventTypeId === selectedEvent2
    ) {
      isDuplicateColumnConfig = true
      duplicateConfigName = config.name
    } else if (
      config.id !== customPhaseConfig?.id &&
      config.name === columnTitle
    ) {
      isDuplicateName = true
    }
  })

  apellaConfigs.forEach((config) => {
    if (
      config.startEventTypeId === selectedEvent1 &&
      config.endEventTypeId === selectedEvent2
    ) {
      isDuplicateColumnConfig = true
      duplicateConfigName = config.name
    } else if (config.name === columnTitle) {
      isDuplicateName = true
    }
  })

  const { doUpsertCustomPhaseConfig } = useCustomPhaseConfigUpsert(
    handleCustomConfigMutationComplete
  )

  useEffect(() => {
    if (
      selectedEvent1 === selectedEvent2 &&
      selectedEvent1 !== '' &&
      selectedEvent2 !== ''
    ) {
      setIsValidEventTypeEntry(false)
    } else {
      setIsValidEventTypeEntry(true)
    }
  }, [selectedEvent1, selectedEvent2])

  useEffect(() => {
    if (customPhaseConfig) {
      setColumnTitle(customPhaseConfig.name)
      setSelectedEvent1(customPhaseConfig.startEventTypeId)
      setSelectedEvent2(customPhaseConfig.endEventTypeId)
    }
  }, [customPhaseConfig])

  const resetModalValues = () => {
    // Reset the custom phase config id to empty string.
    setEditCustomConfig('')
    setIsValidEventTypeEntry(true)
    setColumnTitle('')
    setSelectedEvent1('')
    setSelectedEvent2('')
  }

  const handleCustomPhaseSubmit = async () => {
    const startName = customPhaseEventData.find(
      (e) => e.id === selectedEvent1
    )?.name
    const endName = customPhaseEventData.find(
      (e) => e.id === selectedEvent2
    )?.name

    try {
      await doUpsertCustomPhaseConfig({
        id: customPhaseConfig?.id ?? '',
        startEventType: selectedEvent1,
        endEventType: selectedEvent2,
        name: columnTitle,
        description: `${startName} to ${endName}`,
      })
      toast.success(
        <ToastMessage
          title={
            customPhaseConfig?.id
              ? 'Custom phase updated'
              : 'Custom phase added'
          }
          message=""
          theme={theme}
        />
      )
      setIsOpenModal(false)
      resetModalValues()
      setIsValidEventTypeEntry(true)
    } catch (error) {
      logger.warn('Failed to create custom phase:', { error: String(error) })
      toast.error(
        <ToastMessage
          title="Failed to add custom phase"
          message="An error occurred. Please try again or contact support for assistance."
          theme={theme}
        />
      )
    }
  }

  const showErrorMessage =
    !isValidEventTypeEntry || isDuplicateColumnConfig || isDuplicateName

  return (
    <Dialog
      title="Create custom phase"
      isOpen={isModalOpen}
      onClose={() => {
        setIsOpenModal(false)
        resetModalValues()
        setIsValidEventTypeEntry(true)
      }}
      height={showErrorMessage ? '340px' : '300px'}
      overflow="visible !important"
    >
      <div
        css={{
          display: 'flex',
          flexDirection: 'column',
          color: theme.palette.text.secondary,
        }}
      >
        <P3>
          A custom phase duration may be calculated from any two events on the
          Apella case timeline
        </P3>
        <H6 css={{ marginTop: remSpacing.medium }}>Column name</H6>
        <InputText
          name="customPhaseName"
          value={columnTitle}
          placeholder="Add a name for the phase being calculated"
          onChange={(e) => {
            setColumnTitle(e.target.value)
          }}
          autofocus
          autoComplete={false}
        />
        <div
          css={{
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <SingleSelect
            name={'col1'}
            label={
              customPhaseEventData.find((opt) => opt.id === selectedEvent1)
                ?.name ?? 'Select first event'
            }
            css={{ flex: 1 }}
            search={true}
            onChange={(e) => {
              setSelectedEvent1(e ?? '')
            }}
          >
            {customPhaseEventData.map((item) => (
              <Option key={item.id} value={item.id} label={item.name} />
            ))}
          </SingleSelect>

          <ArrowBack
            css={{
              rotate: '180deg',
              marginTop: remSpacing.small,

              marginLeft: remSpacing.medium,
              marginRight: remSpacing.medium,
            }}
          />
          <SingleSelect
            name={'col2'}
            label={
              customPhaseEventData.find((opt) => opt.id === selectedEvent2)
                ?.name ?? 'Select second event'
            }
            css={{ flex: 1 }}
            search={true}
            onChange={(e) => {
              setSelectedEvent2(e ?? '')
            }}
          >
            {customPhaseEventData.map((item) => (
              <Option key={item.id} value={item.id} label={item.name} />
            ))}
          </SingleSelect>
        </div>
      </div>
      <div
        css={{
          display: 'flex',
          marginTop: remSpacing.medium,
          position: 'absolute',
          bottom: remSpacing.medium,
          right: remSpacing.medium,
          justifyContent: 'flex-end',
        }}
      >
        <Button
          css={{ padding: remSpacing.xxsmall }}
          appearance="link"
          color="primary"
          buttonType="icon"
          onClick={() => {
            setIsOpenModal(false)
            resetModalValues()
            setIsValidEventTypeEntry(true)
          }}
        >
          Cancel
        </Button>
        <Button
          css={{
            margin: `0 ${remSpacing.xsmall}`,
          }}
          color="primary"
          onClick={handleCustomPhaseSubmit}
          disabled={
            !isValidEventTypeEntry ||
            isDuplicateColumnConfig ||
            isDuplicateName ||
            missingRequiredField
          }
        >
          {customPhaseConfig?.id ? 'Update' : 'Add Column'}
        </Button>
      </div>
      {!isValidEventTypeEntry && (
        <P3
          css={{
            color: theme.palette.red[50],
            display: 'flex',
            marginTop: remSpacing.medium,
            marginBottom: remSpacing.large,
            justifyContent: 'right',
          }}
        >
          Start event and end events cannot be the same.
        </P3>
      )}
      {isDuplicateColumnConfig && (
        <P3
          css={{
            color: theme.palette.red[50],
            display: 'flex',
            marginTop: remSpacing.medium,
            justifyContent: 'right',
          }}
        >
          Column for these events already exists: {duplicateConfigName}.
        </P3>
      )}
      {isDuplicateName && (
        <P3
          css={{
            color: theme.palette.red[50],
            display: 'flex',
            marginTop: remSpacing.medium,
            justifyContent: 'right',
          }}
        >
          Column with this name already exists.
        </P3>
      )}
    </Dialog>
  )
}
