import { Duration } from 'luxon'

import { ApellaDateTimeFormats } from '@apella/component-library'
import { useCaseIdToClinicalDataMap } from 'src/modules/insights/hooks/useClinicalData'
import {
  displayCustomerCaseIdCsv,
  generateListCsvDisplay,
} from 'src/pages/Insights/dataHelpers'
import { convertDurationToMinutesString } from 'src/pages/Insights/dateTimeHelpers'
import useFirstCaseTableData from 'src/pages/Insights/FirstCaseStart/hooks/useFirstCaseTableData'
import { DisplayName } from 'src/pages/Insights/types'

export const useFirstCaseStartTableExport = ({
  orderBy,
  sortKeys,
  cubeParams,
}: Omit<Parameters<typeof useFirstCaseTableData>[0], 'limit' | 'offset'>) => {
  const { tableData: rawTableData } = useFirstCaseTableData({
    cubeParams,
    offset: 0,
    limit: undefined, // Do not limit the number of rows.
    orderBy,
    sortKeys,
  })

  const { caseIdToClinicalData } = useCaseIdToClinicalDataMap(
    rawTableData.map((edge) => edge.caseId)
  )

  return rawTableData.map((edge) => {
    const clinicalData = caseIdToClinicalData.get(edge.caseId)
    return {
      [DisplayName.CUSTOMER_CASE_ID]: displayCustomerCaseIdCsv(
        edge.customerCaseId
      ),
      [DisplayName.PRIMARY_SURGEONS]: generateListCsvDisplay(
        clinicalData?.surgeonNames
      ),
      [DisplayName.ANESTHESIA]: generateListCsvDisplay(
        clinicalData?.anesthesiaNames
      ),
      [DisplayName.SERVICE_LINE]: edge.serviceLineName,
      [DisplayName.PROCEDURES]: generateListCsvDisplay(
        clinicalData?.procedureNames
      ),
      [DisplayName.ROOMS]: edge.roomName,
      [DisplayName.SCHEDULED_START]: edge.scheduledStartTime.toLocaleString(
        ApellaDateTimeFormats.DATETIME
      ),
      [DisplayName.DAY]: edge.scheduledStartTime.weekdayLong,
      [DisplayName.ACTUAL_START_TIME]: edge.actualStartTime.toLocaleString(
        ApellaDateTimeFormats.DATETIME
      ),
      [DisplayName.MINUTES_LATE]: convertDurationToMinutesString(
        edge.lateMinutes,
        'floor'
      ),
      [DisplayName.DELAY_COMMENTS]: edge.delayComments,
      [DisplayName.DELAY_DURATION]: edge.delayDurationSeconds
        ? convertDurationToMinutesString(
            Duration.fromObject({
              seconds: parseInt(edge.delayDurationSeconds),
            })
          )
        : undefined,
      [DisplayName.DELAY_REASON]: edge.delayReason,
      [DisplayName.DELAY_TYPE]: edge.delayType,
    }
  })
}

export default useFirstCaseStartTableExport
