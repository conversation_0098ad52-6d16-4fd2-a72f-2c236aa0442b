import { useEffect } from 'react'
import { useNavigate } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'

import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { LocationPath } from 'src/router/types'

import Insights from '../Insights'
import { METRICS } from '../types'

export const FirstCaseStartPage = (): React.JSX.Element => {
  const navigate = useNavigate()
  const { acotsEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  useEffect(() => {
    if (acotsEnabled) {
      navigate(LocationPath.CaseStarts)
    } else {
      navigate(LocationPath.FirstCaseStarts)
    }
  }, [acotsEnabled, navigate])

  return <Insights metric={METRICS.FIRST_CASE_START} />
}
