import { useFlags } from 'launchdarkly-react-client-sdk'

import { DayOfWeekFilter } from 'src/components/DayOfWeekFilter'
import { InsightsDatePicker } from 'src/components/InsightsDatePicker'
import { FilterBarContainer } from 'src/components/PageContentTemplate'
import { ResetFilters } from 'src/components/ResetFilters'
import { ServiceLinesFilterWithCount } from 'src/components/ServiceLinesFilter'
import { SitesRoomsFilter } from 'src/components/SitesRoomsFilter'
import { CASES } from 'src/modules/cube/types/dataCubes'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { CaseOnTimeStartFilter } from 'src/pages/Insights/CaseOnTimeStartFilter'
import AnesthesiaFilterDropdown from 'src/pages/Insights/common/AnesthesiaFilterDropdown'
import PrimarySurgeonsFilterDropdown from 'src/pages/Insights/common/PrimarySurgeonsFilterDropdown'
import ProcedureFilterDropdown from 'src/pages/Insights/common/ProceduresFilterDropdown'
import FirstCaseStartTable from 'src/pages/Insights/FirstCaseStart/FirstCaseStartTable'
import { useRoomOptions, useSiteOptions } from 'src/utils/useSiteRoomsOptions'

import { DelayTypeFilter } from '../DelayTypeFilter'
import { MetricsContainer } from '../MetricsContainer'
import { METRICS } from '../types'
import { useInsightsSearchParams } from '../useInsightsSearchParams'
import FirstCaseStartGraph from './FirstCaseStartGraph'
import FirstCaseStartSummaryTile from './FirstCaseStartSummaryTile'

export const FirstCaseStart = () => {
  const { acotsEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const {
    minTime,
    maxTime,
    daysOfWeek,
    siteIds,
    roomIds,
    serviceLineIds,
    showResetFiltersButton,
    onChangeSites,
    onChangeRooms,
    onChangeServiceLines,
    onChangeDaysOfWeek,
    localOnChangeDateRanges,
    resetActions,
    cubeParams,
    caseType,
    onChangeCaseType,
    onChangeDelayType,
    delayType,
  } = useInsightsSearchParams(
    acotsEnabled ? METRICS.CASE_START : METRICS.FIRST_CASE_START
  )
  const { delayDataEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const { sites } = useSiteOptions()
  const rooms = useRoomOptions()

  return (
    <>
      <FilterBarContainer>
        <InsightsDatePicker
          minTime={minTime}
          maxTime={maxTime}
          onChangeDateRanges={localOnChangeDateRanges}
        />
        <DayOfWeekFilter selected={daysOfWeek} onChange={onChangeDaysOfWeek} />
        <SitesRoomsFilter
          sites={sites}
          selectedSiteIds={siteIds}
          onChangeSites={onChangeSites}
          rooms={rooms}
          selectedRoomIds={roomIds}
          onChangeRooms={onChangeRooms}
          multipleSites
          bulkSelectSites
          bulkSelectRooms
        />
        {acotsEnabled && (
          <CaseOnTimeStartFilter value={caseType} onChange={onChangeCaseType} />
        )}
        {delayDataEnabled && (
          <DelayTypeFilter selected={delayType} onChange={onChangeDelayType} />
        )}
        <PrimarySurgeonsFilterDropdown metric={METRICS.FIRST_CASE_START} />
        <AnesthesiaFilterDropdown metric={METRICS.FIRST_CASE_START} />
        <ServiceLinesFilterWithCount
          cubeParams={cubeParams}
          measure={CASES.COUNT}
          value={serviceLineIds}
          onChange={onChangeServiceLines}
        />
        <ProcedureFilterDropdown metric={METRICS.FIRST_CASE_START} />
        {showResetFiltersButton && <ResetFilters resetActions={resetActions} />}
      </FilterBarContainer>
      <MetricsContainer
        graph={<FirstCaseStartGraph />}
        summaryTile={<FirstCaseStartSummaryTile />}
        table={<FirstCaseStartTable />}
      />
    </>
  )
}
