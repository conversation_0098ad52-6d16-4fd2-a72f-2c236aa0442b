import { useCubeQuery } from '@cubejs-client/react'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { DateTime, DurationLikeObject } from 'luxon'

import {
  InsightsSummaryMetric,
  InsightsTile,
  MetricsSection,
  MetricType,
  SingleMetricWithPadding,
} from 'src/components/InsightsTile/InsightsTile'
import { METRIC_DIRECTION } from 'src/components/InsightsTile/PercentChangeIndicator'
import { useTimezone } from 'src/Contexts'
import { CASES } from 'src/modules/cube/types/dataCubes'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { FirstCaseMeasures } from 'src/pages/Insights/FirstCaseStart/firstCaseMeasures'
import { getNewFirstCaseStartQueryParams } from 'src/pages/Insights/queries'
import { METRICS, OnTimeStartType } from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'

import { FirstCaseHoursBladeButton } from './FirstCaseHoursBlade'

interface SummaryTileDataAnalytics {
  [CASES.AVG_LATE_MINUTES]: DurationLikeObject
  [CASES.COUNT]: string
  [CASES.COUNT_LATE_STARTS]: string
  [CASES.COUNT_LATE_WITHIN_5_MINUTES]: string
}

export interface CommonTileData {
  averageLateMinutes: DurationLikeObject
  count: string
  countLateStart: string
  startWithinFiveMinutes: string
}

const FirstCaseStartSummaryTile = (): React.JSX.Element => {
  const { customFcotsDisplay: isCustomFcotsDisplayEnabled, acotsEnabled } =
    useFlags<WebDashboardFeatureFlagSet>()

  const {
    minTime,
    maxTime,
    daysOfWeek,
    siteIds,
    roomIds,
    staffIds,
    anesthesiaIds,
    procedureIds,
    cubeParams,
    caseType,
  } = useInsightsSearchParams(
    acotsEnabled ? METRICS.CASE_START : METRICS.FIRST_CASE_START
  )

  const { timezone } = useTimezone()

  const measures = [
    CASES.COUNT,
    CASES.COUNT_LATE_STARTS,
    CASES.COUNT_LATE_WITHIN_5_MINUTES,
    CASES.AVG_LATE_MINUTES,
  ]

  const {
    isLoading: isLoadingSummaryTileDataResult,
    resultSet: summaryTileDataResult,
  } = useCubeQuery<SummaryTileDataAnalytics>({
    ...cubeParams,
    measures: measures,
  })

  const getAnalyticsData = (
    analyticsCaseData: SummaryTileDataAnalytics[]
  ): CommonTileData[] => {
    return analyticsCaseData.map((edge) => ({
      averageLateMinutes: edge[CASES.AVG_LATE_MINUTES],
      countLateStart: edge[CASES.COUNT_LATE_STARTS],
      startWithinFiveMinutes: edge[CASES.COUNT_LATE_WITHIN_5_MINUTES],
      count: edge[CASES.COUNT],
    }))
  }

  const analyticsData = getAnalyticsData(summaryTileDataResult?.rawData() ?? [])

  const summaryTileData = analyticsData[0]
  const minDateTime = DateTime.fromISO(minTime)
  const maxDateTime = DateTime.fromISO(maxTime)
  const diff = maxDateTime.diff(minDateTime)
  const prevMinTime = minDateTime.minus(diff)

  const prevParams = getNewFirstCaseStartQueryParams({
    minDateTime: prevMinTime.toISO(),
    maxDateTime: minTime,
    daysOfWeek,
    siteIds,
    roomIds,
    staffIds,
    anesthesiaIds,
    procedureIds,
    timezone,
  })

  const {
    isLoading: isLoadingPreviousDataResult,
    resultSet: previousDataResult,
  } = useCubeQuery<SummaryTileDataAnalytics>({
    ...prevParams,
    measures: measures,
  })
  const finalDataPrev = getAnalyticsData(previousDataResult?.rawData() ?? [])

  const previousData = finalDataPrev[0]

  const gracePeriodDuration = '1 minute'

  const onTimeStartsPercentMetric: InsightsSummaryMetric = (() => {
    const onTimeStartRatio =
      summaryTileData && Number(summaryTileData.count) != 0
        ? 1.0 -
          Number(summaryTileData.countLateStart) / Number(summaryTileData.count)
        : undefined
    const previousOnTimeStartRatio =
      previousData && Number(previousData.count) != 0
        ? 1.0 - Number(previousData.countLateStart) / Number(previousData.count)
        : undefined

    return {
      label: FirstCaseMeasures.ONTIME_STARTS_PERCENT,
      value: onTimeStartRatio,
      type: MetricType.PERCENT,
      description: `The percentage of first cases which were on-time for the selected filters. A case
      is considered on-time if the patient is wheeled in less than ${gracePeriodDuration} after the scheduled case start time.`,
      previousValue: previousOnTimeStartRatio,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
      desiredChangeDirection: METRIC_DIRECTION.POSITIVE,
    }
  })()

  const lateStartsPercentMetric: InsightsSummaryMetric = (() => {
    const lateStartRatio =
      summaryTileData && Number(summaryTileData.count) != 0
        ? Number(summaryTileData.countLateStart) / Number(summaryTileData.count)
        : undefined
    const previousLateStartRatio =
      previousData && Number(previousData.count) != 0
        ? Number(previousData.countLateStart) / Number(previousData.count)
        : undefined

    // Refrain from showing percent change indicator for late starts as it's the same as for ontime starts
    return {
      label: FirstCaseMeasures.LATE_STARTS_PERCENT,
      value: lateStartRatio,
      type: MetricType.PERCENT,
      description: `The percentage of first cases which were late for the selected filters. A case is
      considered late if the patient is wheeled in at least ${gracePeriodDuration} after the scheduled case start time.`,
      previousValue: previousLateStartRatio,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
      desiredChangeDirection: METRIC_DIRECTION.NEGATIVE,
    }
  })()

  const withinFiveMinutesPercentMetric: InsightsSummaryMetric = (() => {
    const withinFiveMinuteRatio =
      summaryTileData && Number(summaryTileData.count) != 0
        ? Number(summaryTileData.startWithinFiveMinutes) /
          Number(summaryTileData.count)
        : undefined
    const previousWithinFiveMinuteRatio =
      previousData && Number(previousData.count) != 0
        ? Number(previousData.startWithinFiveMinutes) /
          Number(previousData.count)
        : undefined

    // Refrain from showing percent change indicator for late starts as it's the same as for ontime starts
    return {
      label: FirstCaseMeasures.WITHIN_FIVE_MINUTES,
      value: withinFiveMinuteRatio,
      type: MetricType.PERCENT,
      description: `The percentage of first cases that started within 5 minutes of the scheduled case start time for the selected filters.`,
      previousValue: previousWithinFiveMinuteRatio,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
      desiredChangeDirection: METRIC_DIRECTION.POSITIVE,
    }
  })()

  const averageLateDurationPercentMetric = (() => {
    const averageLateDuration =
      summaryTileData && summaryTileData.averageLateMinutes
        ? durationFromMinutes(summaryTileData.averageLateMinutes)
        : undefined
    const previousAverageLateDuration =
      previousData && previousData.averageLateMinutes
        ? durationFromMinutes(previousData.averageLateMinutes)
        : undefined

    return {
      label: FirstCaseMeasures.LATE_AVG_DURATION,
      value: averageLateDuration,
      description: `The average number of minutes the first cases were late for the selected filters.`,
      previousValue: previousAverageLateDuration,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
      desiredChangeDirection: METRIC_DIRECTION.NEGATIVE,
    }
  })()

  const countMetric = (() => {
    const count = summaryTileData ? Number(summaryTileData.count) : undefined
    const previousCount = previousData ? Number(previousData.count) : undefined

    return {
      label: 'Total Cases',
      value: count,
      description: 'Case count for selected filters.',
      previousValue: previousCount,
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      previousMinDateTime: prevMinTime,
    }
  })()

  const middleMetrics = [onTimeStartsPercentMetric, lateStartsPercentMetric]

  const bottomMetrics = [
    averageLateDurationPercentMetric,
    withinFiveMinutesPercentMetric,
  ]

  const isLoading =
    isLoadingSummaryTileDataResult || isLoadingPreviousDataResult

  return (
    <InsightsTile
      metricsSections={[
        <div key="top">
          <div css={{ display: 'grid', gridTemplateColumns: '1fr 1fr' }}>
            <SingleMetricWithPadding
              isLoading={isLoading}
              metric={countMetric}
            />
            <div>
              {isCustomFcotsDisplayEnabled &&
                caseType === OnTimeStartType.FCOTS && (
                  <FirstCaseHoursBladeButton siteIds={siteIds} />
                )}
            </div>
          </div>
        </div>,
        <MetricsSection
          key="middle"
          metrics={middleMetrics}
          isLoading={isLoading}
        />,
        <MetricsSection
          key="bottom"
          metrics={bottomMetrics}
          isLoading={isLoading}
        />,
      ]}
    />
  )
}

export default FirstCaseStartSummaryTile
