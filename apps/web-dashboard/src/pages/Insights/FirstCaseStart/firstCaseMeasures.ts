import { CASES } from 'src/modules/cube/types/dataCubes'

export enum FirstCaseMeasures {
  LATE_STARTS_PERCENT = 'Late starts',
  ONTIME_STARTS_PERCENT = 'On time starts',
  WITHIN_FIVE_MINUTES = 'Within 5 min grace period',
  LATE_AVG_DURATION = 'Late average',
  TOTAL_FIRST_CASES = 'Total first cases',
}

export enum AllCaseMeasures {
  TOTAL_ALL_CASES = 'Total all cases',
}

export enum NotFirstCaseMeasures {
  TOTAL_NOT_FIRST_CASES = 'Total not first cases',
}

export const FirstCaseMeasuresToOrderBy = {
  [FirstCaseMeasures.LATE_STARTS_PERCENT]: [CASES.LATE_STARTS_PERCENT, 'desc'],
  [FirstCaseMeasures.WITHIN_FIVE_MINUTES]: [
    CASES.WITHIN_FIVE_MINUTE_GRACE_PERIOD_PERCENT,
    'desc',
  ],
  [FirstCaseMeasures.ONTIME_STARTS_PERCENT]: [
    CASES.ON_TIME_STARTS_PERCENT,
    'desc',
  ],
  [FirstCaseMeasures.LATE_AVG_DURATION]: [CASES.AVG_LATE_MINUTES, 'desc'],
  [FirstCaseMeasures.TOTAL_FIRST_CASES]: [CASES.COUNT, 'desc'],
} as const
