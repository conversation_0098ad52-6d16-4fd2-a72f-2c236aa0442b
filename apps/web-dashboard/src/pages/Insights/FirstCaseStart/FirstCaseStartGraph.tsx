import { useCallback, useMemo } from 'react'

import { useTheme } from '@emotion/react'

import { TimeDimension } from '@cubejs-client/core'
import { useCubeQuery } from '@cubejs-client/react'
import { useFlags } from 'launchdarkly-react-client-sdk'
import { toSafeInteger } from 'lodash'
import { DateTime, Duration, DurationLikeObject } from 'luxon'

import { ChartData, ChartMeta, ComponentTheme } from '@apella/component-library'
import { InsightsGraph } from 'src/components/InsightsGraph'
import { DimensionDropdown } from 'src/components/InsightsGraph/DimensionDropdown'
import { useServiceLines } from 'src/components/ServiceLinesFilter'
import { useTimezone } from 'src/Contexts'
import { toTimeDimensionGranularity } from 'src/modules/cube/adapters'
import {
  CASE_PROCEDURES,
  CASE_STAFF_ANESTHESIA,
  CASE_STAFF_SURGEONS,
  CASES,
  SERVICE_LINES,
} from 'src/modules/cube/types/dataCubes'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import {
  getProcedureIdByName,
  getProcedureNameById,
  getRoomIdByName,
  getRoomNameById,
  getServiceLineIdByName,
  getServiceLineNameById,
  getStaffIdByName,
  getStaffNameById,
} from 'src/pages/Insights/dataHelpers'
import {
  FirstCaseMeasures,
  FirstCaseMeasuresToOrderBy,
} from 'src/pages/Insights/FirstCaseStart/firstCaseMeasures'
import { formatTimeDimensionDateRange } from 'src/pages/Insights/queries'
import { DayOfWeek, METRICS, OnTimeStartType } from 'src/pages/Insights/types'
import { useInsightsSearchParams } from 'src/pages/Insights/useInsightsSearchParams'
import { durationFromMinutes } from 'src/utils/bigQueryCubeHelpers'
import {
  getBucketEndDate,
  NonTimeDimensions,
  TimeDimensions,
} from 'src/utils/bucketHelpers'
import { useSiteOptions } from 'src/utils/useSiteRoomsOptions'

interface AnalyticsCaseData {
  [CASE_PROCEDURES.PROCEDURE_ID]: string
  [CASE_STAFF_ANESTHESIA.STAFF_ID]: string
  [CASE_STAFF_SURGEONS.STAFF_ID]: string
  [CASES.ACTUAL_DAY_OF_WEEK]: string
  [CASES.AVG_LATE_MINUTES]: DurationLikeObject
  [CASES.COUNT]: string
  [CASES.COUNT_LATE_STARTS]: string
  [CASES.LATE_STARTS_PERCENT]: string
  [CASES.ON_TIME_STARTS_PERCENT]: string
  [CASES.ROOM_ID]: string
  [CASES.SCHEDULED_START_TIMESTAMP]: string
  [CASES.WITHIN_FIVE_MINUTE_GRACE_PERIOD_PERCENT]: string
  [SERVICE_LINES.ID]: string
}

export interface CommonGraphData {
  anesthesiaStaffId?: string
  avgLateMinutes: DurationLikeObject
  count: string
  countLateStart: string
  dayOfWeek: string
  lateStartsPercent: string
  onTimeStartsPercent: string
  proceduresId?: string
  roomId?: string
  scheduleStartDateTime: string
  serviceLineId?: string
  staffId?: string
  withinFiveMinuteGracePeriodPercent: string
}

const getDimensionFromResult = (
  data: CommonGraphData,
  timezone: string,
  staffNameById: Map<string, string>,
  anesthesiaStaffNameById: Map<string, string>,
  serviceLineNameById: Map<string, string>,
  procedureNameById: Map<string, string>,
  roomNameById?: Map<string, string>
): string => {
  if (data.scheduleStartDateTime) {
    return DateTime.fromISO(data.scheduleStartDateTime, {
      zone: timezone,
    }).toISO()
  }

  return (
    data.dayOfWeek ||
    (data.roomId && roomNameById?.get(data.roomId)) ||
    (data.staffId && staffNameById.get(data.staffId)) ||
    (data.anesthesiaStaffId &&
      anesthesiaStaffNameById.get(data.anesthesiaStaffId)) ||
    (data.serviceLineId && serviceLineNameById.get(data.serviceLineId)) ||
    (data.proceduresId && procedureNameById.get(data.proceduresId)) ||
    ''
  )
}
const getValueFromResult = (
  measure: FirstCaseMeasures,
  data: CommonGraphData
): number | Duration => {
  switch (measure) {
    case FirstCaseMeasures.LATE_STARTS_PERCENT:
      return Number(data.lateStartsPercent) / 100
    case FirstCaseMeasures.ONTIME_STARTS_PERCENT:
      return Number(data.onTimeStartsPercent) / 100
    case FirstCaseMeasures.WITHIN_FIVE_MINUTES:
      return Number(data.withinFiveMinuteGracePeriodPercent) / 100
    case FirstCaseMeasures.LATE_AVG_DURATION:
      return data.avgLateMinutes ? durationFromMinutes(data.avgLateMinutes) : 0
    case FirstCaseMeasures.TOTAL_FIRST_CASES:
    default:
      return toSafeInteger(data.count)
  }
}

const FirstCaseStartGraph = (): React.JSX.Element => {
  const { acotsEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const {
    minTime,
    maxTime,
    dimension,
    graphSort,
    siteIds,

    anesthesiaStaffWithCount,
    proceduresWithCount,
    staffWithCount,

    onChangeRooms,
    onChangeStaff,
    onChangeAnesthesia,
    onChangeServiceLines,
    onChangeProcedure,
    onChangeGraphOrderBy,
    localOnChangeDateRanges,

    onChangeDimension,
    onChangeDaysOfWeek,

    cubeParams,
    caseType,
  } = useInsightsSearchParams(
    acotsEnabled ? METRICS.CASE_START : METRICS.FIRST_CASE_START
  )

  const { sites } = useSiteOptions()
  const theme: ComponentTheme = useTheme()
  const { timezone } = useTimezone()
  const { serviceLines } = useServiceLines()

  const staffNameById = useMemo(
    () => getStaffNameById(staffWithCount),
    [staffWithCount]
  )
  const staffIdByName = useMemo(
    () => getStaffIdByName(staffWithCount),
    [staffWithCount]
  )
  const anesthesiaStaffNameById = useMemo(
    () => getStaffNameById(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const anesthesiaStaffIdByName = useMemo(
    () => getStaffIdByName(anesthesiaStaffWithCount),
    [anesthesiaStaffWithCount]
  )
  const roomNameById = useMemo(
    () => getRoomNameById(sites, siteIds),
    [sites, siteIds]
  )
  const roomIdByName = useMemo(
    () => getRoomIdByName(sites, siteIds),
    [sites, siteIds]
  )

  const procedureNameById = useMemo(
    () => getProcedureNameById(proceduresWithCount),
    [proceduresWithCount]
  )
  const procedureIdByName = useMemo(
    () => getProcedureIdByName(proceduresWithCount),
    [proceduresWithCount]
  )

  const serviceLineNameById = useMemo(
    () => getServiceLineNameById(serviceLines),
    [serviceLines]
  )
  const serviceLineIdsByName = useMemo(
    () => getServiceLineIdByName(serviceLines),
    [serviceLines]
  )
  const getCubeJsDimensionFromNonTimeDimension = (
    nonTimeDimension: NonTimeDimensions
  ): keyof AnalyticsCaseData => {
    switch (nonTimeDimension) {
      case NonTimeDimensions.DAYOFWEEK:
        return CASES.ACTUAL_DAY_OF_WEEK
      case NonTimeDimensions.OR:
        return CASES.ROOM_ID
      case NonTimeDimensions.SURGEON:
        return CASE_STAFF_SURGEONS.STAFF_ID
      case NonTimeDimensions.ANESTHESIA:
        return CASE_STAFF_ANESTHESIA.STAFF_ID
      case NonTimeDimensions.SERVICE_LINE:
        return SERVICE_LINES.ID
      case NonTimeDimensions.PROCEDURE:
        return CASE_PROCEDURES.PROCEDURE_ID
      default:
        throw new Error(
          `Unsupported non-time dimension of: ${nonTimeDimension}`
        )
    }
  }

  const measures = [
    CASES.COUNT,
    CASES.COUNT_LATE_STARTS,
    CASES.LATE_STARTS_PERCENT,
    CASES.ON_TIME_STARTS_PERCENT,
    CASES.WITHIN_FIVE_MINUTE_GRACE_PERIOD_PERCENT,
    CASES.AVG_LATE_MINUTES,
  ]

  const timeDimension = (() => {
    const timeDimension: TimeDimension = {
      dimension: CASES.SCHEDULED_START_TIMESTAMP,
      dateRange: formatTimeDimensionDateRange([minTime, maxTime], timezone),
    }
    if (dimension in TimeDimensions) {
      timeDimension.granularity = toTimeDimensionGranularity(
        dimension as TimeDimensions
      )
    }
    return timeDimension
  })()

  const dimensions = (() => {
    const dimensions = []
    if (dimension in NonTimeDimensions) {
      const nonTimeDimension = getCubeJsDimensionFromNonTimeDimension(
        dimension as NonTimeDimensions
      )
      dimensions.push(nonTimeDimension)
    }
    return dimensions
  })()

  const { resultSet: data, isLoading } = useCubeQuery<AnalyticsCaseData>({
    ...cubeParams,
    measures,
    timeDimensions: [timeDimension],
    dimensions,
    order: [FirstCaseMeasuresToOrderBy[graphSort as FirstCaseMeasures]],
  })

  // Convert the data to chart data format
  const chartData = useMemo(() => {
    const finalData: CommonGraphData[] = (data?.rawData() ?? []).map(
      (edge) => ({
        count: edge[CASES.COUNT],
        countLateStart: edge[CASES.COUNT_LATE_STARTS],
        lateStartsPercent: edge[CASES.LATE_STARTS_PERCENT],
        onTimeStartsPercent: edge[CASES.ON_TIME_STARTS_PERCENT],
        withinFiveMinuteGracePeriodPercent:
          edge[CASES.WITHIN_FIVE_MINUTE_GRACE_PERIOD_PERCENT],
        avgLateMinutes: edge[CASES.AVG_LATE_MINUTES],
        scheduleStartDateTime: edge[CASES.SCHEDULED_START_TIMESTAMP],
        staffId: edge[CASE_STAFF_SURGEONS.STAFF_ID],
        anesthesiaStaffId: edge[CASE_STAFF_ANESTHESIA.STAFF_ID],
        serviceLineId: edge[SERVICE_LINES.ID],
        proceduresId: edge[CASE_PROCEDURES.PROCEDURE_ID],
        roomId: edge[CASES.ROOM_ID],
        dayOfWeek: edge[CASES.ACTUAL_DAY_OF_WEEK]
          ? edge[CASES.ACTUAL_DAY_OF_WEEK]
          : '',
      })
    )

    const chartData: ChartData[] = []
    finalData.forEach((data) => {
      const baseData = {
        dimension: getDimensionFromResult(
          data,
          timezone,
          staffNameById,
          anesthesiaStaffNameById,
          serviceLineNameById,
          procedureNameById,
          roomNameById
        ),
      }
      // TODO: can probably iterate through measures.
      const onTimeStartsData = {
        ...baseData,
        identifier: FirstCaseMeasures.ONTIME_STARTS_PERCENT,
        value: getValueFromResult(
          FirstCaseMeasures.ONTIME_STARTS_PERCENT,
          data
        ),
        type: 'percent',
      }
      const lateStartsData = {
        ...baseData,
        identifier: FirstCaseMeasures.LATE_STARTS_PERCENT,
        value: getValueFromResult(FirstCaseMeasures.LATE_STARTS_PERCENT, data),
        type: 'percent',
      }
      const lateAverageDurationData = {
        ...baseData,
        identifier: FirstCaseMeasures.LATE_AVG_DURATION,
        value: getValueFromResult(FirstCaseMeasures.LATE_AVG_DURATION, data),
      }
      const withinFiveMinutesData = {
        ...baseData,
        identifier: FirstCaseMeasures.WITHIN_FIVE_MINUTES,
        value: getValueFromResult(FirstCaseMeasures.WITHIN_FIVE_MINUTES, data),
        type: 'percent',
      }
      const countData = {
        ...baseData,
        identifier: FirstCaseMeasures.TOTAL_FIRST_CASES,
        value: getValueFromResult(FirstCaseMeasures.TOTAL_FIRST_CASES, data),
      }

      chartData.push(
        onTimeStartsData,
        lateStartsData,
        withinFiveMinutesData,
        lateAverageDurationData,
        countData
      )
    })
    return chartData
  }, [
    data,
    timezone,
    staffNameById,
    anesthesiaStaffNameById,
    serviceLineNameById,
    procedureNameById,
    roomNameById,
  ])

  // When clicking on a graph group, change the desired filters.
  // Transform the bar group label that the component passes back to the ID required for filtering.
  const onClickBarGroup = useCallback(
    (dimensionValue: string) => {
      if (dimension in TimeDimensions) {
        const startDate = new Date(dimensionValue)
        const endDate = getBucketEndDate(startDate, dimension as TimeDimensions)
        localOnChangeDateRanges([startDate, endDate])
      } else if (dimension == NonTimeDimensions.DAYOFWEEK) {
        onChangeDaysOfWeek([dimensionValue.toLocaleLowerCase()] as DayOfWeek[])
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      } else if (dimension === NonTimeDimensions.OR) {
        onChangeRooms(
          roomIdByName.get(dimensionValue)
            ? [roomIdByName.get(dimensionValue)!]
            : []
        )
      } else if (dimension === NonTimeDimensions.SURGEON) {
        onChangeStaff(staffIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.ANESTHESIA) {
        onChangeAnesthesia(anesthesiaStaffIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.PROCEDURE) {
        onChangeProcedure(procedureIdByName.get(dimensionValue))
      } else if (dimension === NonTimeDimensions.SERVICE_LINE) {
        onChangeServiceLines(serviceLineIdsByName.get(dimensionValue))
      }

      if (dimension in NonTimeDimensions) {
        // For non-time dimensions, filter by dimension then refresh to a by-time graph
        // (triggering onChangeDateRanges will update the bucket accordingly: i.e Day if last month, Week if 2 months, etc.)
        localOnChangeDateRanges([new Date(minTime), new Date(maxTime)])
      }
    },
    [
      dimension,
      localOnChangeDateRanges,
      onChangeDaysOfWeek,
      minTime,
      maxTime,
      onChangeRooms,
      roomIdByName,
      onChangeStaff,
      staffIdByName,
      onChangeAnesthesia,
      anesthesiaStaffIdByName,
      onChangeProcedure,
      procedureIdByName,
      onChangeServiceLines,
      serviceLineIdsByName,
    ]
  )

  const chartMetaData: ChartMeta = {
    [FirstCaseMeasures.TOTAL_FIRST_CASES]: {
      ordinal: 1,
      hide: true,
    },
    [FirstCaseMeasures.LATE_AVG_DURATION]: {
      ordinal: 2,
      hide: true,
    },
    [FirstCaseMeasures.WITHIN_FIVE_MINUTES]: {
      ordinal: 3,
      hide: true,
      type: 'percent',
    },
    [FirstCaseMeasures.ONTIME_STARTS_PERCENT]: {
      ordinal: 4,
      type: 'percent',
      color: theme.palette.blue[50],
    },
    [FirstCaseMeasures.LATE_STARTS_PERCENT]: {
      ordinal: 5,
      type: 'percent',
      color: theme.palette.blue[10],
    },
  }

  return (
    <InsightsGraph
      dimension={dimension}
      data={chartData}
      meta={chartMetaData}
      isLoading={isLoading}
      minTime={minTime}
      maxTime={maxTime}
      title={
        caseType === OnTimeStartType.FCOTS
          ? 'First case start'
          : caseType === OnTimeStartType.ACOTS
            ? 'All case start'
            : 'Not first case start'
      }
      timezone={timezone}
      onClickBarGroup={onClickBarGroup}
      valueAxisScale={'linear'}
      valueAxisType={'percent'}
      graphSort={graphSort}
      onChangeGraphSort={onChangeGraphOrderBy}
      measures={Object.values(FirstCaseMeasures)}
      valueAxisDomain={[0, 1]}
    >
      <DimensionDropdown
        dimension={dimension}
        onChangeDimension={onChangeDimension}
      />
    </InsightsGraph>
  )
}

export default FirstCaseStartGraph
