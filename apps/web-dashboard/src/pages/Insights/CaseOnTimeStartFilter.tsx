import { Option, SingleSelect } from '@apella/component-library'

import { OnTimeStartType } from './types'

export const CaseOnTimeStartFilter = ({
  value,
  onChange,
}: {
  value: OnTimeStartType
  onChange: (value: OnTimeStartType) => void
}) => {
  return (
    <SingleSelect
      name="caseOnTimeStartFilter"
      label="Case On Time Start"
      value={value}
      onChange={(option) => onChange(option as OnTimeStartType)}
    >
      <Option
        key={OnTimeStartType.FCOTS}
        value={OnTimeStartType.FCOTS}
        label={'First cases'}
      />
      <Option
        key={OnTimeStartType.NFCOTS}
        value={OnTimeStartType.NFCOTS}
        label={'Not first cases'}
      />
      <Option
        key={OnTimeStartType.ACOTS}
        value={OnTimeStartType.ACOTS}
        label={'All Cases'}
      />
    </SingleSelect>
  )
}
