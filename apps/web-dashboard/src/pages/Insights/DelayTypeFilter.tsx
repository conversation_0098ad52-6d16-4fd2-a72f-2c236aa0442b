import { SingleSelect, Option } from '@apella/component-library'

export enum DelayType {
  PRESENT = 'present',
  NONE = 'none',
  ALL = 'all',
}
export const DelayTypeFilter = ({
  selected,
  onChange,
}: {
  selected: DelayType
  onChange: (newValue: DelayType) => void
}) => {
  const handleChange = (value?: string) => {
    const newDelayType = value as DelayType
    onChange(newDelayType)
  }

  return (
    <SingleSelect
      name="delay-type-filter"
      label="Delay Type"
      value={selected}
      onChange={(value) => handleChange(value)}
    >
      <Option value={DelayType.ALL} label="All" />
      <Option value={DelayType.NONE} label="Not Delayed" />
      <Option value={DelayType.PRESENT} label="Delayed" />
    </SingleSelect>
  )
}
