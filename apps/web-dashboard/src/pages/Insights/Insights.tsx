import { useMemo } from 'react'
import { generatePath, Outlet } from 'react-router'

import { useFlags } from 'launchdarkly-react-client-sdk'

import { PageContentTemplate } from 'src/components/PageContentTemplate'
import { UserViewContext } from 'src/components/UserViews/useUserFilterViews'
import { WebDashboardFeatureFlagSet } from 'src/modules/feature/types'
import { CaseLength } from 'src/pages/Insights/CaseLength/CaseLength'
import { FirstCaseStart } from 'src/pages/Insights/FirstCaseStart/FirstCaseStart'
import { Turnover } from 'src/pages/Insights/Turnover/Turnover'
import { LocationPath } from 'src/router/types'

import InsightsPageContextProvider, {
  useInsightsPageContext,
} from './InsightsContext'
import { METRICS } from './types'

const Insights = ({ metric }: { metric: METRICS }): React.JSX.Element => {
  return (
    <InsightsPageContextProvider metric={metric}>
      <InsightsPage />
      <Outlet />
    </InsightsPageContextProvider>
  )
}

const InsightsPage = (): React.JSX.Element => {
  const { selectedMetric } = useInsightsPageContext()
  const { acotsEnabled } = useFlags<WebDashboardFeatureFlagSet>()

  const DEFAULT_METRICS = [
    {
      id: METRICS.CASE.toString(),
      display: METRICS.CASE.toString(),
      to: generatePath(LocationPath.CaseLengths),
    },
    ...(acotsEnabled
      ? [
          {
            id: METRICS.CASE_START.toString(),
            display: METRICS.CASE_START.toString(),
            to: generatePath(LocationPath.CaseStarts),
          },
        ]
      : [
          {
            id: METRICS.FIRST_CASE_START.toString(),
            display: METRICS.FIRST_CASE_START.toString(),
            to: generatePath(LocationPath.FirstCaseStarts),
          },
        ]),
    {
      id: METRICS.TURNOVER.toString(),
      display: METRICS.TURNOVER.toString(),
      to: generatePath(LocationPath.Turnovers),
    },
  ]

  // Map the selectedMetric to the correct view ID
  const selectedViewId = useMemo(() => {
    if (!selectedMetric) return ''
    if (selectedMetric === METRICS.FIRST_CASE_START && acotsEnabled) {
      return METRICS.CASE_START.toString()
    }
    return selectedMetric.toString()
  }, [selectedMetric, acotsEnabled])

  return (
    <PageContentTemplate
      title="Insights"
      views={DEFAULT_METRICS}
      selectedViewId={selectedViewId}
      userViewContexts={[UserViewContext.Insights]}
    >
      {selectedMetric == METRICS.TURNOVER && <Turnover />}
      {selectedMetric == METRICS.FIRST_CASE_START && <FirstCaseStart />}
      {selectedMetric == METRICS.CASE && <CaseLength />}
    </PageContentTemplate>
  )
}

export default Insights
