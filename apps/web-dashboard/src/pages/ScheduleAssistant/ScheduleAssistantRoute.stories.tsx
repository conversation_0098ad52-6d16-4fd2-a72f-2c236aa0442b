import { RouteObject } from 'react-router'

import { <PERSON><PERSON><PERSON>, ApolloProvider, InMemoryCache } from '@apollo/client'
import cubejs from '@cubejs-client/core'
import { CubeProvider } from '@cubejs-client/react'
import { Meta, StoryObj } from '@storybook/react'
import { userEvent, waitFor, within, expect } from '@storybook/test'
import { delay, graphql, http, HttpResponse } from 'msw'

import { withRouter } from '@apella/react-router-storybook'
import { aRoomTag } from 'src/__generated__/generated-mocks'
import { TimezoneProvider } from 'src/Contexts'
import { GetSiteOptionsFilterDocument } from 'src/modules/site/__generated__'
import { GetSiteOptionsFilterData } from 'src/modules/site/__mocks__/GetSiteOptionsFilterData'
import { GetCurrentUserDocument } from 'src/modules/user/__generated__'
import { ApellaRouteContext } from 'src/router/types'
import MockLDProvider from 'src/test/MockLDProvider'

import { GetCurrentUserData, UserType } from '../test_mocks/GetCurrentUserData'
import {
  GetRoomTagsQueryDocument,
  GetApellaCasesInSiteDocument,
  GetAvailableTimeSlotsDocument,
  GetCaseDurationPredictionsDocument,
  GetCaseDurationTurnoverPredictionDocument,
  GetPrimarySurgeonInformationDocument,
  GetBlockTimesDocument,
  EmailAvailableTimesHtmlDocument,
  GetCaseDurationSurgeonsDocument,
  GetCaseDurationProceduresDocument,
} from './__generated__'
import { GetApellaCasesInSiteData } from './__mocks__/GetApellaCasesInSiteData'
import {
  GetAvailableTimeSlotsData,
  GetAvailableTimeSlotsDataNoResults,
} from './__mocks__/GetAvailableTimeSlotsData'
import { GetBlockTimesData } from './__mocks__/GetBlockTimesData'
import {
  GetCaseDurationPredictionsData,
  GetCaseDurationPredictionsMultiProcedureData,
  GetCaseDurationPredictionsMultiProcedureDataHighVariance,
} from './__mocks__/GetCaseDurationPredictionsData'
import {
  GetCaseDurationProceduresData,
  GetCaseDurationSurgeonsData,
} from './__mocks__/GetCaseDurationSurgeonsAndProceduresData'
import {
  GetPrimarySurgeonInformationNoBlocksData,
  GetPrimarySurgeonInformationThanosData,
} from './__mocks__/GetPrimarySurgeonInformationDocument'
import {
  GetRecentCasesData,
  GetRecentCasesDataNoResults,
} from './__mocks__/GetRecentCasesData'
import {
  AvailableTimesRoute as AvailableTimesComp,
  loader as availableTimesLoader,
} from './AvailableTimes/AvailableTimes'
import {
  clearRecentCasesCache,
  RecentCases,
  loader as recentCasesLoader,
} from './CaseDuration/RecentCases'
import {
  EstimateLayout,
  loader as estimateLayoutLoader,
} from './EstimateLayout'
import { Component as ScheduleAssistantRoute } from './ScheduleAssistantRoute'
import { SurgeonProcedureFiltersLayout } from './SurgeonProcedureFiltersLayout'
import {
  TimeSelection as TimeSelectionComp,
  loader as timeSelectionLoader,
} from './TimeSelection/TimeSelection'
import { Complexity } from './types'

// Create a new instance for each story. See https://mswjs.io/docs/faq/#apollo-client
const mockDataDependencies = (): Partial<ApellaRouteContext> => ({
  apolloClient: new ApolloClient({
    uri: 'https://your-graphql-endpoint',
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
      query: {
        fetchPolicy: 'no-cache',
        errorPolicy: 'all',
      },
    },
  }),
  cube: cubejs({
    apiUrl: `dummy/cubejs-api/v1`,
    headers: { Authorization: `accessToken` },
  }),
})

const meta: Meta<typeof ScheduleAssistantRoute> = {
  title: 'Pages/ScheduleAssistant',
  component: ScheduleAssistantRoute,
  decorators: [
    (Story, { parameters }) => (
      <TimezoneProvider>
        <MockLDProvider flags={parameters.ldFlags}>
          <CubeProvider cubeApi={parameters.reactRouter.context.cube}>
            <ApolloProvider
              client={parameters.reactRouter.context.apolloClient}
            >
              <Story />
            </ApolloProvider>
          </CubeProvider>
        </MockLDProvider>
      </TimezoneProvider>
    ),
    withRouter,
    (Story) => {
      clearRecentCasesCache()
      return <Story />
    },
  ],
}

export default meta
type Story = StoryObj<typeof ScheduleAssistantRoute>
const routing: RouteObject = {
  path: '/schedule-assistant',
  children: [
    {
      Component: SurgeonProcedureFiltersLayout,
      children: [
        {
          loader: estimateLayoutLoader,
          element: <EstimateLayout />,
          id: 'caseDurationEstimate',
          children: [
            {
              element: <RecentCases />,
              loader: recentCasesLoader,
              path: 'case-duration',
              handle: 'RecentCases',
            },
            {
              element: <AvailableTimesComp />,
              loader: availableTimesLoader,
              path: 'available-times',
              handle: 'AvailableTimes',
            },
          ],
        },
      ],
    },
    {
      element: <TimeSelectionComp />,
      loader: timeSelectionLoader,
      path: 'time-selection',
      handle: 'TimeSelection',
    },
  ],
}

const base_handlers = [
  http.get('https://events.launchdarkly.com/events/diagnostic/', () => {
    return HttpResponse.json({})
  }),
  http.get('https://app.launchdarkly.com/sdk/goals/', () => {
    return HttpResponse.json({})
  }),
]

const msw_time_selection = (
  wait: number | 'infinite' | 'real' = 'real',
  {
    recentCasesData = GetRecentCasesData,
    availableSlotsData = GetAvailableTimeSlotsData,
    dashboardScheduleEnabled = true,
  }: {
    recentCasesData?: typeof GetRecentCasesData
    availableSlotsData?: typeof GetAvailableTimeSlotsData
    dashboardScheduleEnabled?: boolean
  } = {
    recentCasesData: GetRecentCasesData,
    availableSlotsData: GetAvailableTimeSlotsData,
    dashboardScheduleEnabled: true,
  }
) => ({
  handlers: [
    ...base_handlers,
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(
        GetCurrentUserData(UserType.ALL_ACCESS, {
          dashboardScheduleEnabled: dashboardScheduleEnabled,
        })
      )
    }),
    http.get('dummy/cubejs-api/v1/load', async () => {
      await delay(wait)
      return HttpResponse.json(recentCasesData)
    }),
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetCaseDurationPredictionsDocument, () => {
      return HttpResponse.json({
        data: {
          caseDurationPredictions: {
            meta: {
              __typename: 'PredictionMetadata',
              surgeonName:
                GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0]
                  .node.surgeonName,
              procedureName:
                GetCaseDurationProceduresData.data.caseDurationProcedures
                  .edges[0].node.procedureName,
              additionalProcedures: [],
            },
            standard: 48,
            complex: 60,
            samples: [100, 150, 200, 250],
            __typename: 'CaseDurationPredictions',
          },
        },
      })
    }),
    graphql.query(GetAvailableTimeSlotsDocument, async () => {
      await delay(wait)
      return HttpResponse.json(availableSlotsData)
    }),
    graphql.query(GetRoomTagsQueryDocument, () => {
      return HttpResponse.json({
        data: {
          room: {
            id: GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
            tags: [aRoomTag({ name: 'Robot', color: '#006fff' })],
            __typename: 'Room',
          },
        },
      })
    }),
    graphql.query(GetBlockTimesDocument, () => {
      return HttpResponse.json(GetBlockTimesData)
    }),
    graphql.query(GetApellaCasesInSiteDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetApellaCasesInSiteData)
    }),
    graphql.mutation(EmailAvailableTimesHtmlDocument, async () => {
      return HttpResponse.json({
        data: {
          emailAvailableTimesHtml: {
            __typename: 'EmailAvailableTimesHtml',
            email: '<div>email place holder</div>',
            success: true,
            subject: 'OR1 Available Times',
          },
        },
      })
    }),
  ],
})
const msw = (
  wait: number | 'infinite' | 'real' = 'real',
  {
    recentCasesData = GetRecentCasesData,
    availableSlotsData = GetAvailableTimeSlotsData,
    primarySurgeonInformationData = GetPrimarySurgeonInformationNoBlocksData,
    caseDurationPredictionData = GetCaseDurationPredictionsData,
    dashboardScheduleEnabled = true,
  }: {
    recentCasesData?: typeof GetRecentCasesData
    availableSlotsData?: typeof GetAvailableTimeSlotsData
    primarySurgeonInformationData?: typeof GetPrimarySurgeonInformationNoBlocksData
    caseDurationPredictionData?: typeof GetCaseDurationPredictionsData
    dashboardScheduleEnabled?: boolean
  } = {
    recentCasesData: GetRecentCasesData,
    availableSlotsData: GetAvailableTimeSlotsData,
    primarySurgeonInformationData: GetPrimarySurgeonInformationNoBlocksData,
    caseDurationPredictionData: GetCaseDurationPredictionsData,
    dashboardScheduleEnabled: true,
  }
) => ({
  handlers: [
    ...base_handlers,
    graphql.query(GetCurrentUserDocument, () => {
      return HttpResponse.json(
        GetCurrentUserData(UserType.ALL_ACCESS, {
          dashboardScheduleEnabled: dashboardScheduleEnabled,
        })
      )
    }),
    http.get('dummy/cubejs-api/v1/load', async () => {
      return HttpResponse.json(recentCasesData)
    }),
    graphql.query(GetSiteOptionsFilterDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetSiteOptionsFilterData)
    }),
    graphql.query(GetCaseDurationTurnoverPredictionDocument, () => {
      return HttpResponse.json({
        data: {
          caseDurationTurnoverPrediction: {
            meta: {
              __typename: 'PredictionMetadata',
              surgeonName:
                GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0]
                  .node.surgeonName,
              procedureName:
                GetCaseDurationProceduresData.data.caseDurationProcedures
                  .edges[0].node.procedureName,
            },
            cleanAfterCase: 27,
            __typename: 'CaseDurationTurnoverPrediction',
          },
        },
      })
    }),
    graphql.query(GetCaseDurationSurgeonsDocument, () => {
      return HttpResponse.json(GetCaseDurationSurgeonsData)
    }),
    graphql.query(GetCaseDurationProceduresDocument, () => {
      return HttpResponse.json(GetCaseDurationProceduresData)
    }),
    graphql.query(GetBlockTimesDocument, () => {
      return HttpResponse.json(GetBlockTimesData)
    }),
    graphql.query(GetCaseDurationPredictionsDocument, async () => {
      await delay(wait)
      return HttpResponse.json(caseDurationPredictionData)
    }),
    graphql.query(GetPrimarySurgeonInformationDocument, () => {
      return HttpResponse.json(primarySurgeonInformationData)
    }),
    graphql.query(GetApellaCasesInSiteDocument, async () => {
      await delay(wait)
      return HttpResponse.json(GetApellaCasesInSiteData)
    }),
    graphql.query(GetAvailableTimeSlotsDocument, async () => {
      await delay(wait)
      return HttpResponse.json(availableSlotsData)
    }),
    graphql.query(GetRoomTagsQueryDocument, () => {
      return HttpResponse.json({
        data: {
          room: {
            id: GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
            tags: [
              {
                name: 'Robot',
                color: '#006fff',
                __typename: 'RoomTag',
                id: 'tag-1',
              },
            ],
            __typename: 'Room',
          },
        },
      })
    }),
  ],
})

export const CaseDurationEmptyState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationLoadingState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw('infinite'),

    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationNoResults: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, { recentCasesData: GetRecentCasesDataNoResults }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDuration: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationComplex: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          complexity: Complexity.Complex,
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationMultiProcedureEmpty: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      caseDurationPredictionData: GetCaseDurationPredictionsMultiProcedureData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationMultiProcedure: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      caseDurationPredictionData: GetCaseDurationPredictionsMultiProcedureData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
          'additionalProcedures[0]':
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const CaseDurationHighlyVariable: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      caseDurationPredictionData:
        GetCaseDurationPredictionsMultiProcedureDataHighVariance,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
          'additionalProcedures[0]':
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesEmptyState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesNoResults: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      availableSlotsData: GetAvailableTimeSlotsDataNoResults,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesSurgeonBlockWithNoResults: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      availableSlotsData: GetAvailableTimeSlotsDataNoResults,
      primarySurgeonInformationData: GetPrimarySurgeonInformationThanosData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesLoadingState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw('infinite'),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimes: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          'sites[0]': 'site-01',
          'sites[1]': 'site-02',
          'blocks[0]': 'unblocked',
          'blocks[1]': 'block-01',
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesClinicScheduler: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, { dashboardScheduleEnabled: false }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          'sites[0]': 'site-01',
          'sites[1]': 'site-02',
          'blocks[0]': 'unblocked',
          'blocks[1]': 'block-01',
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesCustomDurationWithSelectedBlock: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      primarySurgeonInformationData: GetPrimarySurgeonInformationThanosData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          durationSource: 'custom',
          customProcedureDuration: '62',
          customTurnoverDuration: '23',
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesMultiProcedure: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      caseDurationPredictionData: GetCaseDurationPredictionsMultiProcedureData,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
          'additionalProcedures[0]':
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const AvailableTimesHighlyVariable: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0, {
      caseDurationPredictionData:
        GetCaseDurationPredictionsMultiProcedureDataHighVariance,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/available-times',
        searchParams: {
          surgeon:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonId,
          surgeonName:
            GetCaseDurationSurgeonsData.data.caseDurationSurgeons.edges[0].node
              .surgeonName,
          procedure:
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[0]
              .node.procedureName,
          'additionalProcedures[0]':
            GetCaseDurationProceduresData.data.caseDurationProcedures.edges[1]
              .node.procedureName,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionFlagEnabled: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await waitFor(() => {
      expect(canvas.getAllByRole('checkbox')).toHaveLength(6)
    })
    const firstSlotButton = canvas.getAllByRole('checkbox')[0]
    userEvent.click(firstSlotButton)
  },
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
        searchParams: {
          'sites[0]': GetSiteOptionsFilterData.data.sites.edges[0].node.id,
          'rooms[0]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
              .node.id,
          'rooms[1]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionEditingTime: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await waitFor(() => {
      expect(canvas.getAllByRole('checkbox')).toHaveLength(6)
    })
    const firstSlotButton = canvas.getAllByRole('checkbox')[0]
    await userEvent.hover(firstSlotButton)
    const editButton = canvas.getByRole('button', { name: /Edit/i })
    await userEvent.click(editButton)
  },
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
        searchParams: {
          'sites[0]': GetSiteOptionsFilterData.data.sites.edges[0].node.id,
          'rooms[0]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
              .node.id,
          'rooms[1]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionV1FlagEnabled: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    await waitFor(() => {
      expect(canvas.getAllByRole('checkbox')).toHaveLength(6)
    })
    const firstSlotButton = canvas.getAllByRole('checkbox')[0]
    userEvent.click(firstSlotButton)
    await waitFor(() => {
      expect(
        canvas.getByRole('button', { name: /Confirm/i })
      ).toBeInTheDocument()
    })
    userEvent.click(canvas.getByRole('button', { name: /Confirm/i }))
  },
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
      enableCarolsEmailV1: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
        searchParams: {
          'sites[0]': GetSiteOptionsFilterData.data.sites.edges[0].node.id,
          'rooms[0]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
              .node.id,
          'rooms[1]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionFlagDisabled: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: false,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/case-duration',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionLoadingState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection('infinite'),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
        searchParams: {
          'sites[0]': GetSiteOptionsFilterData.data.sites.edges[0].node.id,
          'rooms[0]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
              .node.id,
          'rooms[1]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionNoSiteSelected: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection(0),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}

export const TimeSelectionEmptyState: Story = {
  parameters: {
    ldFlags: {
      enableTimeFinder: true,
      enableCarolsEmail: true,
    },
    date: new Date(2024, 8, 12, 12),
    msw: msw_time_selection(0, {
      availableSlotsData: GetAvailableTimeSlotsDataNoResults,
    }),
    decorators: [withRouter],
    reactRouter: {
      location: {
        path: '/schedule-assistant/time-selection',
        searchParams: {
          'sites[0]': GetSiteOptionsFilterData.data.sites.edges[0].node.id,
          'rooms[0]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[0]
              .node.id,
          'rooms[1]':
            GetSiteOptionsFilterData.data.sites.edges[0].node.rooms.edges[1]
              .node.id,
        },
      },
      routing,
      context: mockDataDependencies(),
    },
  },
}
