import { BlockNode } from './interfaces'

export const PAGE_FILTER_LOCAL_STORAGE_KEY = 'caseDurationFilters-1'
export const PAGE_FILTER_LOCAL_STORAGE_KEY_TIME_SELECTION =
  'timeSelectionFilters'
export const RECENTLY_SELECTED_SURGEONS_KEY = 'recentlySelectedSurgeons'
export const THE_UNBLOCKED_BLOCK_ID = 'unblocked'
export const UNBLOCKED_BLOCK: BlockNode = {
  node: {
    __typename: 'Block',
    id: THE_UNBLOCKED_BLOCK_ID,
    name: 'Unblocked',
    color: '#F2F2F2',
    surgeonIds: [],
  },
}

export const BLOCK_FILTER_LABEL = 'Select a block'

export const COEFFICIENT_OF_VARIABILITY_RATIO_THRESHOLD = 1
