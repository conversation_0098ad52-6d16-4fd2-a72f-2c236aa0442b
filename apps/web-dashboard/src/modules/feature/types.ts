export interface WebDashboardFeatureFlagSet {
  acotsEnabled: boolean
  assignStaffPlanningEnabled: boolean
  blockManagementEnabled: boolean
  blockTimeOnSchedulePageEnabled: boolean
  blurVideoPlayer: boolean
  customFcotsDisplay: boolean
  customPhaseModalEnabled: boolean
  delayDataEnabled: boolean
  ehrEnabled: boolean
  enableBlockUtilizationDashboard: boolean
  enableBlockUtilizationManagementPage: boolean
  enableCarolsEmail: boolean
  enableCarolsEmailV1: boolean
  enableTimeFinder: boolean
  // This flag is in the type only to enable tests
  mockTestingFlag: boolean
  notificationEventTypes: string[]
  obxNotificationTypes: string[]
  refreshTokenEnabled: boolean
  scheduleShowAnesthesiaOnTimeline: boolean
  showEditScheduleRoute: boolean
  showPatientData: boolean
  statusColors: JSON
  terminalCleansV0: boolean
  textNotificationEnabled: boolean
  toggleRoomPrivacy: boolean
  turnoverStatusesEnabled: boolean
  useNewDirectMediaPlaylistEndpoint: boolean
  videoPlayerSpeeds: number[]
}
