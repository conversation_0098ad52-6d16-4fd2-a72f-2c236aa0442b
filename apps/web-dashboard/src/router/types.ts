import { DataStrategyMatch, LoaderFunctionArgs } from 'react-router'

import { Auth0Client, User as Auth0User } from '@auth0/auth0-spa-js'
import { CubeApi } from '@cubejs-client/core'

import { ApellaDataDependencies } from 'src/api/clients/apellaDataDependencies'
import { DISPLAY_MODE } from 'src/Contexts'
import { ApellaUser, Permissions } from 'src/modules/user/types'

export enum LocationPath {
  Root = '/',
  Home = '/home',
  SignOut = '/logout',
  Turnovers = '/insights/turnovers',
  FirstCaseStarts = '/insights/firstcasestarts',
  CaseStarts = '/insights/casestarts',
  CaseLengths = '/insights/caselengths',
  CaseVolume = '/insights/casevolume',
  Live = '/live',
  Highlights = '/highlights',
  HighlightView = ':highlightId',
  VideoView = 'video',
  Schedule = '/schedule',
  EditSchedule = '/schedule/edit',
  PreOp = '/schedule/pre-op',
  PostOp = '/schedule/post-op',
  TurnoversDashboard = '/schedule/turnovers',
  StaffManagement = '/staff-management',
  DailyInsights = 'daily-insights',
  CasesDataView = 'cases',
  BlocksDataView = 'blocks',
  TerminalCleans = '/terminal-cleans',
  TerminalCleansView = ':measurementPeriodId/:rowId',
  Blocks = '/blocks',
  BlockUtilization = '/block-utilization',
  BlockUtilizationForBlock = ':blockId',
  BlockUtilizationVerifyReport = 'verify-report',
  BlockUtilizationManagement = '/block-utilization-management',
  BlockUtilizationCaseEditView = ':caseId/edit',
  BlockEditor = ':blockId/edit',
  BlockNew = 'new',
  BlockUpload = 'upload',
  BlockUploadReleases = 'upload-releases',
  Boards = '/boards',
  BoardView = '/boards/:boardId',
  BoardEdit = '/boards/:siteId/:boardId/edit',
  Notifications = '/notifications',
  AddSubscriber = '/add-subscriber',
  EditSubscriber = '/edit-subscriber/:id',
  ScheduleAssistant = '/schedule-assistant',
  CaseDuration = 'case-duration',
  AvailableTimes = 'available-times',
  TimeSelection = 'time-selection',
  Other = '*',
  StaffPlanning = 'staff-planning',
}

export enum MenuItemTitle {
  Highlights = 'Highlights',
  Insights = 'Insights',
  Live = 'Live',
  Schedule = 'Schedule',
  TerminalCleans = 'Terminal Cleans',
  Boards = 'Boards',
  ScheduleAssistant = 'Schedule Assistant',
  BlockUtilization = 'Block Utilization',
}

export type OrgSiteLocationParams = 'organizationId' | 'siteId'

export enum TrackingPathTitle {
  Highlights = 'Highlights',
  Insights = 'Insights',
  Live = 'Live',
  Schedule = 'Schedule',
  Video = 'Video',
  DailyInsights = 'DailyInsights',
  CasesData = 'Cases',
  BlocksData = 'Blocks',
  BlockUtilizationManagement = 'BlockUtilizationManagement',
  BlockUtilizationData = 'BlockUtilization',
  StaffManagement = 'StaffManagement',
  TerminalCleans = 'TerminalCleans',
  BlockManagement = 'BlockManagement',
  Boards = 'Boards',
  ScheduleAssistant = 'ScheduleAssistant',
  CaseDuration = 'CaseDuration',
  AvailableTimes = 'AvailableTimes',
  TimeSelection = 'TimeSelection',
  Unknown = 'unknown',
  StaffPlanning = 'StaffPlanning',
}

export type Permission = Exclude<keyof Permissions, '__typename'>

// ensureAuthenticated middleware makes sure these are set before the route is rendered
export interface ApellaRouteContext extends ApellaDataDependencies {
  /**
   * The current user's auth0 client
   */
  auth0Client: Auth0Client
  /**
   * The raw auth0 user object
   */
  auth0User?: Auth0User
  /**
   * The cubejs client for querying the data warehouse
   */
  cube: CubeApi
  /**
   * The current user object
   */
  user: ApellaUser
}

export type ApellaRouteHandle = Partial<{
  /**
   * The title of the page
   */
  title: string
  /**
   * Middleware functions to run before rendering the route
   */
  middleware: MiddlewareFunction[]
  /**
   * The permission required to access the route
   */
  authorization: Permission
  /**
   * Redirect path should permission not be met
   */
  redirect: LocationPath
  /**
   * Used for logging the current path to amplitude
   */
  trackingPathName: TrackingPathTitle
  /**
   * Sets the default display mode for the page
   */
  displayMode: DISPLAY_MODE
  /**
   * Hide the app refresh button
   */
  hideAppRefresh: boolean
  /**
   * Hide the global loading bar. This is useful for routes that do async data loading and have their own loading indicator
   */
  hideGlobalLoadingBar: boolean
}>

export const isApellaRouteHandle = (
  handle: unknown
): handle is ApellaRouteHandle => {
  return (
    !!handle &&
    typeof handle === 'object' &&
    (('title' in handle && typeof handle.title === 'string') ||
      ('middleware' in handle && Array.isArray(handle.middleware)) ||
      ('authorization' in handle && typeof handle.authorization === 'string') ||
      ('redirect' in handle && typeof handle.redirect === 'string') ||
      ('trackingPathName' in handle &&
        typeof handle.trackingPathName === 'string') ||
      ('displayMode' in handle && typeof handle.displayMode === 'string') ||
      ('hideAppRefresh' in handle &&
        typeof handle.hideAppRefresh === 'boolean') ||
      ('hideGlobalLoadingBar' in handle &&
        typeof handle.hideGlobalLoadingBar === 'boolean'))
  )
}

export const isApellaRouteContext = (
  context: unknown
): context is ApellaRouteContext => {
  return (
    !!context &&
    typeof context === 'object' &&
    ('auth0Client' in context ||
      'auth0User' in context ||
      'cube' in context ||
      'flags' in context ||
      'user' in context ||
      'apolloClient' in context ||
      'ldClient' in context ||
      'preloadQuery' in context)
  )
}

export const getRouteContext = (context: unknown): ApellaRouteContext => {
  if (!isApellaRouteContext(context)) {
    throw new Error('Invalid route context')
  }

  return context
}

/**
 * Route middleware function arguments
 */
export type MiddlewareFunctionArgs = LoaderFunctionArgs<
  Partial<ApellaRouteContext>
> & {
  matches: DataStrategyMatch[]
}

/**
 * Route middleware function signature
 */
export type MiddlewareFunction = (
  args: MiddlewareFunctionArgs,
  context: unknown
) => Promise<void>
