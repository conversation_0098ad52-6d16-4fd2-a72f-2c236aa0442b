import { getRouteContext, MiddlewareFunction } from '../types'

const waitForRedirectToCompleteWithTimeout = () =>
  new Promise((_res, reject) =>
    setTimeout(
      () =>
        reject(
          new Error('Could not authenticate: Redirect to Auth0 timed out.')
        ),
      30000
    )
  )

export const ensureAuthenticated: MiddlewareFunction = async (
  _arg,
  context
) => {
  const { auth0Client } = getRouteContext(context)
  // If the user is not authenticated by the time this middleware runs, they are in the process of being redirected to Auth0.
  // We should wait for that redirect to complete before continuing so that loaders don't run.
  if (!(await auth0Client?.isAuthenticated())) {
    await waitForRedirectToCompleteWithTimeout()
  }
}
