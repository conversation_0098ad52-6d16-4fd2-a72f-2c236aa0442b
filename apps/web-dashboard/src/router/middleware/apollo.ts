import { from } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'

import {
  createErrorLink,
  httpLink,
  sentrySpanLink,
} from 'src/api/clients/apolloClient'
import { getAccessTokenSilentlyOrLogout } from 'src/api/clients/auth0'
import { GetCurrentUser } from 'src/modules/user/__generated__'
import { getPermissions } from 'src/modules/user/hooks'
import { GET_CURRENT_USER } from 'src/modules/user/queries'
import { ApellaUser } from 'src/modules/user/types'
import { isApellaEmployee } from 'src/utils/userTypes'

import { ApellaRouteContext, MiddlewareFunctionArgs } from '../types'

export const apolloClientMiddleware = async (
  _args: MiddlewareFunctionArgs,
  context: ApellaRouteContext
) => {
  const { auth0Client, apolloClient, auth0User } = context
  if (typeof auth0Client === 'undefined' || !auth0User?.sub) {
    return
  }
  const authLink = setContext(async (_, { headers }) => {
    const token = await getAccessTokenSilentlyOrLogout(auth0Client)
    return {
      headers: {
        ...headers,
        Authorization: token ? `Bearer ${token}` : '',
      },
    }
  })
  const userId = auth0User.sub
  const links = [sentrySpanLink, createErrorLink(userId), authLink, httpLink]
  apolloClient.setLink(from(links))

  const { data, error } = await apolloClient.query<GetCurrentUser>({
    query: GET_CURRENT_USER,
  })

  if (!data.me) {
    throw new Error('User not found')
  }
  if (error) {
    throw error
  }

  const currentOrganization = data.me.organizations.edges.find(
    ({ node }) => node.auth0OrgId === auth0User.org_id
  )

  if (!currentOrganization) {
    throw new Error('Organization not found')
  }

  const user: ApellaUser = {
    currentOrganization,
    userOrganizations: data.me.organizations.edges,
    permissions: getPermissions(data.me.uiPermissions, data?.me?.permissions),
    userId: data.me.id,
    email: data.me.email,
    isApellaEmployee: isApellaEmployee(data.me.email),
  }

  context.user = user
}
