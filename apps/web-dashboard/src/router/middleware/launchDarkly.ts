import { CLAIMS } from 'src/utils/auth0User'

import { ApellaRouteContext, MiddlewareFunctionArgs } from '../types'

let USER_HAS_BEEN_IDENTIFIED = false

export const launchDarklyMiddleware = async (
  _args: MiddlewareFunctionArgs,
  context: ApellaRouteContext
) => {
  await context.ldClient.waitForInitialization()

  // Setting up LaunchDarkly
  if (context.auth0User && !USER_HAS_BEEN_IDENTIFIED) {
    await context.ldClient?.identify({
      key: context.auth0User.sub,
      email: context.auth0User.email?.toLowerCase(),
      name: context.auth0User.name,
      custom: {
        auth0OrganizationId: context.auth0User.org_id,
        apellaOrganizationId: context.auth0User[CLAIMS.ORG_ID],
        roles: context.auth0User[CLAIMS.ROLES],
      },
    })
    USER_HAS_BEEN_IDENTIFIED = true
  }
}
