import { replace, generatePath } from 'react-router'

import {
  ApellaRouteContext,
  isApellaRouteHandle,
  LocationPath,
  MiddlewareFunctionArgs,
} from '../types'

export const permissionCheckRedirect = (
  { request, params, matches }: MiddlewareFunctionArgs,
  { user }: ApellaRouteContext
) => {
  if (!user) {
    return
  }
  const url = new URL(request.url)
  const { permissions } = user

  for (const match of matches) {
    if (
      isApellaRouteHandle(match.route.handle) &&
      match.route.handle.authorization
    ) {
      const authorization = match.route.handle.authorization
      const redirect = match.route.handle.redirect
      if (redirect) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore params here doesn't play nicely with TS, but it's what we want
        throw replace(`${generatePath(redirect, params)}${url.search}`)
      }
      if (authorization && permissions && !permissions[authorization]) {
        throw replace(LocationPath.Root)
      }
    }
  }

  return
}
