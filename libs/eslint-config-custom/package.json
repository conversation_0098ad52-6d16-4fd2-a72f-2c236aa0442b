{"name": "@apella/eslint-config-custom", "type": "module", "license": "UNLICENSED", "version": "0.0.0", "private": true, "exports": {".": "./eslint.config.js", "./prettier-config": "./prettier.config.js"}, "scripts": {"lint": "eslint --max-warnings=0 ./ && prettier --check ./", "format": "eslint --cache --max-warnings=0 --fix ./ && prettier --cache --write ./"}, "dependencies": {"@emotion/eslint-plugin": "^11.12.0", "@eslint/compat": "^1.2.9", "@eslint/eslintrc": "^3.3.1", "eslint": "^9.27.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.12.0", "typescript-eslint": "^8.33.0"}, "peerDependencies": {"@eslint/eslintrc": "^3.1.0", "prettier": "^3.5.3"}, "devDependencies": {"prettier": "^3.5.3"}}