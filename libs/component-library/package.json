{"name": "@apella/component-library", "version": "1.0.0", "description": "The Apella Component Library", "main": "dist/index.js", "type": "module", "files": ["dist"], "browserslist": ["last 2 chrome versions", "last 2 safari versions", "last 2 firefox versions", "last 2 edge versions"], "scripts": {"build": "echo 'Skipping component-library, `build-storybook` and publish to chromatic'", "circular-dependency:check": "madge --extensions ts,tsx --circular src/", "clean": "rm -rf node_modules dist", "lint": "eslint --max-warnings=0 ./src && prettier --check ./src", "format": "eslint --cache --max-warnings=0 --fix ./src && prettier --cache --write ./src", "storybook": "storybook dev -p 9000 -c .storybook", "chromatic": "chromatic", "dev": "yarn storybook", "build-storybook": "storybook build", "icons:gen": "node scripts/convertSvgToSvgIcon.js", "test": "vitest run", "test:watch": "vitest watch", "tsc": "tsc -p ."}, "nx": {"targets": {"snapshot": {"dependsOn": ["build-storybook"]}}}, "author": "<PERSON>", "license": "UNLICENSED", "dependencies": {"@floating-ui/react": "^0.27.9", "@types/react-calendar": "^4.1.0", "keycode-js": "^3.1.0", "rc-slider": "^11.1.8", "react-calendar": "^5.1.0", "react-csv": "^2.2.2", "react-player": "^2.16.0", "screenfull": "^6.0.2"}, "peerDependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "emotion-reset": ">=3.0.0", "formik": ">=2.4.6", "luxon": ">=2.0.0", "polished": ">=4.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": ">=7.0.0", "recharts": "^2.15.3", "typescript": ">=5.7.3", "yup": ">=0.32.9"}, "devDependencies": {"@apella/eslint-config-custom": "*", "@chromatic-com/storybook": "^3.2.6", "@emotion/babel-plugin": "^11.13.5", "@emotion/jest": "^11.13.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-storysource": "^8.6.14", "@storybook/builder-vite": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/theming": "^8.6.14", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/luxon": "^2.0.4", "@types/react": "^19.0.0", "@types/react-csv": "^1.1.10", "@types/react-dom": "^19.0.0", "@vitejs/plugin-react": "^4.4.1", "core-js": "^3.42.0", "emotion-reset": "^3.0.1", "eslint": "^9.27.0", "formik": "^2.4.6", "hls.js": "^1.6.2", "jsdom": "^26.1.0", "luxon": "^3.5.0", "madge": "^8.0.0", "mockdate": "^3.0.5", "polished": "^4.1.3", "prettier": "^3.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.6.1", "recharts": "^2.15.3", "storybook": "^8.6.14", "tslib": "^2.3.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4", "yup": "^1.4.0"}, "overrides": {"react-is": "^19.0.0"}}