import { ReactNode, useRef, useState } from 'react'

import { useTheme } from '@emotion/react'

import { Header, HeaderProps } from '../../../components/organisms'
import { ZIndex, mediaQueries } from '../../../utils'
import { Environment } from '../../layout/EnvironmentHeader'
import { Menu, MenuProps } from '../../molecules/Menu'
import { OrganizationProps } from '../../molecules/UserDropdown'

export interface HeaderNavPageTemplateProps
  extends Pick<
      MenuProps,
      'menuItems' | 'name' | 'image' | 'settings' | 'footerMenuItem'
    >,
    Omit<HeaderProps, 'toggleMenu'> {
  children: ReactNode
  defaultOpen?: boolean
  environment?: Environment
  isTvModeEnabled?: boolean
  /** For multi-tenant web applications, the organizations to display and potentially change */
  organizationProps?: OrganizationProps
}

export const HeaderNavPageTemplate = ({
  children,
  menuItems,
  footerMenuItem,
  organizationProps,
  settings,
  name,
  image,
  defaultOpen = true,
  environment = 'production',
  isTvModeEnabled = false,
}: HeaderNavPageTemplateProps): React.JSX.Element => {
  const [menuOpen, setMenuOpen] = useState(defaultOpen)
  const pageMainRef = useRef<HTMLDivElement>(null)
  const theme = useTheme()

  if (isTvModeEnabled) {
    return (
      <main
        ref={pageMainRef}
        css={{
          height: '100%',
          width: '100%',
          position: 'absolute',
          overflow: 'auto',
          background: theme.palette.background.primary,
        }}
      >
        {children}
      </main>
    )
  }

  return (
    <div
      style={{
        height: '100vh',
        width: '100vw',
        overflow: 'hidden',
        display: 'flex',
        background: theme.palette.background.primary,
        position: 'relative',
        zIndex: ZIndex.DEFAULT,
      }}
    >
      <Menu
        menuItems={menuItems}
        footerMenuItem={footerMenuItem}
        open={menuOpen}
        organizationProps={organizationProps}
        name={name}
        image={image}
        setOpen={setMenuOpen}
        settings={settings}
      />
      <div
        style={{
          flexGrow: 1,
          overflow: 'auto',
        }}
      >
        {menuOpen && (
          <div
            css={{
              backgroundColor: theme.palette.background.alternate,
              opacity: 0.2,
              position: 'absolute',
              height: '100vh',
              width: '100vw',
              cursor: 'pointer',
              zIndex: ZIndex.ABOVE,
              [mediaQueries.lg]: { display: 'none' },
            }}
          />
        )}
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            height: '100%',
            width: '100%',
            zIndex: ZIndex.DEFAULT,
          }}
        >
          <Header toggleMenu={setMenuOpen} environment={environment} />
          <main
            style={{
              flexGrow: 1,
              overflow: 'auto',
            }}
            ref={pageMainRef}
          >
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
