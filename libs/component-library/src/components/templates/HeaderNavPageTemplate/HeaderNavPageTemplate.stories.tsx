import { useState } from 'react'

import { Meta, StoryFn } from '@storybook/react'

import {
  ExitToApp,
  Folder,
  Highlights,
  InsertChart,
  LifeRing,
  ToggleTheme,
  Videocam,
} from '../../../icons'
import { Badge, Button, Counter } from '../../atoms'
import { MenuItemProps } from '../../atoms/MenuItem'
import { SettingProps } from '../../molecules/UserDropdown'
import { VideoPlayer } from '../../organisms/VideoPlayer'
import {
  HeaderNavPageTemplate,
  HeaderNavPageTemplateProps,
} from './HeaderNavPageTemplate'

export default {
  title: 'Templates/HeaderNavPageTemplate',
  component: HeaderNavPageTemplate,
} as Meta

const HeaderNavPageTemplateItems: MenuItemProps[] = [
  {
    active: true,
    Icon: InsertChart,
    title: 'Insights',
    to: '/insights',
  },
  {
    Icon: Highlights,
    title: 'Highlights',
    to: '/highlights',
    componentRight: <Counter size="sm" color="gray" count={4} />,
  },
  {
    Icon: Folder,
    title: 'Cases',
    to: '/cases',
  },
  {
    disabled: true,
    Icon: Videocam,
    title: 'Disabled Live View',
    to: '/live',
  },
]

const footerMenuItem: MenuItemProps = {
  Icon: LifeRing,
  title: 'Feedback',
  onClick: () => alert('Feedback'),
}

const settings: SettingProps[] = [
  {
    Icon: ToggleTheme,
    title: 'Toggle Theme',
    to: '/somewhere',
    componentRight: <Badge>Internal Only</Badge>,
    settingLevel: 'user',
  },
  {
    Icon: ExitToApp,
    title: 'Sign Out',
    to: '/somewhere',
    settingLevel: 'user',
  },
]

export const Controls: StoryFn<HeaderNavPageTemplateProps> = (
  args
): React.JSX.Element => (
  <HeaderNavPageTemplate {...args}>Main Content!</HeaderNavPageTemplate>
)

Controls.parameters = {
  controls: {
    exclude: ['avatarImage', 'defaultOpen'],
  },
}

const defaultArgs: Partial<HeaderNavPageTemplateProps> = {
  settings: settings,
  menuItems: HeaderNavPageTemplateItems,
  footerMenuItem: footerMenuItem,
  name: 'Dorian Bullerwell',
  isTvModeEnabled: false,
  environment: 'development',
  organizationProps: {
    organizations: [
      { name: 'Ft Worth General', id: '1' },
      { name: 'SouthSound Clinical', id: '2' },
    ],
    activeOrganizationId: '1',
  },
}

Controls.args = defaultArgs

export const TvModeWithVideo: StoryFn<HeaderNavPageTemplateProps> = (
  args
): React.JSX.Element => {
  const [isTvModeEnabled, setIsTvModeEnabled] = useState(false)
  return (
    <HeaderNavPageTemplate {...args} isTvModeEnabled={isTvModeEnabled}>
      <VideoPlayer
        apiDomain=""
        timezone={''}
        videos={[]}
        useNewDirectMediaPlaylistEndpoint={false}
      />
      <Button onClick={() => setIsTvModeEnabled(!isTvModeEnabled)}>
        Toggle tv mode
      </Button>
    </HeaderNavPageTemplate>
  )
}

TvModeWithVideo.args = defaultArgs

TvModeWithVideo.parameters = {
  controls: {
    exclude: ['isTvModeEnabled', 'avatarImage', 'defaultOpen'],
  },
}
