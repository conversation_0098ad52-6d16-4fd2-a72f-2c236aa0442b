import { ChangeEvent, FocusEvent, KeyboardEvent, ReactNode } from 'react'

export const Sizes = ['xxxs', 'xxs', 'xs', 'sm', 'md', 'lg', 'xl'] as const
export type Size = (typeof Sizes)[number]

export interface ChildrenProps {
  children?: ReactNode
}

export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC',
}

export interface InputProps {
  className?: string
  disabled?: boolean
  error?: string
  hint?: string
  id?: string
  label?: string
  name: string
  onBlur?: (e: FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onChange?: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onFocus?: (e: FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  onKeyDown?: (e: KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  placeholder?: string
  readOnly?: boolean
  required?: boolean
  size?: Size
  success?: string | boolean
  value?: string
}
