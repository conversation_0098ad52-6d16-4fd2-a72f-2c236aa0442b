import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ReactN<PERSON>,
} from 'react'
import { NavLink, NavLinkProps, NavLinkRenderProps } from 'react-router'

import { css, useTheme, Theme } from '@emotion/react'

import { SvgIcon } from '../../../icons/SvgIcon'
import { shape } from '../../../utils'
import { findChildByType } from '../../../utils/reactUtils'
import { ChildrenProps } from '../../types'
import { PH6 } from '../Typography'

export interface MenuAtomProps {
  active?: boolean
  className?: string
  disabled?: boolean
  onClick?: MouseEventHandler
  replace?: boolean
  title?: string
  to?: NavLinkProps['to']
}

export interface MenuItemProps extends MenuAtomProps {
  backgroundColor?: string
  componentRight?: ReactNode
  Icon: React.FC<React.ComponentProps<typeof SvgIcon>>
  iconColor?: string
}

type GetRenderMenuItemParams = {
  active?: boolean
  theme: Theme
  disabled?: boolean
  icon?: React.DetailedReactHTMLElement<any, HTMLElement>
  title?: React.DetailedReactHTMLElement<any, HTMLElement>
  componentRight?: React.DetailedReactHTMLElement<any, HTMLElement>
}

const getRenderMenuItem = ({
  active,
  theme,
  disabled,
  icon,
  title,
  componentRight,
}: GetRenderMenuItemParams) => {
  return function render({ isActive: navActive }: NavLinkRenderProps) {
    const isActive = active || navActive

    const iconColor =
      isActive === true
        ? theme.palette.blue[50]
        : disabled === true
          ? theme.palette.gray[30]
          : theme.palette.gray[50]

    const textColor =
      isActive === true
        ? theme.palette.blue[50]
        : disabled === true
          ? theme.palette.text.tertiary
          : theme.palette.text.secondary

    return (
      <div
        css={[
          {
            backgroundColor: isActive
              ? theme.palette.gray.background
              : undefined,
            display: 'flex',
            alignItems: 'center',
            gap: 10,
            padding: 10,
            color: iconColor,
            borderRadius: shape.borderRadius.xsmall,
            flexGrow: 1,
            overflow: 'hidden',
          },
          css`
            &:hover svg {
              color: ${isActive !== true && disabled !== true
                ? theme.palette.gray[60]
                : undefined};
            }
            &:hover ${PH6} {
              color: ${isActive !== true && disabled !== true
                ? theme.palette.text.primary
                : undefined};
            }
          `,
        ]}
      >
        {icon}
        {!!title && (
          <PH6
            color={textColor}
            css={{
              display: 'inline-block',
              overflow: 'hidden',
              textWrap: 'nowrap',
              textOverflow: 'ellipsis',
            }}
          >
            {title}
          </PH6>
        )}
        {componentRight !== undefined && (
          <>
            <div css={{ flexGrow: 1 }} />
            <div css={{ color: theme.palette.text.tertiary }}>
              {componentRight}
            </div>
          </>
        )}
      </div>
    )
  }
}

const MenuItem = ({
  active,
  children,
  disabled,
  to,
  onClick,
  className,
  replace,
}: PropsWithChildren<MenuAtomProps>): React.JSX.Element => {
  const theme = useTheme()
  const title = findChildByType(children, Title.displayName)
  const icon = findChildByType(children, Icon.displayName)
  const componentRight = findChildByType(children, ComponentRight.displayName)

  const render = getRenderMenuItem({
    active,
    theme,
    disabled,
    icon,
    title,
    componentRight,
  })

  if (to) {
    return (
      <NavLink
        css={{
          textDecoration: 'none',
        }}
        to={to}
        className={className}
        replace={replace}
        onClick={onClick}
      >
        {render}
      </NavLink>
    )
  } else {
    if (disabled) {
      return render({
        isActive: !!active,
        isPending: false,
        isTransitioning: false,
      })
    }

    return (
      <div
        role="button"
        onClick={onClick}
        css={{ cursor: 'pointer' }}
        className={className}
      >
        {render({
          isActive: !!active,
          isPending: false,
          isTransitioning: false,
        })}
      </div>
    )
  }
}

const Title = ({ children }: ChildrenProps) => <Fragment>{children}</Fragment>
Title.displayName = 'Title'
MenuItem.Title = Title

const Icon = ({ children }: ChildrenProps) => <Fragment>{children}</Fragment>
Icon.displayName = 'Icon'
MenuItem.Icon = Icon

const ComponentRight = ({ children }: ChildrenProps) => (
  <Fragment>{children}</Fragment>
)
ComponentRight.displayName = 'ComponentRight'
MenuItem.ComponentRight = ComponentRight

export { MenuItem }
