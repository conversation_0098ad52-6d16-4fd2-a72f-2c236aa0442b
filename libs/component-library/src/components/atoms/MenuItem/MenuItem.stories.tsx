import type { Meta, StoryFn } from '@storybook/react'

import { Highlights, InsertChart } from '../../../icons'
import { Counter } from '../Counter'
import { MenuItem } from './MenuItem'

export default {
  title: 'Atoms/MenuItem',
  component: MenuItem,
} as Meta

export const Showcase = (): React.JSX.Element => {
  return (
    <>
      <div style={{ width: '296px' }}>
        <MenuItem to={'someUrl'} title="Insights Inactive">
          <MenuItem.Icon>
            <InsertChart />
          </MenuItem.Icon>
          <MenuItem.Title>Insights Inactive</MenuItem.Title>
        </MenuItem>
        <MenuItem to={'someUrl'} title="Insights Active" active>
          <MenuItem.Icon>
            <InsertChart />
          </MenuItem.Icon>
          <MenuItem.Title>Insights Active</MenuItem.Title>
        </MenuItem>
        <MenuItem to={'someUrl'} title="Insights Disabled" disabled>
          <MenuItem.Icon>
            <InsertChart />
          </MenuItem.Icon>
          <MenuItem.Title>Insights Disabled</MenuItem.Title>
        </MenuItem>
        <MenuItem to={'someUrl'} title="Highlights">
          <MenuItem.Icon>
            <Highlights />
          </MenuItem.Icon>
          <MenuItem.Title>Highlights</MenuItem.Title>
          <MenuItem.ComponentRight>
            <Counter size="sm" color="gray" count={1} />
          </MenuItem.ComponentRight>
        </MenuItem>
        <MenuItem to={'someUrl'} title="Insights Icon">
          <MenuItem.Icon>
            <InsertChart />
          </MenuItem.Icon>
        </MenuItem>
        <MenuItem
          to={'someUrl'}
          title="Really Really Really Really Long Menu Item"
        >
          <MenuItem.Icon>
            <InsertChart />
          </MenuItem.Icon>
          <MenuItem.Title>
            Really Really Really Really Long Menu Item
          </MenuItem.Title>
        </MenuItem>
      </div>
    </>
  )
}

export const Controls: StoryFn<React.ComponentProps<typeof MenuItem>> = (
  args
): React.JSX.Element => (
  <div style={{ width: '296px ' }}>
    <MenuItem to={'someUrl'} {...args}>
      <MenuItem.Icon>
        <InsertChart />
      </MenuItem.Icon>
      <MenuItem.Title>Insights</MenuItem.Title>
    </MenuItem>
  </div>
)
Controls.args = {
  title: 'Insights',
  active: false,
  disabled: false,
}
