import { ReactNode } from 'react'

import { useTheme } from '@emotion/react'

import { ChevronLeft, ChevronRight } from '../../../icons'
import { remSpacing } from '../../../utils'
import { Appearance, BaseButtonProps, Button } from '../Button'
import { Span2 } from '../Typography'

interface PaginationProps {
  appearance?: Appearance
  disabled?: boolean
  onClick?: () => void
  size?: BaseButtonProps['size']
  variant?: 'arrow' | 'text'
}

export interface PaginationItemProps extends PaginationProps {
  active?: boolean
  children: ReactNode
}

interface PaginationArrowProps extends PaginationProps {
  type: 'previous' | 'next'
}

const PrevNext = ({
  disabled = false,
  type,
  variant = 'arrow',
  onClick,
  appearance = 'button',
  size = 'md',
}: PaginationArrowProps): React.JSX.Element => {
  const theme = useTheme()
  let text = null

  if (variant === 'text') {
    text = type === 'previous' ? 'Prev' : 'Next'
  }

  return (
    <Button
      size={size}
      disabled={disabled}
      color="alternate"
      appearance={appearance}
      onClick={onClick}
      buttonType={text ? 'default' : 'icon'}
    >
      {type === 'previous' && (
        <ChevronLeft size="sm" color={theme.palette.gray[50]} />
      )}
      {text}
      {type === 'next' && (
        <ChevronRight size="sm" color={theme.palette.gray[50]} />
      )}
    </Button>
  )
}

const Prev = (props: PaginationProps): React.JSX.Element => (
  <PrevNext {...props} type="previous" />
)

const Next = (props: PaginationProps): React.JSX.Element => (
  <PrevNext {...props} type="next" />
)

const Item = ({
  active,
  children,
  disabled,
  onClick,
}: PaginationItemProps): React.JSX.Element => (
  <Button
    disabled={disabled}
    color={active === true ? 'black' : 'alternate'}
    onClick={onClick}
  >
    {children}
  </Button>
)

const Dots = (): React.JSX.Element => (
  <div css={{ padding: `${remSpacing.xsmall} ${remSpacing.xxsmall}` }}>
    <Span2>...</Span2>
  </div>
)

export const Pagination = {
  Prev,
  Next,
  Item,
  Dots,
}
