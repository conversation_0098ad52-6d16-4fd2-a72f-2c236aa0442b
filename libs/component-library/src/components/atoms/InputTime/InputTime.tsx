import { useId } from 'react'

import { Tooltip } from '../../molecules/Tooltip'
import { Label } from '../InputAdditionalText'
import { AdditionalText } from '../InputAdditionalText/AdditionalText'
import { BaseInputProps, useInputCSS } from '../InputText'

export interface InputTimeProps extends BaseInputProps {
  className?: string
  errorTooltipContent?: React.ReactNode
  highlightErrorOnly?: boolean
  onChange?: React.ChangeEventHandler<HTMLInputElement>
}

export function InputTime({
  size = 'md',
  success,
  error = '',
  highlightErrorOnly = false,
  errorTooltipContent = null,
  hint = '',
  label = '',
  value = '',
  width,
  required,
  id: idProp,
  ...props
}: InputTimeProps) {
  const inputCSS = useInputCSS(size, success, error, width)

  const uniqueId = useId()
  const id = idProp || `${props.name}-${uniqueId}`
  const errorElement = (
    <AdditionalText error={error} hint={hint} success={success} />
  )

  const errorContent = errorTooltipContent ? (
    <Tooltip isHoverable body={errorTooltipContent} placement="bottom">
      {errorElement}
    </Tooltip>
  ) : (
    errorElement
  )

  return (
    <div>
      <Label htmlFor={id} required={required}>
        {label}
      </Label>
      <input type="time" css={inputCSS} id={id} value={value} {...props} />
      {highlightErrorOnly ? null : errorContent}
    </div>
  )
}
