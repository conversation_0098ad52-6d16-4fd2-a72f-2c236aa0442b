import { useMemo } from 'react'

import styled from '@emotion/styled'
import { rem } from 'polished'

import { mediaQueries } from '../../../utils'
import { VideoPlayerError } from '../../organisms/VideoPlayer/types'
import { Progress } from '../Progress'
import { Span1 } from '../Typography'

enum HttpStatusCodes {
  UNPROCESSABLE_ENTITY = 422,
  NOT_FOUND = 404,
  UNAUTHORIZED = 401,
}

interface VideoPlayerOverlayProps {
  error?: VideoPlayerError
  hasActiveVideo: boolean
  isReady: boolean
  patientBoundingBox?: {
    leftPct: number
    bottomPct: number
    widthPct: number
    heightPct: number
  }
  showBlur: boolean
  showLoading: boolean
}

const VideoPlayerOverlayContainer = styled.div({
  position: 'absolute',
  left: '50%',
  top: '50%',
  transform: 'translate(-50%, -50%)',
  textAlign: 'center',
  width: '100%',
})

interface VideoPlayerBlurOverlayContainerProps {
  bottom?: number
  height?: number
  left?: number
  width?: number
}

export const VideoPlayerBlurOverlayContainer =
  styled.div<VideoPlayerBlurOverlayContainerProps>(
    ({ left = 0.28, bottom = 0.19, width = 0.5, height = 0.46 }) => ({
      position: 'absolute',
      left: `${left * 100}%`,
      bottom: `${bottom * 100}%`,
      width: `${width * 100}%`,
      height: `${height * 100}%`,
      backdropFilter: 'blur(1rem)',
    })
  )

export const headerText = (text: string) => {
  return (
    <p
      css={{
        fontSize: rem('16px'),
        fontWeight: 'bold',
        [mediaQueries.md]: {
          fontSize: rem('16px'),
        },
      }}
    >
      {text}
    </p>
  )
}

export const subText = (text: string) => {
  return (
    <Span1
      css={{
        fontSize: rem('12px'),
        display: 'inline-block',
        [mediaQueries.md]: {
          fontSize: rem('16px'),
        },
      }}
    >
      {text}
    </Span1>
  )
}

export const VideoPlayerOverlay = ({
  error,
  hasActiveVideo,
  isReady,
  patientBoundingBox,
  showBlur,
  showLoading,
}: VideoPlayerOverlayProps): React.JSX.Element | null => {
  const hasError: boolean = useMemo(() => {
    return (
      (error?.error === 'hlsError' && !!error?.data?.fatal) ||
      error?.error?.target?.error !== undefined
    )
  }, [error?.data?.fatal, error?.error])

  const statusCode = error?.data?.response?.code

  const overlayContents = useMemo(() => {
    // If fatal HLS Error or MP4 Loading Error, display error
    if (hasError) {
      if (
        statusCode == HttpStatusCodes.UNPROCESSABLE_ENTITY ||
        statusCode == HttpStatusCodes.NOT_FOUND
      ) {
        return (
          <div>
            {headerText('Video not found.')}
            {subText("Please contact support if there's an issue.")}
          </div>
        )
      }
      if (statusCode == HttpStatusCodes.UNAUTHORIZED) {
        return (
          <div>
            {headerText('You do not have permissions to view this video.')}
            {subText(
              'Please contact your administrator for additional permissions.'
            )}
          </div>
        )
      }

      // Catch any other HLS Errors
      return (
        <div>
          {headerText('Unable to play video.')}
          {subText('Please contact support for assistance.')}
        </div>
      )
    } else if (showLoading) {
      return <Progress size="lg" />
    } else if (!hasActiveVideo) {
      return headerText('No Video Selected')
    } else if (!isReady) {
      return <Progress size="lg" />
    }
  }, [hasActiveVideo, hasError, isReady, statusCode, showLoading])

  if (overlayContents !== undefined) {
    return (
      <VideoPlayerOverlayContainer>
        {overlayContents}
      </VideoPlayerOverlayContainer>
    )
  } else if (showBlur) {
    return (
      <VideoPlayerBlurOverlayContainer
        left={patientBoundingBox?.leftPct}
        bottom={patientBoundingBox?.bottomPct}
        width={patientBoundingBox?.widthPct}
        height={patientBoundingBox?.heightPct}
      />
    )
  } else {
    return null
  }
}
