import { Fragment } from 'react'

import { useTheme } from '@emotion/react'

import { rem } from 'polished'

import { CheckCircle, Error } from '../../../icons'
import { remSpacing } from '../../../utils'
import { Span3 } from '../Typography'

export const AdditionalText = ({
  error = '',
  hint = '',
  success,
  className,
}: {
  className?: string
  error?: string
  hint?: string
  success?: string | boolean
}): React.JSX.Element => {
  const theme = useTheme()

  let alertColor = undefined
  let AlertIcon = undefined
  let alertMessage = undefined

  if (success !== undefined) {
    alertColor = theme.palette.green[50]
    AlertIcon = CheckCircle
    alertMessage = success === true ? '' : success
  } else if (error !== '') {
    alertColor = theme.palette.red[50]
    AlertIcon = Error
    alertMessage = error
  }

  const alertContainer = AlertIcon !== undefined && (
    <div
      className={className}
      css={{
        display: 'flex',
        height: rem('20px'),
      }}
    >
      <AlertIcon size="sm" color={alertColor} />
      <Span3 color={alertColor} css={{ marginLeft: remSpacing.xxsmall }}>
        {alertMessage}
      </Span3>
    </div>
  )

  const hintContainer = hint !== '' && (
    <div css={{ display: 'flex' }}>
      <Span3 color={theme.palette.text.tertiary}>{hint}</Span3>
    </div>
  )

  return (
    <Fragment>
      {alertContainer}
      {hintContainer}
    </Fragment>
  )
}
