import { DateTime } from 'luxon'

import { VideoProgress } from '../../organisms/VideoPlayer/types'

export interface VideoPlayerProgressBarProps {
  isCurrentlyLive?: boolean
  isLivePlayer: boolean
  onChangeProgress: (time: DateTime) => void
  onIsDragging: (isDragging: boolean) => void
  playlistStartTime?: DateTime
  progress?: VideoProgress
  timezone: string
  videoEndTime?: DateTime
  videoStartTime?: DateTime
}

export interface VideoProgressSliderProps {
  right?: number
}

export interface VideoPlayedProgressSliderProps
  extends VideoProgressSliderProps {
  isCurrentlyLive: boolean
}
