import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react'

import { Interpolation, useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { rem } from 'polished'

import { ComponentTheme, remSpacing, shape } from '../../../utils'
import { Color } from '../../../utils/colors'
import { findChildByType } from '../../../utils/reactUtils'
import { ChildrenProps } from '../../types'
import { PH4, PH5, PH6, Span1, Span2, Span3 } from '../Typography'

export const Variants = [
  'border',
  'underline',
  'transparent',
  'filled',
] as const

export interface CellProps extends ChildrenProps {
  css?: Interpolation<ComponentTheme>
  fillColor?: Color
  onClick?: (e: React.MouseEvent) => void
  onMouseEnter?: MouseEventHandler
  role?: string
  style?: React.CSSProperties
  title?: string
  variant?: (typeof Variants)[number]
}

interface CellChildrenProps extends ChildrenProps {
  color?: string
  spacing?: string
}

const CellContainer = styled.div<CellProps>((props) => ({
  alignItems: 'center',
  backgroundColor:
    props.variant === 'filled'
      ? props.fillColor === undefined
        ? props.theme.palette.background.secondary
        : props.theme.palette[props.fillColor].background
      : 'transparent',
  border:
    props.variant === 'border'
      ? `1px solid ${props.theme.palette.gray[30]}`
      : 'none',
  borderBottom:
    props.variant === 'underline'
      ? `1px solid ${props.theme.palette.gray[30]}`
      : undefined,
  borderRadius: props.variant === 'underline' ? 0 : shape.borderRadius.xsmall,
  ...(props.onClick !== undefined && { cursor: 'pointer' }),
  display: 'flex',
  padding: `${remSpacing.xsmall} ${remSpacing.small}`,
  gap: remSpacing.xsmall,
}))

const SmallImageContainer = styled.div(() => ({
  marginRight: remSpacing.small,
  height: remSpacing.large,
}))

const LargeImageContainer = styled.div(() => ({
  marginRight: remSpacing.small,
  height: rem('60px'),
}))

const CellImage = styled.img(() => ({
  backgroundSize: 'contain',
  backgroundPosition: 'center',
  borderRadius: shape.borderRadius.small,
  height: '100%',
  maxHeight: '100%',
}))

const Break = styled.div({ flexBasis: '100%', height: 0 })

const RightContainer = styled.div({
  alignItems: 'center',
  display: 'flex',
  justifyContent: 'flex-end',
  marginLeft: 'auto',
})

export const Cell = ({
  children,
  onClick,
  role,
  onMouseEnter,
  ...props
}: CellProps): React.JSX.Element => {
  const iconLeft = findChildByType(children, IconLeft.displayName)
  const image = findChildByType(children, Image.displayName)
  const label = findChildByType(children, Label.displayName)
  const largerLabel = findChildByType(children, LargerLabel.displayName)
  const title = findChildByType(children, Title.displayName)
  const smallText = findChildByType(children, SmallText.displayName)
  const right = findChildByType(children, Right.displayName)
  const toolTipText = findChildByType(children, ToolTipText.displayName)
  const tooltipLabel = findChildByType(children, ToolTipLabel.displayName)

  return (
    <CellContainer
      onMouseEnter={onMouseEnter}
      role={role}
      onClick={onClick}
      {...props}
    >
      {/* This is garbage, but I'm not sure of a cleaner way to do it */}
      {image !== undefined && smallText === undefined && (
        <SmallImageContainer>{image}</SmallImageContainer>
      )}
      {image !== undefined && smallText !== undefined && (
        <LargeImageContainer>{image}</LargeImageContainer>
      )}
      {iconLeft}
      {(title !== undefined ||
        label !== undefined ||
        largerLabel !== undefined ||
        tooltipLabel !== undefined ||
        smallText !== undefined) && (
        <div
          css={{
            display: 'flex',
            flexDirection: 'column',
            minWidth: '0',
            flexGrow: 1,
            flexWrap: 'nowrap',
            justifyContent: 'flex-start',
            alignItems: 'stretch',
            gap: 0,
          }}
        >
          {title}
          {label}
          {largerLabel}
          {tooltipLabel}
          {!!smallText && (
            <React.Fragment>
              <Break />
              {smallText}
            </React.Fragment>
          )}
        </div>
      )}
      {right !== undefined && <RightContainer>{right}</RightContainer>}
      {toolTipText !== undefined && (
        <RightContainer>{toolTipText}</RightContainer>
      )}
    </CellContainer>
  )
}

const Label = ({ children, color }: CellChildrenProps) => (
  <Span2 color={color}>{children}</Span2>
)
Label.displayName = 'Label'
Cell.Label = Label

const LargerLabel = ({ children, color }: CellChildrenProps) => (
  <PH6 color={color}>{children}</PH6>
)
LargerLabel.displayName = 'LargerLabel'
Cell.LargerLabel = LargerLabel

const ToolTipLabel = ({ children, color }: CellChildrenProps) => (
  <Span1 color={color}>{children}</Span1>
)
ToolTipLabel.displayName = 'ToolTipLabel'
Cell.ToolTipLabel = ToolTipLabel

const Title = ({ children, color }: CellChildrenProps) => (
  <PH5 color={color}>{children}</PH5>
)
Title.displayName = 'Title'
Cell.Title = Title

const Right = ({ children, color }: CellChildrenProps) => (
  <Span3 color={color}>{children}</Span3>
)
Right.displayName = 'Right'
Cell.Right = Right

const ToolTipText = ({ children, color }: CellChildrenProps) => (
  <PH4 color={color}>{children}</PH4>
)
ToolTipText.displayName = 'ToolTipText'
Cell.ToolTipText = ToolTipText

const IconLeft = ({ children, color, ...rest }: CellChildrenProps) => {
  const theme = useTheme()
  return (
    <div
      css={{
        color: color ?? theme.palette.text.tertiary,
        display: 'flex',
      }}
      {...rest}
    >
      {children}
    </div>
  )
}
IconLeft.displayName = 'IconLeft'
Cell.IconLeft = IconLeft

const SmallText = ({ children, color }: CellChildrenProps) => {
  return (
    <Span3 css={{ marginTop: remSpacing.xxsmall }} color={color}>
      {children}
    </Span3>
  )
}
SmallText.displayName = 'SmallText'
Cell.SmallText = SmallText

interface CellImageProps {
  imageUrl: string
}

const Image = ({ imageUrl }: CellImageProps) => <CellImage src={imageUrl} />
Image.displayName = 'Image'
Cell.Image = Image
