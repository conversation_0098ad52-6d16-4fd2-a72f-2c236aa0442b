import { css, useTheme } from '@emotion/react'

import { remSpacing, shape } from '../../../utils'
import { InputProps } from '../../types'
import { Label } from '../InputAdditionalText'
import { AdditionalText } from '../InputAdditionalText/AdditionalText'
import { fontFamily, fontRamp } from '../Typography'

export const useInputCSS = (
  size: string,
  success?: string | boolean,
  error?: string,
  width?: string | number
) => {
  const theme = useTheme()
  const sizeVariables =
    size === 'sm'
      ? css(
          {
            padding: '4px 8px',
          },
          fontRamp['13px']
        )
      : size === 'md'
        ? css(
            {
              padding: '8px 12px',
            },
            fontRamp['16px']
          )
        : css(
            {
              padding: '12px 16px',
            },
            fontRamp['20px']
          )

  const borderColor =
    success !== undefined
      ? theme.palette.green[50]
      : error
        ? theme.palette.red[50]
        : theme.palette.gray[30]

  return css(
    {
      background: theme.palette.background.primary,
      borderRadius: shape.borderRadius.xsmall,
      border: 'none',
      color: theme.palette.text.secondary,
      fontFamily: fontFamily,
      margin: `${remSpacing.xsmall} 0`,
      outline: 'none',
      overflow: 'auto',
      width: width ?? '100%',
      boxShadow: `0px 0px 0px 1px ${borderColor} inset`,
      ':focus': {
        boxShadow: `0px 0px 0px 1px ${theme.palette.blue[50]} inset`,
      },
      ':disabled': {
        opacity: 0.4,
      },
      '::placeholder': {
        color: theme.palette.text.tertiary,
      },
    },
    sizeVariables
  )
}

export interface BaseInputProps extends Omit<InputProps, 'onChange'> {
  autofocus?: boolean
  width?: string | number
}

export type SinglelineInputTextProps = BaseInputProps & {
  multiline?: false
  rows?: undefined
  autoComplete?: boolean | string
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void
  innerRef?: React.ForwardedRef<HTMLInputElement>
  resize?: false
}

export type MultilineInputTextProps = BaseInputProps & {
  multiline: true
  rows?: number
  autoComplete?: boolean | string
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void
  innerRef?: React.ForwardedRef<HTMLTextAreaElement>
  resize?: 'none' | 'both' | 'horizontal' | 'vertical' | 'block' | 'inline'
}

export type InputTextProps = SinglelineInputTextProps | MultilineInputTextProps

export const InputText = ({
  disabled = false,
  error = '',
  hint = '',
  label = '',
  rows = 3,
  name,
  placeholder = '',
  readOnly = false,
  required = false,
  size = 'md',
  success,
  value,
  onBlur,
  onKeyDown,
  autofocus = false,
  autoComplete = false,
  width,
  resize,
  ...props
}: InputTextProps) => {
  const inputCSS = useInputCSS(size, success, error, width)

  const inputProperties = {
    disabled,
    id: name,
    name,
    placeholder,
    readOnly,
    value,
    onBlur,
    onKeyDown,
    autoComplete:
      autoComplete === true
        ? 'on'
        : autoComplete === false
          ? 'off'
          : autoComplete,
  }

  const inputElement = props.multiline ? (
    <textarea
      autoFocus={autofocus}
      ref={props.innerRef}
      rows={rows}
      css={[
        inputCSS,
        {
          height: 'auto',
          resize: resize ? resize : undefined,
        },
      ]}
      onChange={props.onChange}
      {...inputProperties}
    />
  ) : (
    <input
      autoFocus={autofocus}
      ref={props.innerRef}
      type="text"
      css={inputCSS}
      onChange={props.onChange}
      {...inputProperties}
    />
  )

  return (
    <div>
      <Label htmlFor={name} required={required}>
        {label}
      </Label>
      {inputElement}
      <AdditionalText error={error} hint={hint} success={success} />
    </div>
  )
}
