import { type ReactPlayerProps } from 'react-player'

import { DateTime, Duration } from 'luxon'

import { ChildrenProps } from '../../types'

export interface VideoProgress {
  playerCurrentTime: DateTime
  playerLoadedTime?: DateTime
}

interface VideoPlayerVideo {
  endTime: DateTime
  startTime: DateTime
}

export interface VideoPlayerVideoProps extends VideoPlayerVideo {
  cameraId: string
  cameraName?: string
  organizationId: string
  patientBoundingBox?: {
    leftPct: number
    bottomPct: number
    widthPct: number
    heightPct: number
  }
  roomId: string
  siteId: string
  url?: string
}

export interface VideoPlayerVideoWithUrl extends VideoPlayerVideoProps {
  url: string
}

export enum VideoSkipDirection {
  FORWARD = 'forwards',
  BACKWARD = 'backwards',
}

export type VideoSkipControlMapping = Partial<{
  [key: string]: {
    label?: string
    seconds: number
    direction: VideoSkipDirection
  }
}>

export interface VideoPlayerProps extends ChildrenProps {
  apiDomain: string
  debugLoggerMaxSize?: number
  defaultActiveCamera?: string
  getHttpAuthHeader?: () => Promise<string>
  hlsCacheMaxAgeInSeconds?: number
  initialSpeed?: number
  initialStartTime?: DateTime | 'LIVE'
  liveBufferWindowSeconds?: number
  liveMaxSyncPositionSeconds?: number
  onChangeCamera?: (cameraId: string) => void
  onChangeFullscreen?: (isFullscreen: boolean) => void
  onChangeSpeed?: (speed: number) => void
  onFragmentLoaded?: (fragmentLoaded: OnFragmentLoadedProps) => void
  onLevelLoaded?: (levelLoaded: OnLevelLoadedProps) => void
  onPause?: () => void
  onPlay?: () => void
  onProgress?: (progress: VideoProgress) => void
  onReady?: () => void
  onSeek?: (seconds: number) => void
  onWatched?: (videoWatched: OnVideoWatchedProps) => void
  progressInterval?: number
  showLoading?: boolean
  skipControlMapping?: VideoSkipControlMapping
  skipOptions?: number[]
  soundEnabled?: boolean
  speeds?: number[]
  timezone: string
  useNewDirectMediaPlaylistEndpoint: boolean
  videoPrivacyLevel?: 'IMAGE' | 'BLURRED_IMAGE' | 'BLUR_CENTER'
  videos: VideoPlayerVideoProps[]
  volume?: number
}

export interface VideoWatchedEventGeneral {
  epochMillis: number
  name: 'onPause' | 'onPlay' | 'onChangeCamera'
}

export interface VideoWatchedEventProgress {
  epochMillis: number
  name: 'onProgress'
  progress: VideoProgress
}

export interface OnFragmentLoadedProps {
  bufferingSeconds: number
  loadingSeconds: number
  parsingSeconds: number
}

export interface OnLevelLoadedProps {
  loadingSeconds: number
  parsingSeconds: number
}

export type VideoWatchedEvent =
  | VideoWatchedEventGeneral
  | VideoWatchedEventProgress

export interface OnVideoWatchedProps {
  amountOfVideoWatched: Duration
  timeSpentWatchingVideo: Duration
}

export type VideoPlayerComponentRef = {
  seekTo: (time: DateTime) => void
}

export type VideoPlayerRef = {
  pause: () => void
  play: () => void
  seekTo: (time: DateTime) => void
}

export { type ReactPlayerProps as VideoPlayerAPI }

export interface VideoPlayerError {
  data: any
  error: any
  hasError: boolean
}

export interface VideoPlayerState {
  activeVideo?: VideoPlayerVideoWithUrl
  error?: VideoPlayerError
  isLivePlayer: boolean
  nextCameraStartTime?: DateTime
  playing: boolean
  playlistEndTime?: DateTime
  playlistStartTime?: DateTime
  progress?: VideoProgress
  ready: boolean
}
