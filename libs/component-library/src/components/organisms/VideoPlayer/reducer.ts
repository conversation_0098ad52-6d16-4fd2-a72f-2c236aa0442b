import { LevelLoadedData } from 'hls.js'
import { DateTime } from 'luxon'

import {
  VideoPlayerError,
  VideoPlayerState,
  VideoPlayerVideoWithUrl,
  VideoProgress,
} from './types'

export const defaultVideoPlayerState: VideoPlayerState = {
  isLivePlayer: false,
  playing: false,
  ready: false,
}

export type VideoPlayerAction =
  | {
      type: 'CHANGE_CAMERA'
      cameraId: string
      videos: VideoPlayerVideoWithUrl[]
    }
  | {
      type: 'ERROR'
      error: VideoPlayerError
    }
  | {
      type: 'LOAD_VIDEOS'
      defaultActiveCamera?: string
      videos: VideoPlayerVideoWithUrl[]
    }
  | {
      type: 'PAUSE'
    }
  | {
      type: 'PLAY'
    }
  | {
      type: 'TOGGLE_PLAYING'
    }
  | {
      type: 'PLAYLIST_LOADED'
      levelLoadedData: LevelLoadedData
      video: VideoPlayerVideoWithUrl
    }
  | {
      type: 'VIDEO_READY'
      video: VideoPlayerVideoWithUrl
    }
  | {
      type: 'VIDEO_PROGRESS'
      video: VideoPlayerVideoWithUrl
      progress: VideoProgress
    }

export const videoReducer = (
  state: VideoPlayerState,
  action: VideoPlayerAction
): VideoPlayerState => {
  switch (action.type) {
    case 'CHANGE_CAMERA':
      if (action.cameraId !== state.activeVideo?.cameraId) {
        const newActiveVideo = action.videos.find(
          (video) => video.cameraId === action.cameraId
        )

        if (newActiveVideo !== undefined) {
          return {
            ...state,
            activeVideo: newActiveVideo,
            nextCameraStartTime: state.progress?.playerCurrentTime,
            ready: false,
          }
        }
      }
      return state
    case 'ERROR':
      return {
        ...state,
        error: action.error,
        playing: false,
      }
    case 'LOAD_VIDEOS':
      let activeVideo: VideoPlayerVideoWithUrl | undefined = undefined

      // Find the initial active video
      if (action.videos.length > 0) {
        // Find the video with the current camera selection, or the default active camera
        activeVideo =
          action.videos.find(
            (video) => video.cameraId === action.defaultActiveCamera
          ) ?? action.videos[0]
      }

      const videoEndsAfterNow =
        activeVideo !== undefined && activeVideo?.endTime > DateTime.now()
      return {
        ...state,
        activeVideo,
        isLivePlayer: videoEndsAfterNow,
        playing: videoEndsAfterNow,
      }
    case 'PLAY':
      return {
        ...state,
        playing: state.ready,
      }
    case 'PAUSE':
      return {
        ...state,
        playing: false,
      }
    case 'TOGGLE_PLAYING':
      const playing = state.ready ? !state.playing : false
      return {
        ...state,
        playing,
      }
    case 'PLAYLIST_LOADED':
      if (action.video.url === state.activeVideo?.url) {
        // Get the duration of the playlist
        let playlistStartTime: DateTime | undefined = undefined
        let playlistEndTime: DateTime | undefined = undefined
        if (action.levelLoadedData.details.dateRanges['0'] !== undefined) {
          playlistStartTime = DateTime.fromISO(
            action.levelLoadedData.details.dateRanges[0].attr['START-DATE']
          )
          playlistEndTime = DateTime.fromISO(
            action.levelLoadedData.details.dateRanges['0'].attr['END-DATE']
          )
        }

        // Get whether the playlist type is Event, Live, or VOD
        // Live and Event return true for live
        const isPlaylistDetailLive = action.levelLoadedData.details.live

        return {
          ...state,
          isLivePlayer: isPlaylistDetailLive,
          playlistStartTime,
          playlistEndTime,
        }
      }
      return state
    case 'VIDEO_READY':
      return {
        ...state,
        ready: true,
        error: undefined,
      }
    case 'VIDEO_PROGRESS':
      const isActiveVideo = state.activeVideo?.url === action.video.url

      return {
        ...state,
        progress: isActiveVideo ? action.progress : state.progress,
      }
    default:
      const exhaustiveCheck: never = action
      throw new Error(
        `Action not handled in Video Player Reducer: ${exhaustiveCheck}`
      )
  }
}
