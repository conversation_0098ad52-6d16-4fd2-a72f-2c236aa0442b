import React, {
  Fragment,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react'

import { ThemeProvider } from '@emotion/react'

import styled from '@emotion/styled'
import { LevelLoadedData } from 'hls.js'
import { DateTime } from 'luxon'

import { VisibilityOff } from '../../../icons'
import { getGlobalStyles, remSpacing, ZIndex } from '../../../utils'
import { darkTheme } from '../../../utils/theme'
import { useFullscreen } from '../../../utils/useFullscreen'
import { VideoPlayerCamera, VideoPlayerCameraSelector } from '../../atoms'
import { VideoPlayerOverlay } from '../../atoms/VideoPlayerOverlay/VideoPlayerOverlay'
import { VideoPlayerControls } from '../../molecules/VideoPlayerControls'
import { getRandomStaticImage } from './getRandomStaticImage'
import { defaultVideoPlayerState, videoReducer } from './reducer'
import {
  OnFragmentLoadedProps,
  OnLevelLoadedProps,
  OnVideoWatchedProps,
  VideoPlayerComponentRef,
  VideoPlayerProps,
  VideoPlayerRef,
  VideoPlayerVideoWithUrl,
  VideoProgress,
} from './types'
import { VideoPlayerComponent } from './VideoPlayerComponent'
import { VideoWatchedTracker } from './VideoWatchedTracker'

const VideoPlayerContainer = styled.div({
  overflow: 'hidden',
  // 16:9 aspect ratio
  paddingTop: '56.25%',
  position: 'relative',
})

const VideoPlayerFullscreenContainer = styled.div({
  [`&:fullscreen > ${VideoPlayerContainer}`]: {
    left: 0,
    right: 0,
    top: '50%',
    transform: 'translateY(-50%)',
  },
})

const VideoPlayerClickContainer = styled.div({
  cursor: 'pointer',
})

const VideoPlayerCameraContainer = styled.div({
  left: remSpacing.medium,
  position: 'absolute',
  top: remSpacing.medium,
})

const mouseMoveThrottleMs = 500
const mouseMoveTimeoutMs = 3 * 1000

const defaultSkipOptions = [1, 10, 5 * 60, 10 * 60]
const defaultSpeeds = [0.5, 1, 2, 5, 10]

// The max number of seconds from now that the UI will allow you to seek in live view
const defaultLiveMaxSyncPositionSeconds = 20
// The number of seconds from now that count as "live"
const defaultLiveBufferWindowSeconds = 60

const MAX_SPEED = 10
const MIN_SPEED = 0

const generatePlaylistUrl = ({
  domain,
  organizationId,
  siteId,
  roomId,
  cameraId,
  startTime,
  endTime,
  useNewDirectMediaPlaylistEndpoint,
}: {
  domain: string
  organizationId: string
  siteId: string
  roomId: string
  cameraId: string
  startTime: DateTime
  endTime: DateTime
  useNewDirectMediaPlaylistEndpoint: boolean
}) => {
  if (useNewDirectMediaPlaylistEndpoint) {
    return `${domain}/v1/media/${organizationId}/${siteId}/${roomId}/${cameraId}/${startTime.toISO()}/${endTime.toISO()}.m3u8`
  } else {
    return `${domain}/v1/media_playlist/${cameraId}/${startTime.toISO()}/${endTime.toISO()}.m3u8`
  }
}

export const VideoPlayer = React.forwardRef<VideoPlayerRef, VideoPlayerProps>(
  function VideoPlayer(
    {
      apiDomain,
      children,
      debugLoggerMaxSize,
      defaultActiveCamera,
      getHttpAuthHeader,
      hlsCacheMaxAgeInSeconds,
      initialSpeed = 1,
      initialStartTime,
      liveBufferWindowSeconds = defaultLiveBufferWindowSeconds,
      liveMaxSyncPositionSeconds = defaultLiveMaxSyncPositionSeconds,
      progressInterval = 500,
      showLoading = false,
      videoPrivacyLevel,
      skipOptions,
      skipControlMapping,
      soundEnabled = false,
      speeds: initialSpeeds = defaultSpeeds,
      timezone,
      videos,
      volume = 0,
      onChangeCamera: onChangeCameraProp,
      onChangeSpeed: onChangeSpeedProp,
      onChangeFullscreen: onChangeFullscreenProp,
      onFragmentLoaded: onFragmentLoadedProp,
      onLevelLoaded: onLevelLoadedProp,
      onPause: onPauseProp,
      onPlay: onPlayProp,
      onProgress,
      onReady: onReadyProp,
      onSeek: onSeekProp,
      onWatched: onWatchedProp,
      useNewDirectMediaPlaylistEndpoint,
    },
    ref
  ): React.JSX.Element {
    const videoPlayerComponentRef = useRef<VideoPlayerComponentRef>(null)
    const fullscreenContainerRef = useRef<HTMLDivElement | null>(null)

    const speeds = useMemo(
      () =>
        Array.from(
          new Set(
            initialSpeeds
              .filter((s) => s > MIN_SPEED && s <= MAX_SPEED)
              .sort((a, b) => a - b)
          )
        ),
      [initialSpeeds]
    )

    const [state, dispatch] = useReducer(videoReducer, defaultVideoPlayerState)

    useEffect(() => {
      dispatch({
        type: 'LOAD_VIDEOS',
        defaultActiveCamera,
        videos: videosWithUrls,
      })
      // Disabled or else there are multiple dispatches when there should only be one
      // TODO: Investigate more as to why multiple dispatches occur for most renders
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [defaultActiveCamera, videos.length])

    const [controlsOpen, setControlsOpen] = useState(true)
    const [fullscreen, setFullscreen] = useState(false)
    const [isDragging, setIsDragging] = useState(false)
    const [isDraggingSound, setIsDraggingSound] = useState(false)
    const [mouseInControls, setMouseInControls] = useState(false)
    const [soundPct, setSoundPct] = useState(volume)
    const [speed, setSpeed] = useState(initialSpeed)

    const randomStaticImage = useMemo(
      () => getRandomStaticImage(),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [videos]
    )

    const videosWithUrls = useMemo(
      () =>
        videos.map((video) => ({
          ...video,
          url:
            video.url ??
            generatePlaylistUrl({
              domain: apiDomain,
              organizationId: video.organizationId,
              siteId: video.siteId,
              roomId: video.roomId,
              cameraId: video.cameraId,
              useNewDirectMediaPlaylistEndpoint,
              startTime: video.startTime,
              endTime: video.endTime,
            }),
        })),
      [apiDomain, useNewDirectMediaPlaylistEndpoint, videos]
    )

    const [hoverTimeout, setHoverTimeout] = useState<number>()
    const [lastHoverRun, setLastHoverRun] = useState<number>(0)

    const onWatched = useCallback(
      (videoWatched: OnVideoWatchedProps) => {
        if (onWatchedProp !== undefined) {
          onWatchedProp(videoWatched)
        }
      },
      [onWatchedProp]
    )

    const videoWatchedTracker = useMemo(
      () => new VideoWatchedTracker(onWatched),
      [onWatched]
    )

    useEffect(() => {
      videoWatchedTracker.startRollups()
      return () => videoWatchedTracker.stopRollups()
    }, [videoWatchedTracker])

    // On hover open the controls and delay closing them
    const onPlayerMouseMove = useCallback(() => {
      // If the controls are open and it hasn't been 500ms since the
      // last time the mouse moved ignore it to prevent unnecessary re-renders
      const now = Date.now()

      if (controlsOpen === true && now - lastHoverRun < mouseMoveThrottleMs) {
        return
      }

      if (controlsOpen === false) {
        setControlsOpen(true)
      }
      window.clearTimeout(hoverTimeout)

      const newHoverTimeout = window.setTimeout(
        () => setControlsOpen(false),
        mouseMoveTimeoutMs
      )

      // Set the timeout to a function so we can clear it when they leave
      setHoverTimeout(newHoverTimeout)
      setLastHoverRun(now)

      // Clean up our timeouts when the component is unmounted
      return () => window.clearTimeout(newHoverTimeout)
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [controlsOpen, hoverTimeout])

    const onPlayerMouseOut = useCallback(() => {
      setControlsOpen(false)
      window.clearTimeout(hoverTimeout)
      return () => window.clearTimeout(hoverTimeout)
    }, [hoverTimeout])

    useEffect(() => {
      const currentFullscreenRef = fullscreenContainerRef.current
      currentFullscreenRef?.addEventListener('mousemove', onPlayerMouseMove)
      currentFullscreenRef?.addEventListener('mouseleave', onPlayerMouseOut)

      return () => {
        currentFullscreenRef?.removeEventListener(
          'mousemove',
          onPlayerMouseMove
        )
        currentFullscreenRef?.removeEventListener(
          'mouseleave',
          onPlayerMouseOut
        )
      }
    }, [onPlayerMouseMove, onPlayerMouseOut])

    const toggleFullscreen = useCallback(
      (isFullscreen: boolean) => {
        setFullscreen(isFullscreen)
        if (onChangeFullscreenProp) {
          onChangeFullscreenProp(isFullscreen)
        }
      },
      [onChangeFullscreenProp]
    )

    useFullscreen(fullscreenContainerRef, fullscreen, toggleFullscreen)

    // Video Player Container Functions
    const onVideoPlayerContainerClick = useCallback(
      () =>
        dispatch({
          type: 'TOGGLE_PLAYING',
        }),
      []
    )

    // Video Player Functions
    const onError = useCallback((error: any, data: any) => {
      dispatch({ type: 'ERROR', error: { hasError: true, error, data } })
    }, [])

    const onPlay = useCallback(() => {
      dispatch({ type: 'PLAY' })

      if (onPlayProp !== undefined) {
        onPlayProp()
      }

      videoWatchedTracker.logEvent({
        name: 'onPlay',
        epochMillis: Date.now(),
      })
    }, [onPlayProp, videoWatchedTracker])

    const onPause = useCallback(() => {
      dispatch({ type: 'PAUSE' })
      if (onPauseProp !== undefined) {
        onPauseProp()
      }

      videoWatchedTracker.logEvent({
        name: 'onPause',
        epochMillis: Date.now(),
      })
    }, [onPauseProp, videoWatchedTracker])

    const onReady = useCallback(
      (video: VideoPlayerVideoWithUrl) => {
        dispatch({ type: 'VIDEO_READY', video })
        if (onReadyProp) {
          onReadyProp()
        }
      },
      [onReadyProp]
    )

    const onSeek = useCallback(
      (seconds: number) => {
        if (onSeekProp !== undefined) {
          onSeekProp(seconds)
        }
      },
      [onSeekProp]
    )

    const videoPlayerOnProgress = useCallback(
      (video: VideoPlayerVideoWithUrl, progress: VideoProgress) => {
        dispatch({ type: 'VIDEO_PROGRESS', video, progress })

        if (state.activeVideo?.url === video.url) {
          if (onProgress) {
            onProgress(progress)
          }
          videoWatchedTracker.logEvent({
            name: 'onProgress',
            epochMillis: Date.now(),
            progress,
          })
        }
      },
      [onProgress, state.activeVideo?.url, videoWatchedTracker]
    )

    // Video Player Controls Functions
    const onChangeProgress = useCallback(
      (time: DateTime) => {
        if (state.activeVideo !== undefined) {
          // Don't allow someone to seek past the time of the requested video,
          // or past 20 seconds from now for a live playlist,
          // and adjust the progress seconds back to the actual video
          const maxLiveTime = DateTime.now().minus({
            seconds: liveMaxSyncPositionSeconds,
          })

          let seekToTime = time

          if (seekToTime > state.activeVideo.endTime) {
            seekToTime = state.activeVideo.endTime
          } else if (seekToTime > maxLiveTime) {
            seekToTime = maxLiveTime
          }
          videoPlayerComponentRef.current?.seekTo(seekToTime)
        }
      },
      [liveMaxSyncPositionSeconds, state.activeVideo]
    )

    const onMouseEnterControls = useCallback(
      () => setMouseInControls(true),
      [setMouseInControls]
    )

    const onMouseLeaveControls = useCallback(
      () => setMouseInControls(false),
      [setMouseInControls]
    )

    const controlsAreOpen = useMemo(
      () =>
        mouseInControls ||
        isDragging ||
        isDraggingSound ||
        !state.playing ||
        controlsOpen,
      [
        mouseInControls,
        isDragging,
        isDraggingSound,
        state.playing,
        controlsOpen,
      ]
    )

    const cameras = useMemo(
      () =>
        videosWithUrls
          .filter(
            (video) =>
              video.cameraId !== undefined && video.cameraName !== undefined
          )
          .map(
            (video) =>
              ({
                id: video.cameraId,
                name: video.cameraName,
              }) as VideoPlayerCamera
          ),
      [videosWithUrls]
    )

    const onChangeCamera = useCallback(
      (cameraId: string) => {
        if (
          Array.isArray(videosWithUrls) &&
          cameraId !== state.activeVideo?.cameraId
        ) {
          dispatch({ type: 'CHANGE_CAMERA', cameraId, videos: videosWithUrls })
          if (onChangeCameraProp !== undefined) {
            onChangeCameraProp(cameraId)
          }
        }

        videoWatchedTracker.logEvent({
          name: 'onChangeCamera',
          epochMillis: Date.now(),
        })
      },
      [
        onChangeCameraProp,
        state.activeVideo?.cameraId,
        videoWatchedTracker,
        videosWithUrls,
      ]
    )

    const onChangeSpeed = useCallback(
      (speed: number) => {
        setSpeed(speed)

        if (onChangeSpeedProp !== undefined) {
          onChangeSpeedProp(speed)
        }
      },
      [onChangeSpeedProp]
    )

    const cameraSelector = useMemo(
      () =>
        state.activeVideo?.cameraId !== undefined &&
        cameras.some((c) => c.id !== state.activeVideo?.cameraId) && (
          <VideoPlayerCameraContainer>
            <VideoPlayerCameraSelector
              activeCameraId={state.activeVideo.cameraId}
              cameras={cameras}
              isVisible={controlsAreOpen}
              onChangeCamera={onChangeCamera}
            />
          </VideoPlayerCameraContainer>
        ),
      [cameras, controlsAreOpen, onChangeCamera, state.activeVideo?.cameraId]
    )

    const containerStyle = useMemo(
      () => ({
        backgroundColor: state.ready
          ? 'transparent'
          : darkTheme.palette.background.primary,
      }),
      [state.ready]
    )

    // Live Video Functions
    const onFragmentLoaded = useCallback(
      (fragmentLoaded: OnFragmentLoadedProps) => {
        if (onFragmentLoadedProp) {
          onFragmentLoadedProp(fragmentLoaded)
        }
      },
      [onFragmentLoadedProp]
    )

    const onLevelLoaded = useCallback(
      (
        levelLoadedData: LevelLoadedData,
        video: VideoPlayerVideoWithUrl,
        levelLoaded: OnLevelLoadedProps
      ) => {
        dispatch({ type: 'PLAYLIST_LOADED', levelLoadedData, video })
        if (onLevelLoadedProp) {
          onLevelLoadedProp(levelLoaded)
        }
      },
      [onLevelLoadedProp]
    )

    // Is the current progress played within the "live" view buffer window
    const isWithinLiveBufferZone = useMemo(() => {
      if (
        !state.isLivePlayer ||
        state.activeVideo === undefined ||
        state.playlistEndTime === undefined ||
        state.progress === undefined ||
        state.progress.playerCurrentTime === undefined
      )
        return false

      return (
        state.progress.playerCurrentTime.diffNow('seconds').as('seconds') >
        -liveBufferWindowSeconds
      )
    }, [
      liveBufferWindowSeconds,
      state.activeVideo,
      state.isLivePlayer,
      state.playlistEndTime,
      state.progress,
    ])

    // When you are in the live window buffer area set the speed to 1x
    useEffect(() => {
      if (isWithinLiveBufferZone && speed !== 1) {
        onChangeSpeed(1)
      }
    }, [isWithinLiveBufferZone, onChangeSpeed, speed])

    // When the live button is clicked seek to the latest time available, with liveMaxSyncPositionSeconds buffer
    const onClickLive = useCallback(() => {
      if (state.activeVideo !== undefined && state.ready === true) {
        const liveSyncTime = DateTime.now().minus({
          seconds: liveMaxSyncPositionSeconds,
        })

        videoPlayerComponentRef.current?.seekTo(liveSyncTime)

        dispatch({ type: 'PLAY' })
      }
    }, [liveMaxSyncPositionSeconds, state.activeVideo, state.ready])

    const showStaticImage = useMemo(
      () =>
        videoPrivacyLevel === 'IMAGE' || videoPrivacyLevel === 'BLURRED_IMAGE',
      [videoPrivacyLevel]
    )

    useImperativeHandle(ref, () => ({
      seekTo: (time: DateTime) => {
        onChangeProgress(time)
      },
      pause: () => onPause(),
      play: () => onPlay(),
    }))

    return (
      <ThemeProvider theme={darkTheme}>
        <VideoPlayerFullscreenContainer
          ref={fullscreenContainerRef}
          css={getGlobalStyles(darkTheme)}
        >
          <VideoPlayerContainer style={containerStyle}>
            <VideoPlayerClickContainer
              onClick={onVideoPlayerContainerClick}
              // Prevent a user from right-clicking and messing with the video directly
              onContextMenu={onVideoPlayerContainerContextMenu}
            >
              {!showStaticImage ? (
                state.activeVideo && (
                  <VideoPlayerComponent
                    key={state.activeVideo.url}
                    ref={videoPlayerComponentRef}
                    debugLoggerMaxSize={debugLoggerMaxSize}
                    getHttpAuthHeader={getHttpAuthHeader}
                    hlsCacheMaxAgeInSeconds={hlsCacheMaxAgeInSeconds}
                    initialStartTime={
                      state.nextCameraStartTime ?? initialStartTime
                    }
                    liveMaxSyncPositionSeconds={liveMaxSyncPositionSeconds}
                    onError={onError}
                    onFragmentLoaded={onFragmentLoaded}
                    onLevelLoaded={onLevelLoaded}
                    onPause={onPause}
                    onPlay={onPlay}
                    onProgress={videoPlayerOnProgress}
                    onReady={onReady}
                    onSeek={onSeek}
                    playbackRate={speed}
                    playing={state.playing}
                    progressInterval={progressInterval}
                    soundEnabled={soundEnabled}
                    soundPct={soundPct}
                    timezone={timezone}
                    video={state.activeVideo}
                  />
                )
              ) : (
                <Fragment>
                  {videoPrivacyLevel === 'BLURRED_IMAGE' && (
                    <div
                      css={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        width: '100%',
                        height: '100%',
                        backdropFilter: 'blur(1rem)',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        display: 'flex',
                        gap: 8,
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'rgba(255, 255, 255, 0.75)',
                        zIndex: ZIndex.ABOVE,
                      }}
                    >
                      <VisibilityOff />
                      Privacy on
                    </div>
                  )}
                  <img
                    css={{
                      left: 0,
                      position: 'absolute',
                      top: 0,
                    }}
                    src={randomStaticImage}
                  />
                </Fragment>
              )}
            </VideoPlayerClickContainer>
            {children}
            {cameraSelector}
            <VideoPlayerControls
              controlsOpen={controlsAreOpen}
              currentSpeed={speed}
              isCurrentlyLive={isWithinLiveBufferZone}
              isFullscreen={fullscreen}
              isLivePlayer={state.isLivePlayer}
              playing={state.playing}
              playlistStartTime={state.playlistStartTime}
              progress={state.progress}
              soundEnabled={soundEnabled}
              soundPct={soundPct}
              timezone={timezone}
              videoStartTime={state.activeVideo?.startTime}
              videoEndTime={state.activeVideo?.endTime}
              speeds={speeds}
              skipOptions={
                skipOptions ??
                (!skipControlMapping ? defaultSkipOptions : undefined)
              }
              skipControlMapping={skipControlMapping}
              onChangeProgress={onChangeProgress}
              onChangeSoundPct={setSoundPct}
              onChangeSpeed={onChangeSpeed}
              onClickFullScreen={toggleFullscreen}
              onClickLive={onClickLive}
              // TODO: Refactor onClickPlaying so the controls use toggle or play/pause
              onClickPlaying={(playing) =>
                dispatch(playing ? { type: 'PLAY' } : { type: 'PAUSE' })
              }
              onIsDragging={setIsDragging}
              onIsDraggingSound={setIsDraggingSound}
              onMouseEnter={onMouseEnterControls}
              onMouseLeave={onMouseLeaveControls}
            />
            <VideoPlayerOverlay
              error={state.error}
              hasActiveVideo={state.activeVideo !== undefined}
              isReady={state.ready || !videoPrivacyLevel}
              patientBoundingBox={state.activeVideo?.patientBoundingBox}
              showBlur={videoPrivacyLevel === 'BLUR_CENTER'}
              showLoading={showLoading}
            />
          </VideoPlayerContainer>
        </VideoPlayerFullscreenContainer>
      </ThemeProvider>
    )
  }
)

const onVideoPlayerContainerContextMenu = (e: React.MouseEvent) =>
  e.preventDefault()
