import { Duration } from 'luxon'

import { KeyboardShortcutSection } from '../../molecules/KeyboardShortcutHelpOverlay'
import {
  keyboardShortcutKeys,
  videoSkipDefaultShortcutKeys,
} from '../../molecules/VideoPlayerControls/VideoPlayerControls'
import { VideoSkipControlMapping, VideoSkipDirection } from './types'

const getShortcutText = (
  duration: number,
  direction: VideoSkipDirection
): string => {
  const durationObject =
    duration < 60
      ? {
          seconds: duration,
        }
      : {
          minutes: duration / 60,
        }
  return `${
    direction === VideoSkipDirection.FORWARD ? 'Fast forward' : 'Rewind'
  } ${Duration.fromObject(durationObject).toHuman()}`
}

export const getVideoPlayerKeyboardShortcutSection = (
  speeds?: number[],
  skipControlMapping?: VideoSkipControlMapping
): KeyboardShortcutSection => {
  const skipShortcuts = []

  if (skipControlMapping) {
    for (const [key, value] of Object.entries(skipControlMapping)) {
      if (!value) continue
      skipShortcuts.push({
        key: value.label ?? key,
        value: getShortcutText(value.seconds, value.direction),
      })
    }
  } else if (speeds) {
    for (const [key, value] of Object.entries(videoSkipDefaultShortcutKeys)) {
      if (value.option in speeds) {
        skipShortcuts.push({
          key,
          value: getShortcutText(speeds[value.option], value.direction),
        })
      }
    }
  }

  return {
    title: 'Video player',
    shortcuts: [
      ...skipShortcuts,
      {
        key: keyboardShortcutKeys.increaseSpeed,
        value: 'Increase playback speed',
      },
      {
        key: keyboardShortcutKeys.decreaseSpeed,
        value: 'Decrease playback speed',
      },
      {
        key: 'space',
        value: 'Play/pause',
      },
      {
        key: 'f',
        value: 'Toggle fullscreen',
      },
    ],
  }
}
