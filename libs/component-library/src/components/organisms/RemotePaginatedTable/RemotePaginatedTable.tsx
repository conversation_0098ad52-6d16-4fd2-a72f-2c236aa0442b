import {
  useCallback,
  useEffect,
  useMemo,
  useState,
  isValidElement,
} from 'react'

import { css, SerializedStyles, useTheme } from '@emotion/react'

import { Info } from '../../../icons'
import { remSpacing } from '../../../utils'
import { selectByStringOrFn } from '../../../utils/selectorUtils'
import { Table, TBody, TD, TH, THead, TR } from '../../atoms'
import { ProgressOverlay } from '../../atoms/Progress'
import { BulkSelectToggle } from '../../molecules/BulkSelectToggle'
import { NoDataMessage } from '../../molecules/emptyStateMessages'
import { CursorPagination } from '../../molecules/TablePagination/CursorPagination'
import { MinimalTablePagination } from '../../molecules/TablePagination/MinimalTablePagination'
import { Tooltip } from '../../molecules/Tooltip'
import { Direction } from '../../types'
import { onClickHeaderSort } from './remotePaginatedTableSorting'
import { styles } from './styles'
import { OrderBy, RemotePaginatedTableProps } from './types'

export const RemotePaginatedTable = <T extends Record<string, any>>({
  columns,
  conditionalRowStyles = [],
  data,
  hasNextPage,
  hasPreviousPage,
  isLoading = false,
  isMultiSort = true,
  paginationType = 'minimal',
  visibleX = false,
  rowKeySelector,
  sortOrder,
  onChangeSort,
  onNextPageClicked,
  onPreviousPageClicked,
  onRowClicked,
  onRowSelectionChanged,
  ...cursorPaginationProps
}: RemotePaginatedTableProps<T>): React.JSX.Element => {
  const [previousResults, setPreviousResults] = useState<T[]>([])
  const [selectedRows, setSelectedRows] = useState<{ [key: string]: T }>({})

  const rowsData = isLoading === true ? previousResults : data
  useEffect(() => {
    if (isLoading === false) {
      setPreviousResults(data)
    }
  }, [data, isLoading])

  useEffect(() => {
    setSelectedRows({})
  }, [rowsData])

  const toggleRowSelect = useCallback(
    (rowData: T) => {
      if (!onRowSelectionChanged || !rowKeySelector) {
        return
      }
      const key = rowKeySelector(rowData)
      const { [key]: prev, ...rest } = selectedRows

      const newSelectedRows = prev ? { ...rest } : { ...rest, [key]: rowData }
      setSelectedRows(newSelectedRows)
      onRowSelectionChanged(newSelectedRows)
    },
    [onRowSelectionChanged, selectedRows, setSelectedRows, rowKeySelector]
  )

  const toggleAllRowsSelected = useCallback(() => {
    setSelectedRows((prev) => {
      const newRows =
        !rowKeySelector || Object.keys(prev).length ? {} : undefined

      const newRowsSelected =
        newRows ??
        rowsData.reduce(
          (accum, curr) => ({
            ...accum,
            [rowKeySelector!(curr)]: curr,
          }),
          {}
        )
      if (onRowSelectionChanged) {
        onRowSelectionChanged(newRowsSelected)
      }
      return newRowsSelected
    })
  }, [rowsData, rowKeySelector, onRowSelectionChanged])

  const sortOrderByAttribute = useMemo(
    () =>
      sortOrder?.reduce(
        (acc, order) => {
          acc[order.sort] = order
          return acc
        },
        {} as Record<string, OrderBy | undefined>
      ) ?? {},
    [sortOrder]
  )

  const selectedRowsLength = Object.keys(selectedRows).length

  const selected =
    selectedRowsLength / (rowsData.length || 1) === 1 ? 'selected' : undefined

  const indeterminateSelection =
    selectedRowsLength / (rowsData.length || 1) > 0 &&
    selectedRowsLength / (rowsData.length || 1) < 1
      ? 'indeterminate'
      : undefined

  const theme = useTheme()

  const rows = rowsData.map((rowData) => {
    let generatedRowKey = ''

    const selectionType =
      rowKeySelector && !!selectedRows[rowKeySelector(rowData)]
        ? 'selected'
        : undefined

    const row = columns.map((column) => {
      if (
        typeof column.selector === 'string' ||
        typeof column.selector === 'function'
      ) {
        const rowColumn = selectByStringOrFn(rowData, column.selector)

        const formattedValue =
          column.formatter === undefined
            ? rowColumn
            : column.formatter(rowColumn, rowData)

        const columnKey = `${column.name}-${
          isValidElement(formattedValue)
            ? typeof column.selector === 'function'
              ? column.selector(rowData)
              : column.selector
            : formattedValue
        }`
        generatedRowKey += columnKey
        return <TD key={columnKey}>{formattedValue}</TD>
      } else if (column.contents !== undefined) {
        return <TD key={column.name}>{column.contents(rowData)}</TD>
      } else {
        return <TD key={column.name}></TD>
      }
    })

    let rowKey = generatedRowKey
    if (rowKeySelector) {
      rowKey = rowKeySelector(rowData)
    }

    const trOnRowClicked = (rowData: T) => {
      if (!!onRowClicked) {
        onRowClicked(rowData)
      }
    }

    const conditionalStyles = conditionalRowStyles.reduce(
      (styles, conditionalStyle) => {
        if (conditionalStyle.when(rowData) === true) {
          return css`
            ${styles}
            ${conditionalStyle.style}
          `
        }
        return styles
      },
      // I'm not sure I like this. It makes sense if we continue to have an onclick on a row,
      // however I think we should move away from that in general.
      {
        ...(!!onRowClicked ? styles.clickableRow : {}),
      } as SerializedStyles
    )

    return (
      <TR
        key={rowKey}
        css={conditionalStyles}
        onClick={() => trOnRowClicked(rowData)}
      >
        {onRowSelectionChanged && (
          <TD key={`row-select`}>
            <BulkSelectToggle
              toggleType={selectionType}
              onClick={() => toggleRowSelect(rowData)}
            />
          </TD>
        )}
        {row}
      </TR>
    )
  })

  const pagination =
    paginationType === 'minimal' ? (
      <MinimalTablePagination
        hasNextPage={hasNextPage}
        hasPreviousPage={hasPreviousPage}
        onNextPageClicked={onNextPageClicked}
        onPreviousPageClicked={onPreviousPageClicked}
      />
    ) : (
      <CursorPagination {...cursorPaginationProps} />
    )

  return (
    <>
      <section
        css={{
          position: 'relative',
          overflowX: visibleX ? 'visible' : 'auto',
        }}
      >
        <Table>
          <THead>
            <TR>
              {onRowSelectionChanged && (
                <TH key={`header-select`}>
                  <BulkSelectToggle
                    toggleType={selected ?? indeterminateSelection}
                    onClick={toggleAllRowsSelected}
                  />
                </TH>
              )}
              {columns.map(({ key, description, name, sortAttribute }) => {
                const thDirection =
                  sortAttribute !== undefined
                    ? sortOrderByAttribute[sortAttribute]?.direction
                    : undefined

                const thOnClick =
                  sortAttribute !== undefined && onChangeSort !== undefined
                    ? (sortDirection?: Direction) =>
                        onChangeSort(
                          onClickHeaderSort({
                            currentSortOrder: sortOrder,
                            isMultiSort,
                            sortAttribute,
                            sortDirection,
                          })
                        )
                    : undefined

                return (
                  <TH
                    key={key ?? name}
                    sortDirection={thDirection ?? undefined}
                    onClick={thOnClick}
                  >
                    <div
                      css={{
                        display: 'flex',
                        // Keep all the content aligned
                        alignItems: 'flex-end',
                      }}
                    >
                      {name}
                      {typeof description === 'string' && (
                        <Tooltip body={description}>
                          <Info
                            css={{
                              color: theme.palette.gray[50],
                              cursor: 'pointer',
                            }}
                            size="xs"
                          />
                        </Tooltip>
                      )}
                      {description &&
                        isValidElement(description) &&
                        description}
                    </div>
                  </TH>
                )
              })}
            </TR>
          </THead>
          <TBody>{rows}</TBody>
        </Table>
        {isLoading ? (
          <ProgressOverlay />
        ) : rows.length === 0 ? (
          <div role="status" css={{ margin: `${remSpacing.xxxlarge} auto` }}>
            <NoDataMessage />
          </div>
        ) : null}
      </section>
      {pagination}
    </>
  )
}
