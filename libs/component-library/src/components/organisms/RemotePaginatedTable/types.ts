import { CSSObject } from '@emotion/react'

import { CursorPaginationProps } from '../../molecules/TablePagination/CursorPagination'
import { MinimalTablePaginationProps } from '../../molecules/TablePagination/MinimalTablePagination'
import { Direction } from '../../types'

export interface OrderBy {
  direction?: Direction | null
  sort: string
}

/** Return type is passed to `formatter` */
export type SelectorFn<T> = (rowData: T) => any

export interface PaginatedTableColumn<T> {
  contents?: (rowData: T) => React.JSX.Element
  description?: string | React.JSX.Element
  /** value is based on `selector` */
  formatter?: (value: any, rowData: T) => string | React.JSX.Element
  /** Unique key to identify column. If omitted, the `name` will be used instead. */
  key?: string
  name: string
  selector?: SelectorFn<T> | string
  sortAttribute?: string
}

export interface ConditionalStyle {
  style: CSSObject
  when: (rowData: any) => boolean
}

type BasePaginatedTableProps<T> = {
  columns: PaginatedTableColumn<T>[]
  conditionalRowStyles?: ConditionalStyle[]
  data: T[]
  isLoading?: boolean
  isMultiSort?: boolean
  sortOrder?: OrderBy[]
  striped?: boolean
  onChangeSort?: (sortOrder?: OrderBy[]) => void
  onRowClicked?: (rowData: T) => void
}

type NonSelectablePaginatedTableProps<T> = {
  rowKeySelector?: (rowData: T) => string
  onRowSelectionChanged?: undefined
} & BasePaginatedTableProps<T>

type SelectablePaginatedTableProps<T> = {
  rowKeySelector: (rowData: T) => string
  onRowSelectionChanged: (selectedRows: { [key: string]: T }) => void
} & BasePaginatedTableProps<T>

export type PaginatedTableProps<T> =
  | NonSelectablePaginatedTableProps<T>
  | SelectablePaginatedTableProps<T>

export type RemotePaginatedTableProps<T> = PaginatedTableProps<T> &
  MinimalTablePaginationProps &
  CursorPaginationProps & {
    paginationType?: 'minimal' | 'cursor'
    visibleX?: boolean
    modalState?: React.Dispatch<React.SetStateAction<boolean>>
    CustomPhaseDialog?: React.JSX.Element
  }
