import React from 'react'

import styled from '@emotion/styled'

import { P2 } from '../atoms/Typography'

const ENV_HEADER_HEIGHT = '30px'

const P2Extended = styled(P2)`
  line-height: ${ENV_HEADER_HEIGHT};

  & > strong {
    font-weight: 600;
  }
`

export type Environment = 'localhost' | 'development' | 'staging' | 'production'

interface EnvProps {
  environment: Environment
}

const EnvHeader = styled(P2Extended)<EnvProps>(({ theme, ...props }) => ({
  display: 'block',
  textAlign: 'center',
  background:
    props.environment === 'localhost'
      ? theme.palette.gray[30]
      : props.environment === 'development'
        ? theme.palette.red[30]
        : props.environment === 'staging'
          ? theme.palette.orange[30]
          : theme.palette.background.primary,
}))

export const EnvironmentHeader = ({
  environment = 'production',
}: {
  environment?: Environment
}): React.JSX.Element =>
  environment !== 'production' ? (
    <EnvHeader environment={environment}>
      Currently viewing <strong>{environment}</strong>
    </EnvHeader>
  ) : (
    <React.Fragment />
  )
