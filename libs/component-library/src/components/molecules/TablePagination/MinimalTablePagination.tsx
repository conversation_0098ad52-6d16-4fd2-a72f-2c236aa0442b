import { Appearance, BaseButtonProps } from '../../atoms'
import { Pagination } from '../../atoms/Pagination'
import { PaginationContainer } from './helpers'

export interface MinimalTablePaginationProps {
  appearance?: Appearance
  className?: string
  hasNextPage?: boolean
  hasPreviousPage?: boolean
  onNextPageClicked?: () => void
  onPreviousPageClicked?: () => void
  size?: BaseButtonProps['size']
}

export const MinimalTablePagination = ({
  hasNextPage,
  hasPreviousPage,
  onNextPageClicked,
  onPreviousPageClicked,
  appearance,
  size,
  className,
}: MinimalTablePaginationProps): React.JSX.Element => {
  return (
    <PaginationContainer className={className}>
      <Pagination.Prev
        key="prev"
        disabled={!hasPreviousPage}
        onClick={() =>
          onPreviousPageClicked !== undefined && onPreviousPageClicked()
        }
        appearance={appearance}
        size={size}
      />
      <Pagination.Next
        key="next"
        disabled={!hasNextPage}
        onClick={() => onNextPageClicked !== undefined && onNextPageClicked()}
        appearance={appearance}
        size={size}
      />
    </PaginationContainer>
  )
}
