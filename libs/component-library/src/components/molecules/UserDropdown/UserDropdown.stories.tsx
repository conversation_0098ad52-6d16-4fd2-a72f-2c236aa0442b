import { CorporateFare, Edit } from '../../../icons'
import { FlexContainer } from '../../layout'
import { OrganizationProps } from '../../molecules/UserDropdown'
import { SettingProps, UserDropdown } from './UserDropdown'

export default {
  title: 'Molecules/UserDropdown',
  component: UserDropdown,
}

const orgSettings: SettingProps[] = [
  {
    title: 'Org Setting',
    Icon: CorporateFare,
    settingLevel: 'org',
  },
]
const userSettings: SettingProps[] = [
  {
    title: 'User Setting',
    Icon: Edit,
    settingLevel: 'user',
  },
]
const oneOrganization: OrganizationProps = {
  activeOrganizationId: '1',
  organizations: [
    {
      id: '1',
      name: 'Organization 1',
    },
  ],
}
const multipleOrganizations: OrganizationProps = {
  activeOrganizationId: '1',
  organizations: [
    {
      id: '1',
      name: 'Organization 1',
    },
    {
      id: '2',
      name: 'Organization 2',
    },
  ],
}

const commonProps = {
  name: '<PERSON>',
  isExpanded: true,
}

export const Showcase = () => {
  return (
    <FlexContainer
      direction={'column'}
      justifyContent={'flex-end'}
      css={{ width: '300px', height: '100vh' }}
    >
      <UserDropdown
        {...commonProps}
        settings={orgSettings.concat(userSettings)}
        organizationProps={multipleOrganizations}
      />
      <UserDropdown
        {...commonProps}
        settings={orgSettings.concat(userSettings)}
      />
      <UserDropdown
        name="Johnny Really Really Long Name Smith"
        isExpanded={true}
        settings={orgSettings.concat(userSettings)}
        organizationProps={{
          activeOrganizationId: '1',
          organizations: [
            { id: '1', name: 'Organization With A Really Really Long Name' },
          ],
        }}
      />
      <UserDropdown
        {...commonProps}
        isExpanded={false}
        settings={orgSettings.concat(userSettings)}
        organizationProps={multipleOrganizations}
      />
      <UserDropdown
        {...commonProps}
        settings={[]}
        organizationProps={oneOrganization}
      />
    </FlexContainer>
  )
}
