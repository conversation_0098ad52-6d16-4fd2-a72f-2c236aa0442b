import { useMemo, useState } from 'react'

import { useTheme } from '@emotion/react'

import { offset } from '@floating-ui/react'

import { Check, ChevronRight, CorporateFare, ExpandMore } from '../../../icons'
import { remSpacing } from '../../../utils'
import { Button, Cell, Dropdown, DropdownWrapper, MenuItem } from '../../atoms'
import { MenuItemProps } from '../../atoms/MenuItem'
import { FlexContainer, FlexItem } from '../../layout'
import { AvatarLabel, AvatarLabelProps } from '../AvatarLabel'
import { Tooltip } from '../Tooltip'

export interface OrganizationProps {
  activeOrganizationId: string
  onChange?: (id: string) => void
  organizations: OrganizationItem[]
}

export interface OrganizationItem {
  id: string
  name: string
}

export interface UserDropdownProps
  extends Omit<
    UserDropdownContentProps,
    'isOrgSwitcherOpen' | 'onChangeIsOrgSwitcherOpen'
  > {
  isExpanded: boolean
}

export const UserDropdown = ({
  image,
  name,
  organizationProps,
  isExpanded,
  settings,
}: UserDropdownProps) => {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  const [isOrgSwitcherOpen, setIsOrgSwitcherOpen] = useState(false)

  const activeOrganizationName = useMemo(
    () =>
      organizationProps?.organizations.find(
        (org) => org.id === organizationProps.activeOrganizationId
      )?.name,
    [organizationProps]
  )

  // Show the user dropdown button and dropdown content if there are settings or multiple organizations to switch to.
  const showUserDropdown =
    (settings && settings.length > 0) ||
    (organizationProps && organizationProps?.organizations.length > 1)
  if (!showUserDropdown) {
    return (
      <FlexContainer css={{ padding: remSpacing.xsmall }}>
        <AvatarLabel
          labelDisplay={isExpanded ? 'show' : 'hide'}
          subText={activeOrganizationName}
          name={name}
          image={image}
        />
      </FlexContainer>
    )
  }

  return (
    <DropdownWrapper
      display={'inline'}
      open={isOpen}
      onClose={() => {
        setIsOpen(false)
        setIsOrgSwitcherOpen(false)
      }}
      css={{
        width: '100%',
      }}
    >
      <Button
        onClick={() => setIsOpen(!isOpen)}
        appearance={'link'}
        color={'black'}
        css={{
          padding: remSpacing.xxsmall,
          width: '100%',
          background: isOpen ? theme.palette.gray.background : 'initial',
        }}
      >
        <FlexContainer
          alignItems={'center'}
          justifyContent={isExpanded ? 'space-between' : 'flex-start'}
          css={{ width: '100%', textAlign: 'left' }}
        >
          <AvatarLabel
            labelDisplay={isExpanded ? 'show' : 'hide'}
            subText={activeOrganizationName}
            name={name}
            image={image}
          />
          {isExpanded && (
            <div
              css={{
                padding: `0 ${remSpacing.small}`,
              }}
            >
              <ExpandMore
                size={'sm'}
                css={{
                  color: theme.palette.gray[50],
                }}
              />
            </div>
          )}
        </FlexContainer>
      </Button>
      <Dropdown
        open={isOpen}
        // Gotta think about this, it works and puts the complicated one off where it belongs in the implementation
        // However using calc is a bit of a cop out, and can be complicated for others to understand
        above={isExpanded ? `calc(100% + ${remSpacing.xsmall})` : '0%'}
        right={isExpanded ? true : `calc(100% + ${remSpacing.xxsmall})`}
        css={{ width: '100%' }}
      >
        <UserDropdownContent
          settings={settings}
          organizationProps={organizationProps}
          isOrgSwitcherOpen={isOrgSwitcherOpen}
          onChangeIsOrgSwitcherOpen={setIsOrgSwitcherOpen}
          name={name}
          activeOrganizationName={activeOrganizationName}
          image={image}
        />
      </Dropdown>
    </DropdownWrapper>
  )
}

export interface SettingProps extends MenuItemProps {
  /** User-level settings only affect the user, such as "sign out" */
  /** Org-level settings affect the entire site, or org */
  settingLevel: 'user' | 'org'
}

interface UserDropdownContentProps
  extends OrgSwitcherProps,
    Pick<AvatarLabelProps, 'image' | 'name'> {
  activeOrganizationName?: string
  isOrgSwitcherOpen: boolean
  onChangeIsOrgSwitcherOpen: (newVal: boolean) => void
  /** Settings are user-level or org-level settings, administrative tools, or configuration */
  settings: SettingProps[]
}

const UserDropdownContent = ({
  name,
  activeOrganizationName,
  image,
  organizationProps,
  settings,
}: UserDropdownContentProps) => {
  const theme = useTheme()
  const [isOpen, setIsOpen] = useState(false)
  // If there are ever more than two setting levels, please consider refactoring the logic to be
  // generate setting elements more dynamically and less hardcoded.
  const userSettingElems = settings
    ?.filter((setting) => setting.settingLevel == 'user')
    .map((props) => <SettingElem key={props.title} {...props} />)
  const orgSettingElems = settings
    ?.filter((setting) => setting.settingLevel == 'org')
    .map((props) => <SettingElem key={props.title} {...props} />)
  const hasMultipleOrgs =
    organizationProps?.organizations &&
    organizationProps.organizations.length > 1

  return (
    <FlexContainer direction={'column'}>
      <div css={{ padding: `0 ${remSpacing.xsmall}`, overflow: 'hidden' }}>
        <div
          css={{
            padding: `${remSpacing.xsmall} 0 ${remSpacing.small}`,
            overflow: 'hidden',
          }}
        >
          <AvatarLabel
            labelDisplay={'show'}
            subText={activeOrganizationName}
            name={name}
            image={image}
          />
        </div>
        {userSettingElems}
      </div>
      {((orgSettingElems && orgSettingElems.length) || hasMultipleOrgs) && (
        <FlexItem
          css={{
            borderBottom: `1px solid ${theme.palette.gray[30]}`,
            margin: `${remSpacing.small} 0`,
          }}
        />
      )}
      <div css={{ padding: `0 ${remSpacing.xsmall}` }}>
        {orgSettingElems}
        {hasMultipleOrgs && (
          <Tooltip
            isHoverable={true}
            body={<OrgSwitcher organizationProps={organizationProps} />}
            placement="right-end"
            minWidth={'296px'}
            gutter={'0px'}
            onOpenChange={setIsOpen}
            middleware={[offset(0)]}
          >
            <MenuItem active={isOpen}>
              <MenuItem.Title>{'Switch Organization'}</MenuItem.Title>
              <MenuItem.Icon>
                <CorporateFare />
              </MenuItem.Icon>
              <MenuItem.ComponentRight>
                <ChevronRight />
              </MenuItem.ComponentRight>
            </MenuItem>
          </Tooltip>
        )}
      </div>
    </FlexContainer>
  )
}

const SettingElem = (props: SettingProps) => {
  // Removing settingLevel from {...rest} since it's not part of MenuItem Props.
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { componentRight, title, Icon, settingLevel, ...rest } = props
  return (
    <MenuItem {...rest}>
      <MenuItem.Title>{title}</MenuItem.Title>
      <MenuItem.Icon>
        <Icon />
      </MenuItem.Icon>
      <MenuItem.ComponentRight>{componentRight}</MenuItem.ComponentRight>
    </MenuItem>
  )
}

interface OrgSwitcherProps {
  organizationProps?: OrganizationProps
}

const OrgSwitcher = ({ organizationProps }: OrgSwitcherProps) => {
  const theme = useTheme()

  return (
    <FlexContainer
      direction={'column'}
      css={{ padding: `${remSpacing.xsmall} 0 ${remSpacing.xsmall} 0` }}
    >
      {organizationProps?.organizations.map((organization) => (
        <FlexItem key={organization.id}>
          <Cell
            onClick={() =>
              organizationProps?.onChange &&
              organizationProps?.onChange(organization.id)
            }
          >
            <Cell.IconLeft>
              {organizationProps?.activeOrganizationId == organization.id && (
                <Check />
              )}
            </Cell.IconLeft>
            <Cell.Label color={theme.palette.text.secondary}>
              {organization.name}
            </Cell.Label>
          </Cell>
        </FlexItem>
      ))}
    </FlexContainer>
  )
}
