import { CSSProperties, ReactNode, useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'

import { useTheme } from '@emotion/react'

import { parseToRgb, rgba } from 'polished'

import { Close } from '../../../icons'
import { remSpacing, shape } from '../../../utils'
import { Button, H4 } from '../../atoms'
import { FlexContainer } from '../../layout'

const CloseButton = ({ ...props }) => {
  return (
    <Button
      css={{ padding: remSpacing.xxsmall }}
      appearance="link"
      color="black"
      buttonType="icon"
      {...props}
    >
      <Close size="sm" />
    </Button>
  )
}

export const Dialog = ({
  isOpen,
  children,
  onClose,
  title,
  isFullScreen = false,
  height,
  overflow,
  className,
  css,
}: {
  isOpen: boolean
  onClose?: () => void
  children: ReactNode
  title: ReactNode
  isFullScreen?: boolean
  height?: string
  overflow?: string
  className?: string
  css?: CSSProperties
}) => {
  const theme = useTheme()

  const { isPresent: isRootPresent, rootRef } = useRootEl()

  const dialogRef = useRef<HTMLDialogElement>(null)
  useEffect(() => {
    const node = dialogRef.current

    if (isRootPresent && isOpen) {
      node?.showModal()
    } else {
      node?.close()
    }

    return () => {
      node?.close()
    }
  }, [isOpen, isRootPresent])

  if (!isOpen || !rootRef.current) {
    return null
  }

  return createPortal(
    <div
      css={{
        position: 'fixed',
        height: '100vh',
        width: '100vw',
        background: `${rgba({
          ...parseToRgb(theme.palette.background.primary),
          alpha: 0.5,
        })}`,
        top: 0,
        left: 0,
        zIndex: 1,
      }}
    >
      <dialog
        ref={dialogRef}
        css={{
          margin: 'auto',
          marginTop: isFullScreen ? '10px' : '100px',
          maxWidth: isFullScreen ? undefined : '600px',
          minWidth: '300px',
          width: isFullScreen ? '100%' : 'fit-content',
          height: isFullScreen ? '100%' : height ? height : undefined,
          border: 'none',
          borderRadius: `${shape.borderRadius.xsmall}`,
          padding: `${remSpacing.gutter}`,
          backgroundColor: `${theme.palette.background.primary}`,
          boxShadow: `${theme.shadows[2]}`,
          overflow: overflow ? overflow : 'hidden',
          ...css,
        }}
        onClose={onClose}
      >
        <FlexContainer
          css={{ marginBottom: remSpacing.xsmall }}
          alignItems="center"
          justifyContent="space-between"
        >
          <H4 color={theme.palette.gray[90]}>{title}</H4>

          {onClose && <CloseButton onClick={onClose} />}
        </FlexContainer>
        <div className={className}>{children}</div>
      </dialog>
    </div>,
    rootRef.current
  )
}

const useRootEl = () => {
  const rootRef = useRef<HTMLElement | null>(null)
  const [isPresent, setIsPresent] = useState(false)

  useEffect(() => {
    rootRef.current = document.getElementById('root')

    if (!isPresent) {
      setIsPresent(true)
    }
  }, [isPresent])

  return { isPresent, rootRef }
}
