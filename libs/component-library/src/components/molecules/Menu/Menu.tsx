import React, { useEffect, useState } from 'react'
import { <PERSON> } from 'react-router'

import { useTheme } from '@emotion/react'

import { rem } from 'polished'

import { CollapseLeft, CollapseRight } from '../../../icons'
import { ZIndex, mediaQueries, remSpacing } from '../../../utils'
import { ApellaLogo, Caps3, MenuItem } from '../../atoms'
import { MenuItemProps } from '../../atoms/MenuItem'
import { UserDropdown, UserDropdownProps } from '../UserDropdown'
import { MenuItemTooltip } from './MenuItemTooltip'

export interface MenuProps
  extends Pick<
    UserDropdownProps,
    'image' | 'name' | 'settings' | 'organizationProps'
  > {
  /** The footer menu item for non-feature menu items. Often the "Feedback" menu item.
   * Can be expanded to a list of items in the future. */
  footerMenuItem: MenuItemProps
  menuItems: MenuItemProps[]
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}

interface MenuItemElemProps extends MenuItemProps {
  open: boolean
}

const MenuItemElem = ({
  title,
  open,
  Icon,
  componentRight,
  onClick,
  ...rest
}: MenuItemElemProps): React.JSX.Element => {
  const ToolTipComponent = title && !open ? MenuItemTooltip : React.Fragment
  return (
    <ToolTipComponent
      content={title ?? ''}
      placement={'right'}
      key={title}
      css={{ display: 'flex' }}
    >
      <MenuItem
        onClick={onClick}
        {...rest}
        title={title}
        css={{
          display: 'inline-block',
          width: 208,
          '&[aria-expanded=false]': {
            width: 40,
          },
        }}
        aria-expanded={open}
      >
        {open && <MenuItem.Title>{title}</MenuItem.Title>}
        <MenuItem.Icon>
          <Icon size="sm" />
        </MenuItem.Icon>
        {componentRight !== undefined && open && (
          <MenuItem.ComponentRight>{componentRight}</MenuItem.ComponentRight>
        )}
      </MenuItem>
    </ToolTipComponent>
  )
}

const collapsedWidth = 72
const expandedWidth = 240

export const Menu = ({
  image,
  setOpen,
  menuItems,
  footerMenuItem,
  name,
  organizationProps,
  open,
  settings,
}: MenuProps): React.JSX.Element => {
  const theme = useTheme()

  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const mediaQuery = window.matchMedia('(max-width: 724px)')
    const onChange = (e: MediaQueryListEvent) => setIsMobile(e.matches)
    setIsMobile(mediaQuery.matches)
    mediaQuery.addEventListener('change', onChange)
    return () => {
      mediaQuery.removeEventListener('change', onChange)
    }
  }, [])

  return (
    <div
      css={{
        position: 'absolute',
        flexShrink: 0,
        display: 'flex',
        height: '100vh',
        flexDirection: 'column',
        backgroundColor: theme.palette.background.primary,
        boxShadow: theme.shadows[3],
        userSelect: 'none',
        transition: 'width 0.3s ease-in-out',

        zIndex: ZIndex.MORE_ABOVE,
        '&[aria-expanded=false]': {
          display: 'none',
        },
        // full-width on mobile
        [mediaQueries.xs]: {
          '&[aria-expanded=true]': {
            width: '100vw',
          },
        },
        [mediaQueries.sm]: {
          '&[aria-expanded=true]': {
            width: rem(expandedWidth),
          },
        },
        [mediaQueries.lg]: {
          position: 'relative',
          '&[aria-expanded=false]': {
            display: 'flex',
            width: rem(collapsedWidth),
          },
        },
      }}
      aria-expanded={open}
    >
      <div
        style={{
          flex: 'none',
        }}
      >
        <div
          css={{
            padding: `${remSpacing.medium} ${remSpacing.medium} 0`,
          }}
        >
          <Link
            style={{
              overflow: 'hidden',
            }}
            to={'/'}
            onClick={() => setOpen(false)}
          >
            <ApellaLogo type={open ? 'full' : 'logo'} />
          </Link>
          <div
            css={{
              padding: remSpacing.small,
              // When collapsed, we hide the text element
              visibility: !open ? 'hidden' : undefined,
            }}
          >
            <Caps3 color={theme.palette.text.tertiary}>Menu</Caps3>
          </div>
        </div>
      </div>
      <nav
        css={{
          flexGrow: 1,
          paddingLeft: remSpacing.medium,
          overflowY: 'auto',
          overflowX: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          gap: 4,
        }}
      >
        {menuItems.map((menuItem) => (
          <MenuItemElem
            key={menuItem.title}
            {...menuItem}
            open={open}
            onClick={() => (isMobile ? setOpen(false) : setOpen(open))}
          />
        ))}
      </nav>
      <div
        style={{
          padding: `${remSpacing.xsmall} ${remSpacing.medium}`,
          flex: 'none',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          gap: 4,
          borderTop: `1px solid ${theme.palette.gray[30]}`,
        }}
      >
        <MenuItemElem
          key={footerMenuItem.title}
          {...footerMenuItem}
          open={open}
        />
        <MenuItemElem
          open={open}
          title={open ? 'Collapse' : 'Expand'}
          onClick={() => setOpen((prevOpen) => !prevOpen)}
          Icon={open ? CollapseLeft : CollapseRight}
        />
      </div>
      <div
        style={{
          borderBottom: `1px solid ${theme.palette.gray[30]}`,
        }}
      />
      <div style={{ margin: remSpacing.xsmall }}>
        <UserDropdown
          image={image}
          organizationProps={organizationProps}
          name={name}
          settings={settings}
          isExpanded={open}
        />
      </div>
    </div>
  )
}
