import type { Meta, StoryFn } from '@storybook/react'

import { SetStateAction } from 'react'

import { Folder, Highlights, InsertChart, Videocam } from '../../../icons'
import { Counter } from '../../atoms/Counter'
import { MenuItemProps } from '../../atoms/MenuItem'
import {
  OrganizationItem,
  OrganizationProps,
  SettingProps,
} from '../UserDropdown'
import { Menu, MenuProps } from './Menu'

export default {
  title: 'Molecules/Menu',
  component: Menu,
} as Meta

const Template: StoryFn<MenuProps> = (args) => <Menu {...args} />

const menuItems: MenuItemProps[] = [
  {
    active: true,
    Icon: InsertChart,
    title: 'Insights',
    to: '/insights',
  },
  {
    Icon: Highlights,
    title: 'Highlights',
    to: '/highlights',
    componentRight: <Counter size="sm" color="gray" count={1} />,
  },
  {
    Icon: Folder,
    title: 'Cases',
    to: '/cases',
  },
  {
    Icon: Folder,
    title: 'Disabled Cases',
    to: '/cases',
    disabled: true,
  },
  {
    Icon: Folder,
    title: 'onClick not a link',
    onClick: () => {
      console.log('Clicked')
    },
  },
]

const footerMenuItem: MenuItemProps = {
  disabled: true,
  Icon: Videocam,
  title: 'Live View',
  to: '/live',
}

const organizationItems: OrganizationItem[] = [
  {
    name: 'Apella Internal',
    id: 'apella-internal',
  },
  {
    name: 'Scrubs Hospital',
    id: 'scrubs',
  },
  {
    name: "Grey's Anatomy",
    id: 'greys',
  },
]

const settings: SettingProps[] = [
  {
    settingLevel: 'user',
    Icon: Folder,
    title: 'Settings',
  },
  {
    settingLevel: 'org',
    Icon: Videocam,
    title: 'Shut off cameras',
  },
]

const menuFooterOnSelect = (id: string) => {
  console.log(`Selected ${id}`)
}

const menuFooterProps: OrganizationProps = {
  activeOrganizationId: organizationItems[0].id,
  onChange: menuFooterOnSelect,
  organizations: organizationItems,
}

const commonProps: Partial<MenuProps> = {
  menuItems,
  footerMenuItem,
  settings,
  open: true,
  name: 'Dr. John Doe',
  setOpen: (open: SetStateAction<boolean>) => {
    console.log(open)
  },
}

export const WithOrganization = Template.bind({})
WithOrganization.args = {
  ...commonProps,
  organizationProps: menuFooterProps,
}

export const NoOrganization = Template.bind({})
NoOrganization.args = {
  ...commonProps,
}

export const MenuCollapsed = Template.bind({})
MenuCollapsed.args = {
  ...commonProps,
  organizationProps: menuFooterProps,
  open: false,
}

const lotsOfMenuItems = Array.from({ length: 10 }, (_, i) => ({
  active: i === 0,
  Icon: InsertChart,
  title: `Insights ${i}`,
  to: `/insights/${i}`,
}))

export const MenuScrollbar = Template.bind({})
MenuScrollbar.args = {
  ...commonProps,
  menuItems: lotsOfMenuItems,
}
MenuScrollbar.parameters = {
  viewport: {
    defaultViewport: 'mobile2',
    defaultOrientation: 'landscape',
  },
}

export const MenuCollapsedScrollbar = Template.bind({})
MenuCollapsedScrollbar.args = {
  ...commonProps,
  open: false,
  menuItems: lotsOfMenuItems,
}
MenuCollapsedScrollbar.parameters = {
  viewport: {
    viewports: {
      shortAndWide: {
        name: 'Short and Wide',
        styles: {
          width: '1080px',
          height: '500px',
        },
      },
    },
    defaultViewport: 'shortAndWide',
  },
}
