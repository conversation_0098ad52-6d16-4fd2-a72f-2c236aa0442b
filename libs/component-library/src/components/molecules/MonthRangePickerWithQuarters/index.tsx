import React from 'react'

import MonthRangePicker from '../MonthRangePicker'
import { MonthRangePickerProps } from '../MonthRangePicker/types'

type MonthRangePickerWithQuartersProps = Omit<
  MonthRangePickerProps,
  'isOpen' | 'onOpen'
> & {
  readOnly?: boolean
  disabled?: boolean
  defaultOpen?: boolean
  className?: string
}

export const MonthRangePickerWithQuarters = ({
  value,
  onChange,
  onDone,
  onClose,
  maxDate,
  maxMonthRange,
  readOnly = false,
  disabled = false,
  defaultOpen = false,
  name,
  className,
}: MonthRangePickerWithQuartersProps) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen)
  const onOpen = (open: boolean) => {
    setIsOpen(open)
  }
  return (
    <MonthRangePicker
      value={value}
      onChange={onChange}
      onDone={onDone}
      onClose={onClose}
      isOpen={isOpen}
      onOpen={onOpen}
      maxDate={maxDate}
      maxMonthRange={maxMonthRange}
      name={name}
    >
      <MonthRangePicker.Button
        id={name}
        readOnly={readOnly}
        disabled={disabled}
        className={className}
      />
      <MonthRangePicker.Wrapper>
        <MonthRangePicker.Header />
        <MonthRangePicker.MonthQuarterWrapper>
          <MonthRangePicker.Months />
          {typeof maxMonthRange === 'number' && maxMonthRange < 3 ? null : (
            <>
              <MonthRangePicker.Divider />
              <MonthRangePicker.Quarters />
            </>
          )}
        </MonthRangePicker.MonthQuarterWrapper>
        <MonthRangePicker.SubmitButtons />
      </MonthRangePicker.Wrapper>
    </MonthRangePicker>
  )
}
