import { useState } from 'react'

import { Meta, StoryFn } from '@storybook/react'
import { DateTime } from 'luxon'

import { MonthRangePickerWithQuarters } from '.'
import { remSpacing } from '../../../utils'

export default {
  title: 'Molecules/MonthRangePicker/WithQuarters',
  component: MonthRangePickerWithQuarters,
  decorators: [
    (Story) => (
      <div style={{ width: '350px', margin: remSpacing.medium }}>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof MonthRangePickerWithQuarters>

const Template: StoryFn = (args) => {
  const [value, setValue] = useState<DateTime[]>([])

  const handleChange = (value: DateTime[]) => {
    setValue(value)
  }

  const handleDone = (value: DateTime[]) => {
    alert(value)
  }
  return (
    <MonthRangePickerWithQuarters
      value={value}
      onChange={handleChange}
      onDone={handleDone}
      name="monthPicker"
      {...args}
    />
  )
}

export const Default = Template.bind({})
Default.args = {
  defaultOpen: true,
}
Default.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Disabled = Template.bind({})
Disabled.args = {
  disabled: true,
  value: [
    DateTime.fromObject({
      year: 2025,
      month: 1,
      day: 1,
    }),
    DateTime.fromObject({
      year: 2025,
      month: 1,
      day: 28,
    }),
  ],
}
Disabled.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const ReadOnly = Template.bind({})
ReadOnly.args = {
  readOnly: true,
  value: [
    DateTime.fromObject({
      year: 2025,
      month: 1,
      day: 1,
    }),
    DateTime.fromObject({
      year: 2025,
      month: 1,
      day: 28,
    }),
  ],
}
ReadOnly.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const MaxMonthRange = Template.bind({})
MaxMonthRange.args = {
  maxMonthRange: 3,
  defaultOpen: true,
}
MaxMonthRange.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const MaxMonthRangeLessThan1Quarter = Template.bind({})
MaxMonthRangeLessThan1Quarter.args = {
  maxMonthRange: 2,
  defaultOpen: true,
}
MaxMonthRangeLessThan1Quarter.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const MaxDate = Template.bind({})
MaxDate.args = {
  maxDate: DateTime.fromObject({
    year: 2025,
    month: 7,
    day: 7,
  }).endOf('day'),
  defaultOpen: true,
}
MaxDate.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}
