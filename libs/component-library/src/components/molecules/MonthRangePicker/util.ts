import { DateTime } from 'luxon'

import { MAX_MONTH_RANGE_DEFAULT } from './constants'
import { MonthRangePickerOptions, Quarter } from './types'

export const QUARTERS: Quarter[] = ['Q1', 'Q2', 'Q3', 'Q4']

export const QUARTER_MONTH_RANGE_MAP: Record<Quarter, number[]> = {
  Q1: [0, 2],
  Q2: [3, 5],
  Q3: [6, 8],
  Q4: [9, 11],
}

export const addMonth = (date: DateTime, months: number): DateTime => {
  return date.plus({ months })
}

export const subtractMonth = (date: DateTime, months: number): DateTime => {
  return date.minus({ months })
}

export const monthDiff = (startDate: DateTime, endDate: DateTime): number => {
  const start = startDate
  const end = endDate

  return (end.year - start.year) * 12 + (end.month - start.month)
}

export const isMonthIndexInRange = (
  monthIndex: number,
  selectedYear: number,
  startDate?: DateTime,
  endDate?: DateTime
): boolean => {
  const currentMonth = DateTime.fromObject({
    year: selectedYear,
    month: monthIndex + 1,
    day: 1,
  })

  if (!startDate) {
    return false
  }

  if (!endDate) {
    return startDate.hasSame(currentMonth, 'month')
  }

  const startMonth = startDate.startOf('month')
  const endMonth = endDate.startOf('month')

  const rangeDirection = endMonth > startMonth ? 'after' : 'before'

  return rangeDirection === 'after'
    ? currentMonth >= startMonth && currentMonth <= endMonth
    : currentMonth <= startMonth && currentMonth >= endMonth
}

/**
 * Determines if the given date range represents a singular standard time period (1 month or 1 quarter)
 * @param startDate - The start date of the range
 * @param endDate - The end date of the range
 * @returns string - 'month' if the range is a singular month, 'quarter' if it's a singular quarter, or null if neither
 */
export const rangePeriodName = (
  startDate: DateTime,
  endDate: DateTime
): 'month' | 'quarter' | null => {
  const start = startDate
  const end = endDate

  // Check if it's exactly one month (from start to end of month, considering end of day)
  if (start.hasSame(end, 'month') && start.year === end.year) {
    return 'month'
  }

  // Check if it's a quarter (considering end of day)
  for (const quarter of QUARTERS) {
    const [startMonth, endMonth] = QUARTER_MONTH_RANGE_MAP[quarter]
    const quarterStartDate = DateTime.fromObject({
      year: start.year,
      month: startMonth + 1,
      day: 1,
    })
    const quarterEndDate = DateTime.fromObject({
      year: start.year,
      month: endMonth + 1,
      day: 1,
    }).endOf('month')

    if (
      start.startOf('day').hasSame(quarterStartDate.startOf('day'), 'day') &&
      end.endOf('day').hasSame(quarterEndDate.endOf('day'), 'day')
    ) {
      return 'quarter'
    }
  }

  // If we get here, it's not a standard period
  return null
}

/**
 * Give a "new" date range, an existing range, and options, calculate a new range.
 *
 * @param start - The start date of the new range
 * @param end - The end date of the new range
 * @param existingStart - The start date of the existing range (optional)
 * @param existingEnd - The end date of the existing range (optional)
 * @param options - Options for the calculation
 * @param options.maxMonthRange - The maximum number of months allowed in the range
 * @param options.maxDate - The maximum date allowed in the range
 * @returns
 */
export const calcRange = (
  start: DateTime,
  end: DateTime,
  existingStart?: DateTime,
  existingEnd?: DateTime,
  {
    maxMonthRange = MAX_MONTH_RANGE_DEFAULT,
    maxDate,
  }: MonthRangePickerOptions = {
    maxMonthRange: MAX_MONTH_RANGE_DEFAULT,
  }
): DateTime[] => {
  // Ensure chronological order of start/end dates
  const newStart = start < end ? start : end
  const newEnd = start < end ? end : start

  // Apply maxDate threshold to end date, if provided
  let actualEndDate = newEnd
  if (maxDate && newEnd > maxDate) {
    actualEndDate = maxDate
  }
  const newRangeNumMonths = monthDiff(newStart, actualEndDate) + 1
  if (newRangeNumMonths > maxMonthRange) {
    return []
  }

  if (!existingStart || !existingEnd) {
    /**
     * Current: -------------------
     * New:     [-Jan-]------------
     * Result:  [-Jan-]------------
     */
    return [newStart, actualEndDate]
  }

  if (existingStart && existingEnd) {
    const monthsBetween = Math.abs(monthDiff(existingStart, newStart))
    const rangeLargerThanMax = monthsBetween >= maxMonthRange

    if (rangeLargerThanMax) {
      return [newStart, actualEndDate]
    }

    const reseting = existingStart.hasSame(newStart, 'day')
    if (reseting) {
      return []
    }

    /**
     * Current: [-Jan-]-------------
     * New:     ---------[-Apr-]----
     * Result:  [--------------]----
     * OR
     * Current: [-Q1-]-------------
     * New:     ---------[-Q2-]----
     * Result:  [--------------]----
     */
    const existingPeriod = rangePeriodName(existingStart, existingEnd)
    const newPeriod = rangePeriodName(newStart, actualEndDate)

    // Extend range if selected period is the same as existing period
    if (existingPeriod && newPeriod && existingPeriod === newPeriod) {
      return newStart > existingStart
        ? [existingStart, actualEndDate]
        : [newStart, existingEnd]
    }

    return [newStart, actualEndDate]
  }
  return []
}

export function setEndOfMonth(date: DateTime): DateTime {
  return date.endOf('month')
}

export const setBeginningOfMonth = (date: DateTime): DateTime => {
  return date.startOf('month')
}

export const getQuarterDateRange = (
  quarter: Quarter,
  year: number
): DateTime[] => {
  const quarterMonthRange = QUARTER_MONTH_RANGE_MAP[quarter]
  if (!quarterMonthRange) return []

  const [startMonth, endMonth] = quarterMonthRange
  const startDate = DateTime.fromObject({
    year,
    month: startMonth + 1,
    day: 1,
  })

  const endDate = DateTime.fromObject({
    year,
    month: endMonth + 1,
    day: 1,
  }).endOf('month')

  return [startDate, endDate]
}

export function datetimeToDateStr(date: DateTime): string {
  return date.toISODate()
}

/**
 * Given a date range, return the quarters fully contained within the range.
 * If a specific year is provided, only quarters within that year are considered.
 * Otherwise, return all quarters fully contained within the range across years.
 *
 * - ex: Jan 1, 2023 to Mar 31, 2023, in 2023, returns ['Q1'].
 * - ex: Jan 1, 2023 to Apr 15, 2023, in 2023, returns ['Q1'].
 * - ex: Jan 1, 2023 to Dec 31, 2023, in 2023, returns ['Q1', 'Q2', 'Q3', 'Q4'].
 * - ex: Jan 1, 2023 to Feb 28, 2023, in 2023, returns [].
 * - ex: Jan 1, 2023 to Jan 31, 2024, in 2023, returns ['Q1', 'Q2', 'Q3', 'Q4'].
 * - ex: Oct 1, 2023 to Mar 31, 2024, in 2023, returns ['Q4'].
 * - ex: Jul 1, 2023 to Mar 31, 2024, in 2023, returns ['Q3', 'Q4'].
 * - ex: Oct 1, 2023 to Mar 31, 2025, in 2024, returns ['Q1', 'Q2', 'Q3', 'Q4'].
 * - ex: Jan 1, 2023 to Mar 31, 2024, without withinYear, returns ['Q1', 'Q2', 'Q3', 'Q4', 'Q1'].
 *
 * @param startDate - The start date of the range.
 * @param endDate - The end date of the range.
 * @param withinYear - The year to check quarters against (optional).
 * @return An array of quarters that match the date range.
 */
export function getQuartersWithinRange(
  startDate?: DateTime,
  endDate?: DateTime,
  withinYear?: number
): string[] {
  if (!startDate || !endDate) return []

  if (startDate > endDate) return []

  const quarters = []

  if (withinYear) {
    for (const quarter of QUARTERS) {
      const [startMonth, endMonth] = QUARTER_MONTH_RANGE_MAP[quarter]
      const quarterStartDate = DateTime.fromObject({
        year: withinYear,
        month: startMonth + 1,
        day: 1,
      })

      const quarterEndDate = DateTime.fromObject({
        year: withinYear,
        month: endMonth + 1,
        day: 1,
      }).endOf('month')

      if (startDate <= quarterStartDate && endDate >= quarterEndDate) {
        quarters.push(quarter)
      }
    }
  } else {
    let currentStartDate = startDate

    while (startDate <= endDate) {
      const currentYear = currentStartDate.year
      for (const quarter of QUARTERS) {
        const [startMonth, endMonth] = QUARTER_MONTH_RANGE_MAP[quarter]
        const quarterStartDate = DateTime.fromObject({
          year: currentYear,
          month: startMonth + 1,
          day: 1,
        })

        const quarterEndDate = DateTime.fromObject({
          year: currentYear,
          month: endMonth + 1,
          day: 1,
        }).endOf('month')

        if (startDate <= quarterStartDate && endDate >= quarterEndDate) {
          quarters.push(quarter)
        }
      }
      currentStartDate = DateTime.fromObject({
        year: currentYear + 1,
        month: 1,
        day: 1,
      })
    }
  }

  return quarters
}

export const shouldHighlightMonthOnHover = (
  monthIndex: number,
  selectedYear: number,
  startDate: DateTime,
  endDate: DateTime,
  mouseHoverDate: DateTime,
  seekMode: 'before' | 'after',
  maxMonthRange: number
): boolean =>
  monthDiff(startDate, endDate) === 0 &&
  monthDiff(endDate, mouseHoverDate) !== 0 &&
  Math.abs(monthDiff(startDate, mouseHoverDate)) < maxMonthRange &&
  isMonthIndexInRange(
    monthIndex,
    selectedYear,
    seekMode === 'after'
      ? addMonth(startDate, 1)
      : setBeginningOfMonth(mouseHoverDate),
    seekMode === 'after' ? mouseHoverDate : subtractMonth(startDate, 1)
  )
