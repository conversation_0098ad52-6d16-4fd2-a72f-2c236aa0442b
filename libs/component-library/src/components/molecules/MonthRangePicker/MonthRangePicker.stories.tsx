import { useState } from 'react'

import { Meta, StoryFn } from '@storybook/react'
import { DateTime } from 'luxon'

import MonthRangePicker from '.'
import { remSpacing } from '../../../utils'

export default {
  title: 'Molecules/MonthRangePicker/Components',
  component: MonthRangePicker,
  decorators: [
    (Story) => (
      <div style={{ width: '350px', margin: remSpacing.medium }}>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof MonthRangePicker>

const Template: StoryFn = (args) => {
  const [value, setValue] = useState<DateTime[]>([])

  const handleChange = (value: DateTime[]) => {
    setValue(value)
  }

  const handleDone = (value: DateTime[]) => {
    alert(value)
  }
  return (
    <MonthRangePicker
      value={value}
      onChange={handleChange}
      onDone={handleDone}
      name="monthPicker"
      isOpen
      onOpen={() => {}}
      {...args}
    />
  )
}

export const Month = Template.bind({})
Month.args = {
  defaultOpen: true,
  children: (
    <MonthRangePicker.Month
      monthNameFormat="short"
      monthIndex={0}
      year={2025}
    />
  ),
}
Month.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Months = Template.bind({})
Months.args = {
  defaultOpen: true,
  children: <MonthRangePicker.Months />,
}
Months.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Quarters = Template.bind({})
Quarters.args = {
  defaultOpen: true,
  children: <MonthRangePicker.Quarters />,
}
Quarters.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Divider = Template.bind({})
Divider.args = {
  defaultOpen: true,
  children: (
    <div
      css={{
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <div css={{ height: '200px' }}>A</div>
      <MonthRangePicker.Divider />
      <div css={{ height: '200px' }}>B</div>
    </div>
  ),
}
Divider.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Header = Template.bind({})
Header.args = {
  defaultOpen: true,
  children: <MonthRangePicker.Header />,
}
Header.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const SubmitButtons = Template.bind({})
SubmitButtons.args = {
  defaultOpen: true,
  children: <MonthRangePicker.SubmitButtons />,
}
SubmitButtons.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}

export const Button = Template.bind({})
Button.args = {
  defaultOpen: true,
  value: [
    DateTime.fromObject({
      year: 2025,
      month: 4,
      day: 1,
    }),
    DateTime.fromObject({
      year: 2025,
      month: 10,
      day: 30,
    }),
  ],
  children: (
    <MonthRangePicker.Button
      id="monthRangePicker"
      readOnly={false}
      disabled={false}
      className="monthRangePicker"
      onClick={() => {}}
    />
  ),
}
Button.parameters = {
  date: DateTime.fromObject({
    year: 2025,
    month: 4,
    day: 7,
  }),
}
