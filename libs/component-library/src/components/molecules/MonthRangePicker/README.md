# Month Range Picker

This component is a composable set of smaller components that can be composed into whatever custom Month picker you might need. It follows the principles of [Compound Components](https://www.patterns.dev/react/compound-pattern/) which allows components with shared state/logic to be composed independently via the [Context API](https://react.dev/learn/passing-data-deeply-with-context).

See [`MonthRangePickerWithQuarters`](../MonthRangePickerWithQuarters/index.tsx) as a reference implementation.

## Example QuarterSelector

Let's say you have a design that looks like this:

![Quarterly Snapshot Bar Chart with Quarter Selection](docs/image.png)

You can use MonthRangePicker's `<Quarters />` to instead of hand rolling each button:

```tsx
function OverScheduledQuarterlySnapshot() {
  const data = useLoaderData()
  const [searchParams, setSearchParams] = useSearchParams()

  return (
    <>
      <OverScheduledChart data={data} />
      <QuarterSelector
        onChange={([startDate, endDate]) =>
          setSearchParams({ startDate, endDate })
        }
        value={[searchParams.get('startDate'), searchParams.get('endDate')]}
      />
    </>
  )
}

function QuarterSelector({ onChange, value }) {
  return (
    <MonthRangePicker onChange={onChange} value={value} maxMonthRange={3}>
      <MonthRangePicker.Quarters />
    </MonthRangePicker>
  )
}
```
