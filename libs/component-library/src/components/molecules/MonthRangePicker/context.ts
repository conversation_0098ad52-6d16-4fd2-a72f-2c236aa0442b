import React from 'react'

import { DateTime, UnitLength } from 'luxon'

import { SeekMode, Quarter } from './types'

export type MonthRangePickerContextType = {
  selectedYear: number
  startDate?: DateTime
  endDate?: DateTime
  maxMonthRange: number
  maxDate?: DateTime
  mouseHoverDate?: DateTime
  handleMonthClick: (monthIndex: number) => void
  handleMonthHover: React.Dispatch<React.SetStateAction<DateTime | undefined>>
  handleQuarterClick: (quarter: Quarter) => void
  handleYearClick: React.Dispatch<React.SetStateAction<number>>
  selectedQuarters: string[]
  seekMode?: SeekMode
  className?: string
  monthNameFormat: UnitLength
  isOpen: boolean
  toggleOpen: () => void
  close: () => void
  name: string
  onDone?: (value: DateTime[]) => void
  onClose?: () => void
}

export const MonthRangePickerContext =
  React.createContext<MonthRangePickerContextType>({
    selectedYear: 0,
    startDate: undefined,
    endDate: undefined,
    maxMonthRange: 0,
    maxDate: undefined,
    mouseHoverDate: undefined,
    handleMonthHover: () => {},
    handleMonthClick: () => {},
    handleQuarterClick: () => {},
    handleYearClick: () => {},
    selectedQuarters: [],
    seekMode: undefined,
    monthNameFormat: 'short',
    isOpen: false,
    toggleOpen: () => {},
    close: () => {},
    name: 'monthRange',
    onDone: () => {},
    onClose: () => {},
  })
