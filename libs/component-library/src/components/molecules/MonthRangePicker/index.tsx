import React, { useState } from 'react'

import { DateTime, Info } from 'luxon'

import { ArrowDropDown, CalendarIcon } from '../../../icons'
import { pxSpacing, remSpacing, theme } from '../../../utils'
import {
  Button,
  Dropdown,
  DropdownWrapper,
  H4,
  H5,
  Span2,
  Span3,
} from '../../atoms'
import { MinimalTablePagination } from '../TablePagination'
import { MAX_MONTH_RANGE_DEFAULT } from './constants'
import { MonthRangePickerContext } from './context'
import {
  MonthRangePickerButtonProps,
  MonthRangePickerMonthProps,
  MonthRangePickerProps,
  Quarter,
  SeekMode,
} from './types'
import {
  isMonthIndexInRange,
  QUARTERS,
  getQuarterDateRange,
  getQuartersWithinRange,
  calcRange,
  setEndOfMonth,
  shouldHighlightMonthOnHover,
  datetimeToDateStr,
} from './util'

export const MonthRangePicker: React.FC<
  React.PropsWithChildren<MonthRangePickerProps>
> = ({
  value = [],
  onChange,
  onClose,
  onDone,
  maxDate,
  maxMonthRange = MAX_MONTH_RANGE_DEFAULT,
  monthNameFormat = 'short',
  children,
  isOpen = false,
  onOpen,
  dropdownDisplay = 'block',
  name,
}) => {
  const initialYear = value?.length ? value[0].year : DateTime.now().year

  const startDate = value[0]
  const endDate = value[1]
  const [selectedYear, handleYearClick] = useState<number>(initialYear)
  const [mouseHoverDate, handleMonthHover] = useState<DateTime | undefined>(
    undefined
  )
  const close = () => onOpen(false)
  const toggleOpen = () => onOpen(!isOpen)
  let seekMode: SeekMode

  if (mouseHoverDate && startDate) {
    seekMode =
      mouseHoverDate.toMillis() > startDate.toMillis() ? 'after' : 'before'
  }

  const handleMonthClick = (monthIndex: number) => {
    const newStart = DateTime.fromObject({
      year: selectedYear,
      month: monthIndex + 1,
      day: 1,
    })
    const newEnd = setEndOfMonth(newStart)
    onChange(
      calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange,
        maxDate,
      })
    )
  }
  const handleQuarterClick = (quarter: Quarter) => {
    // Get the quarter date range
    const [quarterStartDate, quarterEndDate] = getQuarterDateRange(
      quarter,
      selectedYear
    )
    // Use calcRange with the quarter start and end dates
    onChange(
      calcRange(quarterStartDate, quarterEndDate, startDate, endDate, {
        maxMonthRange,
        maxDate,
      })
    )
  }

  const selectedQuarters = getQuartersWithinRange(
    startDate,
    endDate,
    selectedYear
  )

  return (
    <MonthRangePickerContext.Provider
      value={{
        selectedYear,
        startDate,
        endDate,
        maxMonthRange,
        maxDate,
        mouseHoverDate,
        handleMonthClick,
        handleQuarterClick,
        handleYearClick,
        handleMonthHover,
        selectedQuarters,
        seekMode,
        monthNameFormat,
        isOpen,
        toggleOpen,
        close,
        name,
        onDone,
        onClose,
      }}
    >
      <DropdownWrapper
        display={dropdownDisplay}
        open={isOpen}
        onClose={() => {
          if (onClose) {
            onClose()
          }
          close()
        }}
      >
        {children}
      </DropdownWrapper>
    </MonthRangePickerContext.Provider>
  )
}

export const MonthRangePickerWrapper: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const { isOpen } = React.useContext(MonthRangePickerContext)
  return (
    <Dropdown open={isOpen}>
      <div
        css={{
          marginLeft: remSpacing.medium,
          marginRight: remSpacing.medium,
          marginTop: remSpacing.small,
          marginBottom: remSpacing.small,
        }}
      >
        {children}
      </div>
    </Dropdown>
  )
}

const MonthRangePickerHeader: React.FC = () => {
  const { selectedYear, handleYearClick } = React.useContext(
    MonthRangePickerContext
  )

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '10px',
      }}
    >
      <H5>{selectedYear}</H5>
      <MinimalTablePagination
        css={{ padding: 0 }}
        size="sm"
        appearance="link"
        hasNextPage
        hasPreviousPage
        onNextPageClicked={() => handleYearClick(selectedYear + 1)}
        onPreviousPageClicked={() => handleYearClick(selectedYear - 1)}
      />
    </div>
  )
}

const MonthRangePickerMonths: React.FC = () => {
  const { selectedYear, monthNameFormat } = React.useContext(
    MonthRangePickerContext
  )

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '10px',
        flex: 1,
      }}
    >
      {Array.from({ length: 12 }).map((_, index) => (
        <MonthRangePickerMonth
          key={index}
          monthIndex={index}
          monthNameFormat={monthNameFormat}
          year={selectedYear}
        />
      ))}
    </div>
  )
}

const MonthRangePickerMonth = ({
  year,
  monthIndex,
  monthNameFormat,
}: MonthRangePickerMonthProps) => {
  const {
    startDate,
    endDate,
    maxMonthRange,
    maxDate,
    mouseHoverDate,
    seekMode,
    handleMonthClick,
    handleMonthHover,
  } = React.useContext(MonthRangePickerContext)
  const hoverColor =
    startDate &&
    endDate &&
    mouseHoverDate &&
    seekMode &&
    shouldHighlightMonthOnHover(
      monthIndex,
      year,
      startDate,
      endDate,
      mouseHoverDate,
      seekMode,
      maxMonthRange
    )
      ? theme.palette.blue[10]
      : undefined
  const isMonthBeyondMaxDate = maxDate
    ? DateTime.fromObject({ year, month: monthIndex + 1, day: 1 }).startOf(
        'day'
      ) > maxDate.startOf('day')
    : false

  const month = Info.months(monthNameFormat)[monthIndex]

  return (
    <Button
      css={{
        paddingLeft: remSpacing.medium,
        paddingRight: remSpacing.medium,
        backgroundColor: hoverColor,
        ':hover:not(:active):not(:disabled)': {
          backgroundColor: hoverColor,
        },
      }}
      size="sm"
      key={month}
      disabled={maxDate && isMonthBeyondMaxDate}
      color={
        isMonthIndexInRange(monthIndex, year, startDate, endDate)
          ? 'primary'
          : 'alternate'
      }
      onClick={() => handleMonthClick(monthIndex)}
      onMouseEnter={() =>
        handleMonthHover(
          setEndOfMonth(
            DateTime.fromObject({ year, month: monthIndex + 1, day: 1 })
          )
        )
      }
      onFocus={() =>
        handleMonthHover(
          setEndOfMonth(
            DateTime.fromObject({ year, month: monthIndex + 1, day: 1 })
          )
        )
      }
      onBlur={() => handleMonthHover(undefined)}
    >
      {month}
    </Button>
  )
}

const MonthRangePickerQuarters: React.FC = () => {
  const { selectedYear, handleQuarterClick, selectedQuarters, maxDate } =
    React.useContext(MonthRangePickerContext)
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        justifyContent: 'center',
      }}
    >
      {QUARTERS.map((quarter) => {
        const selected = selectedQuarters.includes(quarter)
        const quarterDateRange = getQuarterDateRange(quarter, selectedYear)

        const isQuarterBeyondMaxDate =
          maxDate && quarterDateRange[1] ? quarterDateRange[1] > maxDate : false

        return (
          <Button
            size="sm"
            css={{
              paddingLeft: remSpacing.large,
              paddingRight: remSpacing.large,
            }}
            key={quarter}
            color={selected ? 'primary' : 'alternate'}
            disabled={maxDate && isQuarterBeyondMaxDate}
            onClick={() => handleQuarterClick(quarter)}
          >
            {quarter}
          </Button>
        )
      })}
    </div>
  )
}

const MonthRangePickerDivider: React.FC = () => {
  return (
    <div
      style={{
        flex: '0 0 auto',
        minWidth: '1px',
        backgroundColor: theme.palette.gray[30],
        marginLeft: remSpacing.xxsmall,
        marginRight: remSpacing.xxsmall,
      }}
    />
  )
}

const MonthRangePickerMonthQuarterWrapper: React.FC<
  React.PropsWithChildren
> = ({ children }) => {
  return (
    <div
      style={{
        display: 'flex',
        gap: '10px',
      }}
    >
      {children}
    </div>
  )
}

const MonthRangePickerButton = ({
  size = 'md',
  readOnly,
  id,
  onClick,
  disabled,
  className,
}: MonthRangePickerButtonProps) => {
  const LabelComponent = size === 'lg' ? H4 : size === 'sm' ? Span3 : Span2
  const { startDate, endDate, isOpen, toggleOpen, name } = React.useContext(
    MonthRangePickerContext
  )
  const startDateString = startDate ? startDate.toFormat('MMMM yyyy') : ''
  const endDateString = endDate ? endDate.toFormat('MMMM yyyy') : ''

  const label =
    startDateString && endDateString
      ? startDateString === endDateString
        ? startDateString
        : `${startDateString} - ${endDateString}`
      : 'Select a date range'

  return (
    <div className={className}>
      <Button
        size={size}
        css={{
          width: '100%',
          ...(readOnly && {
            cursor: 'default',
          }),
        }}
        style={
          isOpen
            ? { boxShadow: `0px 0px 0px 1px ${theme.palette.blue[50]} inset` }
            : {}
        }
        id={id}
        type="button"
        aria-label={label}
        color="alternate"
        onClick={() => {
          if (readOnly || disabled) {
            return
          }
          toggleOpen()
          onClick?.()
        }}
        disabled={disabled}
      >
        <div
          css={{
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            gap: pxSpacing.xsmall,
          }}
        >
          <CalendarIcon color={theme.palette.blue[50]} size="sm" />
          <LabelComponent
            color={
              startDate || endDate
                ? theme.palette.text.secondary
                : theme.palette.text.tertiary
            }
          >
            {label}
          </LabelComponent>
          <ArrowDropDown
            css={{
              color: theme.palette.gray[50],
            }}
            size="sm"
          />
        </div>
      </Button>
      <input
        type="hidden"
        value={startDate ? datetimeToDateStr(startDate) : ''}
        readOnly
        name={`${name}StartDate`}
        id={`${id}-start-date`}
      />
      <input
        type="hidden"
        value={endDate ? datetimeToDateStr(endDate) : ''}
        readOnly
        name={`${name}EndDate`}
        id={`${id}-end-date`}
      />
    </div>
  )
}

const MonthRangePickerSubmitButtons: React.FC = () => {
  const { close, startDate, endDate, onDone, onClose } = React.useContext(
    MonthRangePickerContext
  )
  return onDone ? (
    <div
      style={{
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: remSpacing.medium,
        gap: remSpacing.xsmall,
      }}
    >
      <Button
        color="alternate"
        onClick={() => {
          if (onClose) {
            onClose()
          }
          close()
        }}
      >
        Cancel
      </Button>
      <Button
        onClick={() => {
          if (startDate && endDate) {
            onDone([startDate, endDate])
          }
          close()
        }}
        color="primary"
        type="submit"
      >
        Done
      </Button>
    </div>
  ) : null
}

const MonthRangePickerWithComponents = Object.assign(MonthRangePicker, {
  Header: MonthRangePickerHeader,
  Wrapper: MonthRangePickerWrapper,
  Month: MonthRangePickerMonth,
  Months: MonthRangePickerMonths,
  Quarters: MonthRangePickerQuarters,
  Divider: MonthRangePickerDivider,
  MonthQuarterWrapper: MonthRangePickerMonthQuarterWrapper,
  Button: MonthRangePickerButton,
  SubmitButtons: MonthRangePickerSubmitButtons,
})

export default MonthRangePickerWithComponents
