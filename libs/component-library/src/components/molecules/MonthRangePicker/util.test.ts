import { DateTime } from 'luxon'
import { describe, it, expect } from 'vitest'

import {
  isMonthIndexInRange,
  setEndOfMonth,
  setBeginningOfMonth,
  addMonth,
  subtractMonth,
  getQuarterDateRange,
  getQuartersWithinRange,
  calcRange,
  rangePeriodName,
  shouldHighlightMonthOnHover,
} from './util'

describe('MonthRangePicker utils', () => {
  beforeEach(() => {
    vi.useFakeTimers({ now: DateTime.local(2023, 1, 1).toJSDate() })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('isMonthIndexInRange', () => {
    it('should return true if the month index is within the range', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 12, 31)
      expect(isMonthIndexInRange(11, 2023, startDate, endDate)).toBe(true)
    })

    it('should return false if the month index is outside the range', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 12, 31)
      expect(isMonthIndexInRange(12, 2023, startDate, endDate)).toBe(false)
    })

    it('should return false if startDate is not defined', () => {
      expect(isMonthIndexInRange(0, 2023)).toBe(false)
    })

    it('should return true if there is no endDate but the month index matches the startDate month', () => {
      const startDate = DateTime.local(2023, 1, 1)
      expect(isMonthIndexInRange(0, 2023, startDate)).toBe(true)
    })
  })

  describe('setEndOfMonth', () => {
    it('should set the date to the last day of the month', () => {
      const date = DateTime.local(2023, 1, 15)
      expect(setEndOfMonth(date).day).toBe(31)
    })
  })

  describe('setBeginningOfMonth', () => {
    it('should set the date to the first day of the month', () => {
      const date = DateTime.local(2023, 1, 15)
      expect(setBeginningOfMonth(date).day).toBe(1)
    })
  })

  describe('addMonth', () => {
    it('should correctly add months to a date', () => {
      const date = DateTime.local(2023, 1, 1)
      expect(addMonth(date, 2).month).toBe(3)
    })
  })

  describe('subtractMonth', () => {
    it('should correctly subtract months from a date', () => {
      const date = DateTime.local(2023, 3, 1)
      expect(subtractMonth(date, 2).month).toBe(1)
    })
  })

  describe('getQuarterDateRange', () => {
    it('should return the correct date range for a given quarter', () => {
      const [startDate, endDate] = getQuarterDateRange('Q1', 2023)
      expect(startDate.toISO()).toBe(DateTime.local(2023, 1, 1).toISO())
      expect(endDate.toISO()).toBe(
        DateTime.local(2023, 3, 31, 23, 59, 59, 999).endOf('day').toISO()
      )
    })
  })

  describe('getQuartersWithinRange', () => {
    it('should return a single quarter for a date range matching one quarter', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 3, 31, 23, 59, 59, 999)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual(['Q1'])
    })

    it('should return a full year of quarters for a date range covering the entire year', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 12, 31, 23, 59, 59, 999)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual([
        'Q1',
        'Q2',
        'Q3',
        'Q4',
      ])
    })

    it('should return an empty array for a date range not matching any full quarter', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 2, 28)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual([])
    })

    it('should return the last quarter for a date range starting in Q4 and ending in the next year', () => {
      const startDate = DateTime.local(2023, 10, 1)
      const endDate = DateTime.local(2024, 3, 31)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual(['Q4'])
    })

    it('should return matching quarters for a date range spanning multiple quarters and ending in the next year', () => {
      const startDate = DateTime.local(2023, 4, 1)
      const endDate = DateTime.local(2024, 3, 31)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual([
        'Q2',
        'Q3',
        'Q4',
      ])
    })

    it('should return an empty array if the start date is after the end date', () => {
      const startDate = DateTime.local(2023, 4, 1)
      const endDate = DateTime.local(2023, 3, 31)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual([])
    })

    it('should return an empty array if the start date is undefined', () => {
      const endDate = DateTime.local(2023, 3, 31)
      expect(getQuartersWithinRange(undefined, endDate, 2023)).toEqual([])
    })

    it('should return an empty array if the end date is undefined', () => {
      const startDate = DateTime.local(2023, 1, 1)
      expect(getQuartersWithinRange(startDate, undefined, 2023)).toEqual([])
    })

    it('should return all quarters if withinYear is between start/end years', () => {
      const startDate = DateTime.local(2023, 10, 1)
      const endDate = DateTime.local(2025, 3, 31)
      expect(getQuartersWithinRange(startDate, endDate, 2024)).toEqual([
        'Q1',
        'Q2',
        'Q3',
        'Q4',
      ])
    })

    it('should return partial match quarters', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 5, 15)
      expect(getQuartersWithinRange(startDate, endDate, 2023)).toEqual(['Q1'])
    })
  })

  describe('shouldHighlightMonthOnHover', () => {
    it('should return false if more than a month is already selected', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 3, 31)
      const result = shouldHighlightMonthOnHover(
        0,
        2023,
        startDate,
        endDate,
        DateTime.local(2023, 5, 30),
        'after',
        12
      )
      expect(result).toBe(false)
    })

    it('should return false if selected month is being hovered', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31)
      const result = shouldHighlightMonthOnHover(
        0,
        2023,
        startDate,
        endDate,
        DateTime.local(2023, 1, 31),
        'after',
        12
      )
      expect(result).toBe(false)
    })

    it('should return false if hovering a future month outside the maxMonthRange', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31)
      const result = shouldHighlightMonthOnHover(
        3,
        2023,
        startDate,
        endDate,
        DateTime.local(2023, 1, 31),
        'after',
        3
      )
      expect(result).toBe(false)
    })

    it('should be false if hoving a past month outside the maxMonthRange', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31)
      const result = shouldHighlightMonthOnHover(
        11,
        2022,
        startDate,
        endDate,
        DateTime.local(2023, 1, 31),
        'before',
        3
      )
      expect(result).toBe(false)
    })

    it('should return true if hovering a month within the maxMonthRange', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31)
      const hoverApril = DateTime.local(2023, 4, 30)

      const febResult = shouldHighlightMonthOnHover(
        1,
        2023,
        startDate,
        endDate,
        hoverApril,
        'after',
        12
      )
      expect(febResult).toBe(true)
      const marchResult = shouldHighlightMonthOnHover(
        2,
        2023,
        startDate,
        endDate,
        hoverApril,
        'after',
        12
      )
      expect(marchResult).toBe(true)

      const aprilResult = shouldHighlightMonthOnHover(
        3,
        2023,
        startDate,
        endDate,
        hoverApril,
        'after',
        12
      )
      expect(aprilResult).toBe(true)

      const mayResult = shouldHighlightMonthOnHover(
        4,
        2023,
        startDate,
        endDate,
        hoverApril,
        'after',
        12
      )

      expect(mayResult).toBe(false)
    })
  })

  describe('rangePeriodName', () => {
    it('should return true for a single month period', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      expect(rangePeriodName(startDate, endDate)).toBe('month')
    })

    it('should return true for a quarter period', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 3, 31, 23, 59, 59, 999)
      expect(rangePeriodName(startDate, endDate)).toBe('quarter')
    })

    it('should return false for a non-standard period', () => {
      const startDate = DateTime.local(2023, 1, 15)
      const endDate = DateTime.local(2023, 3, 15, 23, 59, 59, 999)
      expect(rangePeriodName(startDate, endDate)).toBe(null)
    })

    it('should return false for periods spanning multiple months but not a quarter', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 2, 28, 23, 59, 59, 999)
      expect(rangePeriodName(startDate, endDate)).toBe(null)
    })
  })

  describe('calcRange', () => {
    it('should return the new range if no existing selection', () => {
      const newStart = DateTime.local(2023, 1, 1)
      const newEnd = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, undefined, undefined)
      expect(result).toEqual([newStart, newEnd])
    })

    it('should return the new range in chronological order', () => {
      const laterDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const earlierDate = DateTime.local(2023, 1, 1)
      const result = calcRange(laterDate, earlierDate, undefined, undefined)
      expect(result).toEqual([earlierDate, laterDate])
    })

    it('should return empty array if new range exceeds maxMonthRange', () => {
      const newStart = DateTime.local(2023, 1, 1)
      const newEnd = DateTime.local(2023, 5, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, undefined, undefined, {
        maxMonthRange: 3,
      })
      expect(result).toEqual([])
    })

    it('should cap the end date on the max date if ending on same month', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const maxDate = DateTime.local(2023, 1, 15)
      const result = calcRange(startDate, endDate, undefined, undefined, {
        maxMonthRange: 3,
        maxDate,
      })
      expect(result).toEqual([startDate, maxDate])
    })

    it('should expand a month range to the future', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 4, 1)
      const newEnd = DateTime.local(2023, 4, 30, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([startDate, newEnd])
    })

    it('should expand a month range to the past', () => {
      const startDate = DateTime.local(2023, 4, 1)
      const endDate = DateTime.local(2023, 4, 30, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 1, 1)
      const newEnd = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([newStart, endDate])
    })

    it('should reset the range if selecting the same period', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const result = calcRange(startDate, endDate, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([])
    })

    it('should expand a quarter range to the future', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 3, 31, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 7, 1)
      const newEnd = DateTime.local(2023, 9, 30, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([startDate, newEnd])
    })

    it('should expand a quarter range to the past', () => {
      const startDate = DateTime.local(2023, 7, 1)
      const endDate = DateTime.local(2023, 9, 30, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 1, 1)
      const newEnd = DateTime.local(2023, 3, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([newStart, endDate])
    })

    it('should not expand if current selection is not a month or quarter into future', () => {
      const startDate = DateTime.local(2023, 1, 15)
      const endDate = DateTime.local(2023, 3, 15, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 5, 1)
      const newEnd = DateTime.local(2023, 5, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([newStart, newEnd])
    })

    it('should not expand if current selection is not a month or quarter into past', () => {
      const startDate = DateTime.local(2023, 5, 15)
      const endDate = DateTime.local(2023, 7, 15, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 1, 1)
      const newEnd = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 12,
      })
      expect(result).toEqual([newStart, newEnd])
    })

    it('should respect maxMonthRange when expanding', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 12, 1)
      const newEnd = DateTime.local(2023, 12, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 6,
      })
      expect(result).toEqual([newStart, newEnd])
    })

    it('should create new range if outside maxMonthRange', () => {
      const startDate = DateTime.local(2023, 1, 1)
      const endDate = DateTime.local(2023, 1, 31, 23, 59, 59, 999)
      const newStart = DateTime.local(2023, 8, 1)
      const newEnd = DateTime.local(2023, 8, 31, 23, 59, 59, 999)
      const result = calcRange(newStart, newEnd, startDate, endDate, {
        maxMonthRange: 6,
      })
      expect(result).toEqual([newStart, newEnd])
    })
  })
})
