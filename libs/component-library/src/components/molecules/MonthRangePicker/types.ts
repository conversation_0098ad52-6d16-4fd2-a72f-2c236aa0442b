import { DateTime, UnitLength } from 'luxon'

import { BaseButtonProps, DropdownWrapperProps } from '../../atoms'

export type Quarter = 'Q1' | 'Q2' | 'Q3' | 'Q4'

export const isQuarter = (value: string): value is Quarter => {
  return ['Q1', 'Q2', 'Q3', 'Q4'].includes(value)
}

export type MonthRangePickerProps = {
  // value must include DateTime's time component
  value: DateTime[]
  onChange: (value: DateTime[]) => void
  onDone?: (range: DateTime[]) => void
  onClose?: () => void
  monthNameFormat?: UnitLength
  isOpen: boolean
  onOpen: (value: boolean) => void
  dropdownDisplay?: DropdownWrapperProps['display']
  name: string
} & MonthRangePickerOptions

export type MonthRangePickerOptions = {
  maxMonthRange?: number
  maxDate?: DateTime
}

export type MonthRangePickerMonthProps = {
  monthIndex: number
  year: number
  monthNameFormat: UnitLength
}

export type MonthRangePickerButtonProps = {
  size?: BaseButtonProps['size']
  readOnly: boolean
  id: string
  onClick?: () => void
  disabled: boolean
  className?: string
}

export type SeekMode = 'before' | 'after' | undefined
