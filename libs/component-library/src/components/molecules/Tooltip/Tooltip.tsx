import React, { Fragment, ReactNode, useState } from 'react'

import {
  arrow as arrowMiddleware,
  autoPlacement as autoPlacementMiddleware,
  flip as flipMiddleware,
  FloatingPortal,
  hide as hideMiddleware,
  inline as inlineMiddleware,
  Middleware,
  offset as offsetMiddleware,
  Placement,
  safePolygon,
  shift as shiftMiddleware,
  size as sizeMiddleware,
  useFloating,
  useHover,
  useInteractions,
  Strategy,
  useDismiss,
} from '@floating-ui/react'

import { remSpacing, ZIndex } from '../../../utils'
import { Tile, TileProps } from '../../atoms/Tile'

export interface TooltipProps extends TileProps {
  additionalCss?: React.CSSProperties
  body: ReactNode
  children: ReactNode
  className?: string
  closeDelay?: number
  defaultIsOpen?: boolean
  hoverIntent?: {
    cursorRestMs?: number
  }
  isDismissable?: boolean
  isFloatingPortal?: boolean
  isHoverable?: boolean
  isTouchEnabled?: boolean
  middleware?: Middleware[]
  onOpenChange?: (isOpen: boolean) => void
  placement?: Placement
  strategy?: Strategy
}
export const Tooltip = ({
  children,
  body,
  placement,
  defaultIsOpen = false,
  middleware: middlewareProp,
  strategy: strategyProp = 'absolute',
  isHoverable = false,
  isTouchEnabled = true,
  hoverIntent,
  isFloatingPortal = true,
  onOpenChange,
  additionalCss,
  isDismissable = false,
  ...tileProps
}: TooltipProps): React.JSX.Element => {
  const [isOpen, setIsOpen] = useState<boolean>(defaultIsOpen)
  const middleware: Middleware[] = React.useMemo(() => {
    if (middlewareProp) {
      return middlewareProp
    }

    const defaultMiddleware = [offsetMiddleware(16)]

    if (placement === undefined) {
      defaultMiddleware.push(autoPlacementMiddleware())
    }

    return defaultMiddleware
  }, [placement, middlewareProp])

  const { floatingStyles, refs, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      setIsOpen(open)
      onOpenChange?.(open)
    },
    placement,
    middleware,
    strategy: strategyProp,
  })

  const hover = useHover(context, {
    handleClose: isHoverable ? safePolygon() : null,
    mouseOnly: !isTouchEnabled,
    restMs: hoverIntent?.cursorRestMs,
  })

  const dismiss = useDismiss(context, {
    enabled: isDismissable,
    referencePress: true,
  })

  const { getReferenceProps, getFloatingProps } = useInteractions([
    hover,
    dismiss,
  ])

  // Creating a portal can cause issues with the tooltip not working in fullscreen mode
  const Portal = isFloatingPortal ? FloatingPortal : Fragment

  return (
    <div ref={refs.setReference} {...getReferenceProps()}>
      {children}
      {isOpen && (
        <Portal>
          <Tile
            css={{
              ...floatingStyles,
              zIndex: ZIndex.MORE_ABOVE,
              ...additionalCss,
            }}
            maxWidth={'325px'}
            ref={refs.setFloating}
            gutter={remSpacing.medium}
            {...tileProps}
            {...getFloatingProps()}
          >
            {body}
          </Tile>
        </Portal>
      )}
    </div>
  )
}

Tooltip.middleware = {
  offset: offsetMiddleware,
  shift: shiftMiddleware,
  flip: flipMiddleware,
  arrow: arrowMiddleware,
  size: sizeMiddleware,
  autoPlacement: autoPlacementMiddleware,
  hide: hideMiddleware,
  inline: inlineMiddleware,
}
