import React, { useMemo } from 'react'

import { useTheme } from '@emotion/react'

import styled from '@emotion/styled'
import { rem } from 'polished'

import { Fullscreen, FullscreenExit, Pause, PlayArrow } from '../../../icons'
import { mediaQueries, remSpacing, transitions, ZIndex } from '../../../utils'
import {
  VideoPlayerProgressBar,
  VideoPlayerProgressBarProps,
} from '../../atoms/VideoPlayerProgressBar'
import { VideoPlayerSkip } from '../../atoms/VideoPlayerSkip'
import {
  VideoPlayerSound,
  VideoPlayerSoundProps,
} from '../../atoms/VideoPlayerSound'
import {
  VideoPlayerSpeedSelector,
  VideoPlayerSpeedSelectorProps,
} from '../../atoms/VideoPlayerSpeedSelector'
import {
  VideoPlayerTimer,
  VideoPlayerTimerDisplayType,
} from '../../atoms/VideoPlayerTimer'
import {
  VideoSkipControlMapping,
  VideoSkipDirection,
} from '../../organisms/VideoPlayer/types'
import {
  VideoPlayerLiveButton,
  VideoPlayerLiveButtonProps,
} from '../VideoPlayerLiveButton'

export interface VideoPlayerControlsProps
  extends VideoPlayerLiveButtonProps,
    VideoPlayerProgressBarProps,
    VideoPlayerSoundProps,
    VideoPlayerSpeedSelectorProps {
  controlsOpen?: boolean
  isFullscreen?: boolean
  isLivePlayer: boolean
  onClickFullScreen: (fullscreen: boolean) => void
  onClickPlaying: (playing: boolean) => void
  onMouseEnter: () => void
  onMouseLeave: () => void
  playing: boolean
  skipControlMapping?: VideoSkipControlMapping
  skipOptions?: number[]
  soundEnabled?: boolean
}

interface VideoPlayerControlsContainerProps {
  isOpen: boolean
}

const videoPlayerControlsHeight = 112

const VideoPlayerControlsContainer = styled.div({
  bottom: 0,
  left: 0,
  position: 'absolute',
  right: 0,
})

export const keyboardShortcutKeys = {
  decreaseSpeed: '<',
  increaseSpeed: '>',
  playPause: ' ',
  fullscreen: 'f',
} as const

export const videoSkipDefaultShortcutKeys = {
  j: { option: 0, direction: VideoSkipDirection.FORWARD },
  J: { option: 1, direction: VideoSkipDirection.FORWARD, label: 'shift + j' },
  "'": { option: 2, direction: VideoSkipDirection.FORWARD },
  '"': { option: 3, direction: VideoSkipDirection.FORWARD, label: 'shift + "' },
  l: { option: 0, direction: VideoSkipDirection.BACKWARD },
  L: { option: 1, direction: VideoSkipDirection.BACKWARD, label: 'shift + l' },
  ';': { option: 2, direction: VideoSkipDirection.BACKWARD },
  ':': { option: 3, direction: VideoSkipDirection.BACKWARD },
} as const

const VideoPlayerControlsSlider = styled.div<VideoPlayerControlsContainerProps>(
  ({ isOpen }) => ({
    background:
      'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.9) 68.23%)',
    display: 'flex',
    height: rem(`${videoPlayerControlsHeight}px`),
    flexDirection: 'column-reverse',
    minHeight: rem(`${videoPlayerControlsHeight}px`),
    padding: remSpacing.medium,
    transform: isOpen === true ? 'translateY(0%)' : 'translateY(100%)',
    transition: `transform ${transitions.fast}`,
  })
)

const VideoPlayerControlsIcon = styled.div(() => ({
  cursor: 'pointer',
  display: 'flex',
  [mediaQueries.md]: {
    marginRight: remSpacing.gutter,
  },
  marginRight: '0px',
}))

const VideoPlayerControlsIcons = styled.div(() => ({
  alignItems: 'center',
  display: 'flex',
  marginTop: remSpacing.xsmall,
  [`${VideoPlayerControlsIcon}:last-child`]: {
    marginRight: 0,
  },
}))

export const VideoPlayerControls = ({
  controlsOpen = true,
  currentSpeed,
  isCurrentlyLive,
  isFullscreen = false,
  isLivePlayer,
  soundEnabled = true,
  playing,
  playlistStartTime,
  progress,
  soundPct,
  speeds,
  timezone,
  videoStartTime,
  videoEndTime,
  skipOptions,
  skipControlMapping,
  onChangeProgress,
  onChangeSoundPct,
  onChangeSpeed,
  onClickLive,
  onClickFullScreen,
  onClickPlaying,
  onIsDragging,
  onIsDraggingSound,
  onMouseEnter,
  onMouseLeave,
}: VideoPlayerControlsProps): React.JSX.Element => {
  const theme = useTheme()

  const onClickPlayPauseIcon = React.useCallback(
    () => onClickPlaying(!playing),
    [playing, onClickPlaying]
  )

  const onClickFullScreenIcon = React.useCallback(
    () => onClickFullScreen(!isFullscreen),
    [isFullscreen, onClickFullScreen]
  )

  const PlayPauseIcon = React.useMemo(
    () => (playing === true ? Pause : PlayArrow),
    [playing]
  )
  const FullScreenIcon = React.useMemo(
    () => (isFullscreen === true ? FullscreenExit : Fullscreen),
    [isFullscreen]
  )

  const onSkipClick = React.useCallback(
    (seconds?: number) => {
      if (!progress?.playerCurrentTime || !seconds) return
      onChangeProgress(progress.playerCurrentTime.plus({ seconds }))
      onClickPlaying(false)
    },
    [onChangeProgress, onClickPlaying, progress?.playerCurrentTime]
  )

  if (skipOptions && skipControlMapping) {
    throw new Error(
      'Only one of skipOptions or skipControlMapping should be provided'
    )
  }

  const skipOptionsSorted = React.useMemo(() => {
    if (skipControlMapping) {
      return Object.values(skipControlMapping)
        .filter((o) => o?.direction === VideoSkipDirection.FORWARD)
        .map((o) => o?.seconds ?? 0)
        .sort((a, b) => a - b)
    } else if (skipOptions) {
      return skipOptions.sort((a, b) => a - b)
    }
  }, [skipOptions, skipControlMapping])

  const filteredSpeeds = useMemo(
    () => (isCurrentlyLive ? [1] : speeds),
    [isCurrentlyLive, speeds]
  )

  // Keyboard Events
  React.useEffect(() => {
    const keyDownHandler = (event: KeyboardEvent) => {
      const element = event.target as HTMLElement

      // Only perform the keyboard shortcuts if a user isn't focused inside of an element
      // This will help prevent us from having to prevent bubbling on every input with a video player on the page
      if (
        element !== undefined &&
        (element.tagName === undefined ||
          element.tagName.toUpperCase() !== 'BODY')
      ) {
        return
      }

      // Handle video skipping
      // If we have a skipControlMapping, use that to handle skipping
      if (skipControlMapping && event.key in skipControlMapping) {
        if (!(event.key in skipControlMapping)) return
        const skipControl = skipControlMapping[event.key]
        skipControl &&
          (skipControl.direction === VideoSkipDirection.FORWARD
            ? onSkipClick(skipControl.seconds)
            : onSkipClick(0 - skipControl.seconds))
      }
      // Otherwise, if we don't have a skipControlMapping, see if we have skipOptions and a default mapping for the key
      else if (
        skipOptionsSorted &&
        ((key: string): key is keyof typeof videoSkipDefaultShortcutKeys =>
          key in videoSkipDefaultShortcutKeys)(event.key)
      ) {
        const skipControl = videoSkipDefaultShortcutKeys[event.key]
        if (skipOptionsSorted && skipControl.option in skipOptionsSorted) {
          skipControl.direction === VideoSkipDirection.FORWARD
            ? onSkipClick(skipOptionsSorted[skipControl.option])
            : onSkipClick(0 - (skipOptionsSorted[skipControl.option] ?? 0))
        }
      } else {
        switch (event.key) {
          case keyboardShortcutKeys.fullscreen:
            onClickFullScreen(!isFullscreen)
            event.preventDefault()
            break
          case keyboardShortcutKeys.playPause:
            onClickPlaying(!playing)
            event.preventDefault()
            break
          case keyboardShortcutKeys.decreaseSpeed:
            if (!currentSpeed) return
            const decreaseIndex = filteredSpeeds.indexOf(currentSpeed)
            const newDecreaseIndex =
              decreaseIndex - 1 < 0 ? 0 : decreaseIndex - 1
            onChangeSpeed(filteredSpeeds[newDecreaseIndex])
            break
          case keyboardShortcutKeys.increaseSpeed:
            if (!currentSpeed) return
            const increaseIndex = filteredSpeeds.indexOf(currentSpeed)
            const newIncreaseIndex =
              increaseIndex + 1 > filteredSpeeds.length - 1
                ? filteredSpeeds.length - 1
                : increaseIndex + 1
            onChangeSpeed(filteredSpeeds[newIncreaseIndex])
            break
        }
      }
    }
    window.addEventListener('keydown', keyDownHandler)

    return () => {
      window.removeEventListener('keydown', keyDownHandler)
    }
  }, [
    filteredSpeeds,
    currentSpeed,
    playing,
    isFullscreen,
    onClickFullScreen,
    onClickPlaying,
    onSkipClick,
    skipOptionsSorted,
    onChangeSpeed,
    skipControlMapping,
  ])

  return (
    <VideoPlayerControlsContainer
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <VideoPlayerControlsSlider isOpen={controlsOpen}>
        <VideoPlayerControlsIcons>
          <VideoPlayerControlsIcon>
            <PlayPauseIcon
              color={theme.palette.gray[80]}
              onClick={onClickPlayPauseIcon}
            />
          </VideoPlayerControlsIcon>
          {soundEnabled === true && (
            <VideoPlayerControlsIcon>
              <VideoPlayerSound
                soundPct={soundPct}
                onChangeSoundPct={onChangeSoundPct}
                onIsDraggingSound={onIsDraggingSound}
              />
            </VideoPlayerControlsIcon>
          )}
          {skipOptionsSorted && (
            <VideoPlayerControlsIcon>
              {skipOptionsSorted
                .slice()
                .reverse()
                .map((o: number) => (
                  <VideoPlayerSkip
                    key={o}
                    direction="backwards"
                    amount={o}
                    onSkip={onSkipClick}
                  />
                ))}
            </VideoPlayerControlsIcon>
          )}
          <VideoPlayerControlsIcon css={{ cursor: 'default' }}>
            <VideoPlayerTimer
              currentTime={progress?.playerCurrentTime}
              timezone={timezone}
              videoStartTime={videoStartTime}
              videoEndTime={videoEndTime}
              displayType={
                isLivePlayer
                  ? VideoPlayerTimerDisplayType.TimeOnly
                  : VideoPlayerTimerDisplayType.TimeWithDuration
              }
            />
          </VideoPlayerControlsIcon>
          {isLivePlayer && (
            <VideoPlayerControlsIcon>
              <VideoPlayerLiveButton
                isCurrentlyLive={isCurrentlyLive}
                onClickLive={onClickLive}
              />
            </VideoPlayerControlsIcon>
          )}
          {skipOptionsSorted && !isCurrentlyLive && (
            <VideoPlayerControlsIcon>
              {skipOptionsSorted.map((o: number) => (
                <VideoPlayerSkip
                  key={o}
                  direction="forwards"
                  amount={o}
                  onSkip={onSkipClick}
                />
              ))}
            </VideoPlayerControlsIcon>
          )}
          <VideoPlayerControlsIcon css={{ marginLeft: 'auto' }}>
            <VideoPlayerSpeedSelector
              currentSpeed={currentSpeed}
              speeds={filteredSpeeds}
              onChangeSpeed={onChangeSpeed}
            />
          </VideoPlayerControlsIcon>
          <VideoPlayerControlsIcon>
            <FullScreenIcon
              css={{ zIndex: ZIndex.MORE_ABOVE }}
              color={theme.palette.gray[80]}
              onClick={onClickFullScreenIcon}
            />
          </VideoPlayerControlsIcon>
        </VideoPlayerControlsIcons>
        <VideoPlayerProgressBar
          isCurrentlyLive={isCurrentlyLive}
          isLivePlayer={isLivePlayer}
          playlistStartTime={playlistStartTime}
          progress={progress}
          timezone={timezone}
          videoStartTime={videoStartTime}
          videoEndTime={videoEndTime}
          onChangeProgress={onChangeProgress}
          onIsDragging={onIsDragging}
        />
      </VideoPlayerControlsSlider>
    </VideoPlayerControlsContainer>
  )
}
