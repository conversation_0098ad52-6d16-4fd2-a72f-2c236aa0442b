import {
  VideoSkipControlMapping,
  VideoSkipDirection,
} from '../../organisms/VideoPlayer/types'

export const defaultVideoSkipControlMapping: VideoSkipControlMapping = {
  j: {
    direction: VideoSkipDirection.FORWARD,
    seconds: 1,
  },
  J: {
    direction: VideoSkipDirection.FORWARD,

    seconds: 10,
    label: 'shift + j',
  },
  h: {
    direction: VideoSkipDirection.FORWARD,
    seconds: 60,
  },
  H: {
    direction: VideoSkipDirection.FORWARD,
    seconds: 60 * 5,
    label: 'shift + h',
  },
  g: {
    direction: VideoSkipDirection.FORWARD,
    seconds: 60 * 10,
  },
  l: {
    direction: VideoSkipDirection.BACKWARD,
    seconds: 1,
  },
  L: {
    direction: VideoSkipDirection.BACKWARD,
    seconds: 10,
    label: 'shift + l',
  },
  ';': {
    direction: VideoSkipDirection.BACKWARD,
    seconds: 60,
  },
  ':': {
    direction: VideoSkipDirection.BACKWARD,
    seconds: 60 * 5,
  },
  "'": {
    direction: VideoSkipDirection.BACKWARD,
    seconds: 60 * 10,
  },
} as const
