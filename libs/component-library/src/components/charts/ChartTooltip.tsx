import React, { Fragment } from 'react'

import { useTheme } from '@emotion/react'

import { rem } from 'polished'
import {
  NameType,
  Payload,
  ValueType,
} from 'recharts/types/component/DefaultTooltipContent'

import { formatDatum, LONG_DATE_WITH_WEEKDAY, remSpacing } from '../../utils'
import { Cell, CellProps, Tile } from '../atoms'
import { FlexContainer } from '../layout'
import { ChartMeta, ChartTooltipProps } from './types'

interface TooltipDataElement {
  color?: string
  name?: NameType
  numberType?: 'auto' | 'percent'
  value?: ValueType
}

export const ChartTooltip = React.memo(function ChartTooltip({
  active,
  label,
  payload,
  timezone,
  sortedKeys,
  meta,
  children,
}: ChartTooltipProps): React.JSX.Element | null {
  const title = React.useMemo(
    () =>
      label
        ? formatDatum(label, undefined, timezone, LONG_DATE_WITH_WEEKDAY)
        : '',
    [label, timezone]
  )

  const theme = useTheme()

  if (active !== true || !payload) return null

  return (
    <Tile
      gutter={remSpacing.gutter}
      minWidth={rem('328px')}
      css={{
        display: 'flex',
        flexDirection: 'column',
        gap: remSpacing.gutter,
      }}
    >
      <Cell css={{ padding: 0 }}>
        <Cell.Title color={theme.palette.text.tertiary}>{title}</Cell.Title>
      </Cell>
      {children}
      <FlexContainer direction="column" gap={remSpacing.small}>
        <ToolTipDataCells
          payload={payload}
          sortedKeys={sortedKeys}
          timezone={timezone}
          meta={meta}
        />
      </FlexContainer>
    </Tile>
  )
})

/**
 * Transforms the raw, messy tooltip payload into a simple array of DataElement for displaying the data.
 * The `payload` input is an array which has an element per key that is displayed.
 * For the keys that are hidden from being displayed, additional transformation is needed. Each
 * `payload` item also has a nested `payload` with the raw { key : value } of all the keys, regardless
 * if they were hidden or not.
 * @param sortedKeys sorted keys to display
 * @param payload messy raw Recharts input
 * @param meta metadata which encompasses which keys are hidden
 */
const getTooltipData = (
  sortedKeys: string[] | undefined,
  payload: Payload<ValueType, NameType>[],
  meta: ChartMeta | undefined
): TooltipDataElement[] => {
  interface BarKeyToDataElement {
    [dataKey: string | number]: TooltipDataElement
  }

  if (!sortedKeys || !payload) return payload

  // Create a dictionary for the keys which are shown in the graph
  const payloadDict = payload.reduce<BarKeyToDataElement>(
    (acc, data) =>
      data.dataKey
        ? {
            ...acc,
            [data.dataKey]: {
              color: data.color,
              name: data.name,
              value: data.value,
              numberType:
                meta && meta[data.dataKey]
                  ? meta[data.dataKey].type
                  : undefined,
            },
          }
        : acc,
    {}
  )

  // Create a dictionary for the hidden keys. The first element in the payload will have all the
  // raw data for all the keys.
  const payloadDictForHiddenKeys = (() => {
    if (payload.length > 0) {
      const rawPayload = payload[0].payload
      return Object.keys(rawPayload)
        .filter((key: string) => meta && meta[key] && meta[key].hide)
        .reduce<BarKeyToDataElement>(
          (acc, key) => ({
            ...acc,
            [key]: {
              name: key,
              value: rawPayload[key],
              numberType: meta && meta[key] ? meta[key].type : undefined,
            },
          }),
          {}
        )
    }
    return {}
  })()

  return sortedKeys
    .map((key) => payloadDict[key] || payloadDictForHiddenKeys[key])
    .filter((data) => !!data)
}

interface ToolTipDataCellsProps {
  meta: ChartTooltipProps['meta']
  payload: Payload<ValueType, NameType>[]
  sortedKeys: ChartTooltipProps['sortedKeys']
  timezone: ChartTooltipProps['timezone']
}

const ToolTipDataCells = React.memo(function ToolTipDataCells({
  payload,
  sortedKeys,
  timezone,
  meta,
}: ToolTipDataCellsProps) {
  const sortedData = React.useMemo(
    () => getTooltipData(sortedKeys, payload, meta),
    [sortedKeys, payload, meta]
  )

  const theme = useTheme()

  return (
    <Fragment>
      {sortedData?.map(
        (data, index) =>
          data.name !== undefined && (
            <Fragment key={data.name}>
              {index > 0 && (
                <hr
                  css={{
                    width: '100%',
                    margin: 0,
                    border: `solid ${theme.palette.gray[20]}`,
                    borderWidth: `1px 0 0 0`,
                    borderRadius: 0,
                  }}
                />
              )}
              <ToolTipCell
                data={data}
                variant={'transparent'}
                timezone={timezone}
              />
            </Fragment>
          )
      )}
    </Fragment>
  )
})

const Rectangle = ({ color }: { color: string }) => (
  <span
    css={{
      height: remSpacing.medium,
      width: remSpacing.medium,
      background: color,
      display: 'inline-block',
      borderRadius: remSpacing.xxsmall,
    }}
  />
)

const ToolTipCell = React.memo(function ToolTipCell({
  data,
  variant,
  timezone,
}: {
  data: TooltipDataElement
  variant: CellProps['variant']
  timezone?: string
}) {
  const theme = useTheme()
  const formattedDatum = React.useMemo(
    () => formatDatum(data.value, data.numberType, timezone),
    [data.numberType, data.value, timezone]
  )

  return (
    <Cell variant={variant} css={{ padding: 0 }}>
      {data.color && (
        <Cell.IconLeft spacing={`${remSpacing.none}`}>
          <Rectangle color={data.color} />
        </Cell.IconLeft>
      )}
      <Cell.ToolTipLabel color={theme.palette.gray[60]}>
        {data.name}
      </Cell.ToolTipLabel>
      <Cell.ToolTipText color={theme.palette.gray[80]}>
        {formattedDatum}
      </Cell.ToolTipText>
    </Cell>
  )
})
